# Authorization Enhancement Deployment Guide

This guide provides comprehensive instructions for deploying the authorization enhancement feature to various environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Configuration](#configuration)
3. [Environment-Specific Setup](#environment-specific-setup)
4. [Database Considerations](#database-considerations)
5. [Redis Configuration](#redis-configuration)
6. [Deployment Steps](#deployment-steps)
7. [Verification](#verification)
8. [Monitoring](#monitoring)
9. [Troubleshooting](#troubleshooting)
10. [Rollback Procedures](#rollback-procedures)

## Prerequisites

### System Requirements

- Java 11 or higher
- Spring Boot 2.5+
- Redis 6.0+ (for caching)
- Database with existing CoreUser, CoreRole, CorePermission, and CoreRoleBusinessUnit tables
- Sufficient memory for JWT token caching (recommended: additional 512MB heap space)

### Database Schema

Ensure the following tables exist and have proper relationships:

```sql
-- Core tables required for authorization enhancement
CoreUser (username, firstName, lastName, email, readOnlyUser)
CoreRole (roleId, roleName)
CorePermission (permissionId, permissionName)
CoreUserRole (userId, roleId)
CoreRolePermission (roleId, permissionId)
CoreRoleBusinessUnit (roleId, businessUnitId)
BusinessUnit (businessUnitId, businessUnitName)
```

### Network Requirements

- Database connectivity from application servers
- Redis connectivity from application servers (if caching enabled)
- Outbound HTTPS access for Okta token validation

## Configuration

### Basic Configuration

Add the following to your `application.properties` or `application.yml`:

```properties
# Enable authorization enhancement
authorization.enabled=true

# Enable caching (recommended for production)
authorization.caching-enabled=true

# Cache configuration
authorization.cache.expiration-minutes=58
authorization.cache.max-size=10000

# Database timeouts
authorization.database.connection-timeout-seconds=30
authorization.database.query-timeout-seconds=15
authorization.database.max-retries=3

# JWT token configuration
authorization.jwt.include-roles=true
authorization.jwt.include-permissions=true
authorization.jwt.include-business-units=true
authorization.jwt.include-read-only-access=true
authorization.jwt.max-token-size-bytes=4096

# Monitoring
authorization.monitoring.metrics-enabled=true
authorization.monitoring.slow-query-threshold-ms=1000

# Fallback mechanisms
authorization.fallback.enable-cache-fallback=true
authorization.fallback.enable-empty-data-fallback=true
```

### Advanced Configuration

For production environments, consider these additional settings:

```properties
# Performance tuning
authorization.database.batch-processing-enabled=true
authorization.database.batch-size=100
authorization.cache.statistics-enabled=true

# Security limits
authorization.jwt.max-roles=50
authorization.jwt.max-permissions=100
authorization.jwt.max-business-units=20

# Circuit breaker
authorization.fallback.circuit-breaker-failure-threshold=5
authorization.fallback.circuit-breaker-timeout-seconds=60

# Health checks
authorization.monitoring.health-check-interval-seconds=60
```

## Environment-Specific Setup

### Development Environment

```properties
# Development settings
spring.profiles.active=dev

# Enable detailed logging for debugging
authorization.monitoring.detailed-logging-enabled=true
authorization.monitoring.slow-query-threshold-ms=500

# Smaller cache for development
authorization.cache.max-size=1000

# Logging levels
logging.level.com.recvueoktatoken.authorizer=DEBUG
logging.level.org.springframework.data.redis=DEBUG
```

### Staging Environment

```properties
# Staging settings
spring.profiles.active=staging

# Production-like settings but with more logging
authorization.monitoring.detailed-logging-enabled=false
authorization.monitoring.slow-query-threshold-ms=1000

# Medium cache size
authorization.cache.max-size=5000

# Health checks
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
```

### Production Environment

```properties
# Production settings
spring.profiles.active=prod

# Optimized for performance
authorization.monitoring.detailed-logging-enabled=false
authorization.monitoring.slow-query-threshold-ms=2000

# Large cache for production load
authorization.cache.max-size=50000

# Security
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=never

# Logging
logging.level.com.recvueoktatoken.authorizer=INFO
```

## Database Considerations

### Index Optimization

Create the following indexes for optimal performance:

```sql
-- Indexes for authorization queries
CREATE INDEX idx_core_user_username ON CoreUser(username);
CREATE INDEX idx_core_user_role_user_id ON CoreUserRole(userId);
CREATE INDEX idx_core_role_permission_role_id ON CoreRolePermission(roleId);
CREATE INDEX idx_core_role_business_unit_role_id ON CoreRoleBusinessUnit(roleId);

-- Composite indexes for complex queries
CREATE INDEX idx_user_role_composite ON CoreUserRole(userId, roleId);
CREATE INDEX idx_role_permission_composite ON CoreRolePermission(roleId, permissionId);
```

### Connection Pool Configuration

Configure your database connection pool appropriately:

```properties
# HikariCP configuration (recommended)
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.leak-detection-threshold=60000
```

## Redis Configuration

### Basic Redis Setup

```properties
# Redis connection
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.database=0
spring.redis.timeout=2000ms

# Connection pool
spring.redis.jedis.pool.max-active=8
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.min-idle=0
spring.redis.jedis.pool.max-wait=-1ms
```

### Redis Cluster Configuration

For production environments with Redis cluster:

```properties
# Redis cluster
spring.redis.cluster.nodes=redis1:6379,redis2:6379,redis3:6379
spring.redis.cluster.max-redirects=3
spring.redis.timeout=2000ms

# Connection pool for cluster
spring.redis.jedis.pool.max-active=16
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.min-idle=2
```

### Redis Security

```properties
# Redis authentication (if required)
spring.redis.password=your-redis-password

# SSL/TLS (if required)
spring.redis.ssl=true
```

## Deployment Steps

### Step 1: Pre-Deployment Verification

1. **Database Schema Check**
   ```sql
   -- Verify required tables exist
   SELECT table_name FROM information_schema.tables 
   WHERE table_name IN ('CoreUser', 'CoreRole', 'CorePermission', 'CoreUserRole', 'CoreRolePermission', 'CoreRoleBusinessUnit');
   ```

2. **Redis Connectivity Test**
   ```bash
   redis-cli -h your-redis-host -p 6379 ping
   ```

3. **Configuration Validation**
   ```bash
   # Validate configuration syntax
   java -jar your-app.jar --spring.config.location=classpath:/application.properties --spring.profiles.active=validation-only
   ```

### Step 2: Gradual Rollout

1. **Feature Flag Deployment**
   ```properties
   # Initial deployment with feature disabled
   authorization.enabled=false
   ```

2. **Enable for Testing**
   ```properties
   # Enable for specific test users or environments
   authorization.enabled=true
   authorization.monitoring.detailed-logging-enabled=true
   ```

3. **Production Rollout**
   ```properties
   # Full production enablement
   authorization.enabled=true
   authorization.monitoring.detailed-logging-enabled=false
   ```

### Step 3: Application Deployment

1. **Build Application**
   ```bash
   mvn clean package -DskipTests
   ```

2. **Deploy to Staging**
   ```bash
   # Copy artifact to staging servers
   scp target/recvueoktatoken-*.jar staging-server:/opt/app/
   
   # Start application with staging profile
   java -jar -Dspring.profiles.active=staging recvueoktatoken-*.jar
   ```

3. **Deploy to Production**
   ```bash
   # Blue-green deployment recommended
   # Deploy to inactive environment first
   java -jar -Dspring.profiles.active=prod recvueoktatoken-*.jar
   ```

## Verification

### Health Check Verification

```bash
# Check application health
curl http://localhost:8080/actuator/health

# Check authorization-specific health
curl http://localhost:8080/actuator/health/authorization
```

Expected response:
```json
{
  "status": "UP",
  "components": {
    "authorization": {
      "status": "UP",
      "details": {
        "authorizationEnabled": true,
        "databaseHealthy": true,
        "cacheHealthy": true,
        "serviceHealthy": true
      }
    }
  }
}
```

### Functional Verification

1. **JWT Token Verification**
   ```bash
   # Make authenticated request
   curl -H "Authorization: Bearer your-okta-token" \
        -H "X-Tenant-Identifier: your-tenant" \
        http://localhost:8080/api/v2.0/authorize
   ```

2. **Verify JWT Claims**
   Decode the returned JWT token and verify it contains:
   - `roles` array
   - `permissions` array
   - `businessUnits` array
   - `readOnlyAccess` boolean

3. **Cache Verification**
   ```bash
   # Check Redis for cached data
   redis-cli -h your-redis-host keys "auth_context:*"
   redis-cli -h your-redis-host keys "user_token:*"
   ```

### Performance Verification

1. **Response Time Check**
   ```bash
   # Measure response times
   curl -w "@curl-format.txt" -H "Authorization: Bearer token" \
        http://localhost:8080/api/v2.0/authorize
   ```

2. **Cache Hit Rate**
   ```bash
   # Check cache statistics
   curl http://localhost:8080/actuator/metrics/cache.gets
   ```

## Monitoring

### Key Metrics to Monitor

1. **Authorization Service Metrics**
   - Authorization context retrieval time
   - Cache hit/miss ratio
   - Database query execution time
   - JWT token generation time

2. **System Metrics**
   - Memory usage (heap and non-heap)
   - Database connection pool utilization
   - Redis connection pool utilization
   - Application response times

3. **Error Metrics**
   - Authorization service failures
   - Database connectivity issues
   - Cache connectivity issues
   - JWT token size warnings

### Monitoring Setup

```properties
# Enable metrics endpoints
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true

# Custom metrics
authorization.monitoring.metrics-enabled=true
authorization.monitoring.cache-metrics-enabled=true
authorization.monitoring.token-size-monitoring-enabled=true
```

### Alerting Rules

Set up alerts for:

- Authorization service health check failures
- Cache hit ratio below 80%
- Database query times above 2 seconds
- JWT token size above 3KB
- Memory usage above 85%

## Troubleshooting

### Common Issues

1. **Authorization Service Not Starting**
   ```
   Error: Could not create bean 'authorizationService'
   ```
   - Check database connectivity
   - Verify Redis connectivity (if caching enabled)
   - Check configuration properties

2. **Empty Authorization Data**
   ```
   JWT tokens contain empty roles/permissions arrays
   ```
   - Verify database schema and data
   - Check user-role associations
   - Review authorization service logs

3. **Cache Issues**
   ```
   Cache operations failing
   ```
   - Check Redis connectivity
   - Verify Redis configuration
   - Check Redis memory usage

4. **Performance Issues**
   ```
   Slow response times
   ```
   - Check database query performance
   - Verify cache hit rates
   - Review database indexes

### Debug Mode

Enable debug logging:

```properties
logging.level.com.recvueoktatoken.authorizer=DEBUG
authorization.monitoring.detailed-logging-enabled=true
```

### Log Analysis

Key log patterns to look for:

```
# Successful authorization context retrieval
INFO  AuthorizationService - Retrieved authorization context for user: username

# Cache hit
DEBUG AuthorizationService - Cache hit for user: username

# Cache miss
DEBUG AuthorizationService - Cache miss for user: username, querying database

# Database query performance
WARN  AuthorizationService - Slow query detected: 1500ms for user: username

# Fallback activation
WARN  AuthorizationService - Database unavailable, using cached data for user: username
```

## Rollback Procedures

### Emergency Rollback

If issues occur, immediately disable the feature:

```properties
# Disable authorization enhancement
authorization.enabled=false
```

This will:
- Stop including authorization claims in JWT tokens
- Revert to basic user information only
- Maintain backward compatibility

### Gradual Rollback

1. **Reduce Cache Size**
   ```properties
   authorization.cache.max-size=1000
   ```

2. **Disable Specific Features**
   ```properties
   authorization.jwt.include-roles=false
   authorization.jwt.include-permissions=false
   authorization.jwt.include-business-units=false
   ```

3. **Increase Timeouts**
   ```properties
   authorization.database.query-timeout-seconds=30
   authorization.database.connection-timeout-seconds=60
   ```

### Full Rollback

1. **Application Rollback**
   ```bash
   # Deploy previous version
   java -jar previous-version.jar
   ```

2. **Configuration Cleanup**
   ```bash
   # Remove authorization configuration
   # Restart with previous configuration
   ```

3. **Cache Cleanup**
   ```bash
   # Clear authorization cache data
   redis-cli -h your-redis-host --scan --pattern "auth_context:*" | xargs redis-cli -h your-redis-host del
   redis-cli -h your-redis-host --scan --pattern "user_token:*" | xargs redis-cli -h your-redis-host del
   ```

## Support and Maintenance

### Regular Maintenance Tasks

1. **Weekly**
   - Review authorization service metrics
   - Check cache hit rates
   - Monitor JWT token sizes

2. **Monthly**
   - Review database query performance
   - Analyze authorization data growth
   - Update configuration if needed

3. **Quarterly**
   - Review and optimize database indexes
   - Evaluate cache size requirements
   - Performance testing and optimization

### Contact Information

For deployment support:
- Development Team: <EMAIL>
- Operations Team: <EMAIL>
- Database Team: <EMAIL>

### Documentation Updates

This deployment guide should be updated when:
- Configuration options change
- New features are added
- Performance optimizations are implemented
- Issues and solutions are discovered