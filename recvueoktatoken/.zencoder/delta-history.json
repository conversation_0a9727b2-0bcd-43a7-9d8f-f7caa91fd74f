{"snapshots": {"/Users/<USER>/workspace/RECVUE_MS_Security/recvueoktatoken/src/main/java/com/recvueoktatoken/authorizer/health/AuthorizationHealthIndicator.java": {"filePath": "/Users/<USER>/workspace/RECVUE_MS_Security/recvueoktatoken/src/main/java/com/recvueoktatoken/authorizer/health/AuthorizationHealthIndicator.java", "baseContent": "package com.recvueoktatoken.authorizer.health;\n\nimport com.recvueoktatoken.authorizer.config.AuthorizationProperties;\nimport com.recvueoktatoken.authorizer.service.AuthorizationService;\nimport com.recvueoktatoken.coreuser.dao.CoreUserDao;\nimport org.slf4j.Logger;\nimport org.slf4j.LoggerFactory;\nimport org.springframework.boot.actuate.health.Health;\nimport org.springframework.boot.actuator.health.HealthIndicator;\nimport org.springframework.data.redis.core.RedisTemplate;\nimport org.springframework.stereotype.Component;\n\nimport java.util.HashMap;\nimport java.util.Map;\n\n/**\n * Health indicator for authorization service functionality\n */\n@Component\npublic class AuthorizationHealthIndicator implements HealthIndicator {\n\n    private static final Logger logger = LoggerFactory.getLogger(AuthorizationHealthIndicator.class);\n\n    private final AuthorizationProperties authorizationProperties;\n    private final AuthorizationService authorizationService;\n    private final CoreUserDao coreUserDao;\n    private final RedisTemplate<String, Object> redisTemplate;\n\n    public AuthorizationHealthIndicator(\n            AuthorizationProperties authorizationProperties,\n            AuthorizationService authorizationService,\n            CoreUserDao coreUserDao,\n            RedisTemplate<String, Object> redisTemplate) {\n        this.authorizationProperties = authorizationProperties;\n        this.authorizationService = authorizationService;\n        this.coreUserDao = coreUserDao;\n        this.redisTemplate = redisTemplate;\n    }\n\n    @Override\n    public Health health() {\n        try {\n            Map<String, Object> details = new HashMap<>();\n            boolean overallHealthy = true;\n\n            // Check if authorization enhancement is enabled\n            details.put(\"authorizationEnabled\", authorizationProperties.isEnabled());\n            \n            if (!authorizationProperties.isEnabled()) {\n                details.put(\"status\", \"Authorization enhancement is disabled\");\n                return Health.up().withDetails(details).build();\n            }\n\n            // Check database connectivity\n            boolean databaseHealthy = checkDatabaseHealth();\n            details.put(\"databaseHealthy\", databaseHealthy);\n            if (!databaseHealthy) {\n                overallHealthy = false;\n            }\n\n            // Check Redis cache connectivity (if caching is enabled)\n            if (authorizationProperties.isCachingEnabled()) {\n                boolean cacheHealthy = checkCacheHealth();\n                details.put(\"cacheHealthy\", cacheHealthy);\n                details.put(\"cachingEnabled\", true);\n                if (!cacheHealthy) {\n                    overallHealthy = false;\n                }\n            } else {\n                details.put(\"cachingEnabled\", false);\n                details.put(\"cacheHealthy\", \"N/A - Caching disabled\");\n            }\n\n            // Check authorization service functionality\n            boolean serviceHealthy = checkAuthorizationServiceHealth();\n            details.put(\"serviceHealthy\", serviceHealthy);\n            if (!serviceHealthy) {\n                overallHealthy = false;\n            }\n\n            // Add configuration details\n            addConfigurationDetails(details);\n\n            // Add performance metrics if available\n            addPerformanceMetrics(details);\n\n            if (overallHealthy) {\n                return Health.up().withDetails(details).build();\n            } else {\n                return Health.down().withDetails(details).build();\n            }\n\n        } catch (Exception e) {\n            logger.error(\"Error checking authorization service health\", e);\n            return Health.down()\n                    .withException(e)\n                    .withDetail(\"error\", \"Failed to check authorization service health\")\n                    .build();\n        }\n    }\n\n    private boolean checkDatabaseHealth() {\n        try {\n            // Test database connectivity by attempting a simple query\n            // Using a lightweight query that should work regardless of data\n            coreUserDao.getUserByUsername(\"__health_check_user__\");\n            return true;\n        } catch (Exception e) {\n            logger.warn(\"Database health check failed\", e);\n            return false;\n        }\n    }\n\n    private boolean checkCacheHealth() {\n        try {\n            // Test Redis connectivity\n            String testKey = \"auth_health_check\";\n            String testValue = \"healthy\";\n            \n            redisTemplate.opsForValue().set(testKey, testValue);\n            Object retrievedValue = redisTemplate.opsForValue().get(testKey);\n            redisTemplate.delete(testKey);\n            \n            return testValue.equals(retrievedValue);\n        } catch (Exception e) {\n            logger.warn(\"Cache health check failed\", e);\n            return false;\n        }\n    }\n\n    private boolean checkAuthorizationServiceHealth() {\n        try {\n            // Test authorization service by attempting to get context for a test user\n            // This should not throw an exception even if the user doesn't exist\n            authorizationService.getUserAuthorizationContext(\"__health_check_user__\");\n            return true;\n        } catch (Exception e) {\n            logger.warn(\"Authorization service health check failed\", e);\n            return false;\n        }\n    }\n\n    private void addConfigurationDetails(Map<String, Object> details) {\n        Map<String, Object> config = new HashMap<>();\n        \n        config.put(\"cacheExpirationMinutes\", authorizationProperties.getCache().getExpirationMinutes());\n        config.put(\"maxCacheSize\", authorizationProperties.getCache().getMaxSize());\n        config.put(\"databaseMaxRetries\", authorizationProperties.getDatabase().getMaxRetries());\n        config.put(\"queryTimeoutSeconds\", authorizationProperties.getDatabase().getQueryTimeoutSeconds());\n        \n        Map<String, Object> jwtConfig = new HashMap<>();\n        jwtConfig.put(\"includeRoles\", authorizationProperties.getJwt().isIncludeRoles());\n        jwtConfig.put(\"includePermissions\", authorizationProperties.getJwt().isIncludePermissions());\n        jwtConfig.put(\"includeBusinessUnits\", authorizationProperties.getJwt().isIncludeBusinessUnits());\n        jwtConfig.put(\"includeReadOnlyAccess\", authorizationProperties.getJwt().isIncludeReadOnlyAccess());\n        jwtConfig.put(\"maxTokenSizeBytes\", authorizationProperties.getJwt().getMaxTokenSizeBytes());\n        config.put(\"jwt\", jwtConfig);\n        \n        Map<String, Object> fallbackConfig = new HashMap<>();\n        fallbackConfig.put(\"cacheFallbackEnabled\", authorizationProperties.getFallback().isEnableCacheFallback());\n        fallbackConfig.put(\"emptyDataFallbackEnabled\", authorizationProperties.getFallback().isEnableEmptyDataFallback());\n        fallbackConfig.put(\"maxCacheAgeMinutes\", authorizationProperties.getFallback().getMaxCacheAgeMinutes());\n        config.put(\"fallback\", fallbackConfig);\n        \n        details.put(\"configuration\", config);\n    }\n\n    private void addPerformanceMetrics(Map<String, Object> details) {\n        try {\n            Map<String, Object> metrics = new HashMap<>();\n            \n            // Add basic JVM metrics\n            Runtime runtime = Runtime.getRuntime();\n            metrics.put(\"totalMemoryMB\", runtime.totalMemory() / (1024 * 1024));\n            metrics.put(\"freeMemoryMB\", runtime.freeMemory() / (1024 * 1024));\n            metrics.put(\"usedMemoryMB\", (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024));\n            \n            // Add timestamp for when health check was performed\n            metrics.put(\"lastHealthCheckTimestamp\", System.currentTimeMillis());\n            metrics.put(\"lastHealthCheckTime\", new java.util.Date().toString());\n            \n            details.put(\"metrics\", metrics);\n        } catch (Exception e) {\n            logger.debug(\"Could not add performance metrics to health check\", e);\n            details.put(\"metrics\", \"Unable to collect metrics\");\n        }\n    }\n}", "baseTimestamp": 1756624207341, "deltas": [{"timestamp": 1756624211318, "changes": [{"type": "MODIFY", "lineNumber": 8, "content": "import org.springframework.boot.actuate.health.HealthIndicator;", "oldContent": "import org.springframework.boot.actuator.health.HealthIndicator;"}]}]}, "/Users/<USER>/workspace/RECVUE_MS_Security/recvueoktatoken/src/main/java/com/recvueoktatoken/authorizer/request/JWTUtils.java": {"filePath": "/Users/<USER>/workspace/RECVUE_MS_Security/recvueoktatoken/src/main/java/com/recvueoktatoken/authorizer/request/JWTUtils.java", "baseContent": "/*\n * To change this license header, choose License Headers in Project Properties.\n * To change this template file, choose Tools | Templates\n * and open the template in the editor.\n */\npackage com.recvueoktatoken.authorizer.request;\n\nimport io.jsonwebtoken.JwtBuilder;\nimport io.jsonwebtoken.Jwts;\nimport io.jsonwebtoken.SignatureAlgorithm;\nimport java.io.FileNotFoundException;\nimport java.io.IOException;\nimport java.security.Key;\nimport java.security.KeyFactory;\nimport java.security.NoSuchAlgorithmException;\nimport java.security.PrivateKey;\nimport java.security.Security;\nimport java.security.spec.InvalidKeySpecException;\nimport java.security.spec.PKCS8EncodedKeySpec;\nimport java.util.Date;\nimport java.util.Map;\nimport javax.crypto.spec.SecretKeySpec;\nimport javax.xml.bind.DatatypeCo;\nimport org.slf4j.Logger;\nimport org.slf4j.LoggerFactory;\nimport org.springframework.beans.factory.annotation.Autowired;\nimport org.springframework.stereotype.Component;\nimport org.bouncycastle.jce.provider.BouncyCastleProvider;\n\nimport com.recvueoktatoken.authorizer.monitoring.AuthorizationMetrics;\n\n@Component\npublic class JWTUtils {\n\n\tprivate final Logger LOGGER = LoggerFactory.getLogger(this.getClass());\n\tprivate static PrivateKey priv = null;\n\n\t@Autowired(required = false)\n\tprivate AuthorizationMetrics authorizationMetrics;\n\n\t/**\n\t *\n\t * @param id\n\t * @param issuer\n\t * @param subject\n\t * @param ttlMillis\n\t * @return\n\t */\n\tpublic String createJWT(String id, String issuer, String subject, long ttlMillis) {\n\n\t\t// The JWT signature algorithm we will be using to sign the token\n\t\tSignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;\n\n\t\tlong nowMillis = System.currentTimeMillis();\n\t\tDate now = new Date(nowMillis);\n\n\t\t// We will sign our JWT with our ApiKey secret\n\t\t// byte[] apiKeySecretBytes =\n\t\t// DatatypeConverter.parseBase64Binary(apiKey.getSecret());\n\t\tbyte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(\"secret\");\n\t\tKey signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());\n\n\t\t// Let's set the JWT Claims\n\t\tJwtBuilder builder = Jwts.builder().setId(id).setIssuedAt(now).setSubject(subject).setIssuer(issuer)\n\t\t\t\t.signWith(signatureAlgorithm, signingKey);\n\n\t\t// if it has been specified, let's add the expiration\n\t\tif (ttlMillis >= 0) {\n\t\t\tlong expMillis = nowMillis + ttlMillis;\n\t\t\tDate exp = new Date(expMillis);\n\t\t\tbuilder.setExpiration(exp);\n\t\t}\n\n\t\t// Builds the JWT and serializes it to a compact, URL-safe string\n\t\treturn builder.compact();\n\t}\n\n\t/**\n\t * generates JWT token with given HashMap parameter value\n\t * \n\t * @param claims\n\t * @return\n\t * @throws java.security.NoSuchAlgorithmException\n\t * @throws java.security.spec.InvalidKeySpecException\n\t * @throws java.io.IOException\n\t */\n\tpublic String generateJwt(Map<String, Object> claims)\n\t\t\tthrows NoSuchAlgorithmException, InvalidKeySpecException, IOException {\n\t\t\n\t\t// Log authorization claims for debugging\n\t\tlogAuthorizationClaims(claims);\n\t\t\n\t\t// Validate authorization claims structure\n\t\tvalidateAuthorizationClaims(claims);\n\t\t\n\t\tif (priv == null) {\n\t\t\tSecurity.addProvider(new BouncyCastleProvider());\n\t\t\tKeyFactory factory = KeyFactory.getInstance(\"RSA\");\n\t\t\t// PrivateKey\n\t\t\tpriv = generatePrivateKey(factory, Constants.KEY_FILES_PATH + Constants.privateKeyFileName);\n\t\t\tLOGGER.debug(\"generateJwt -PrivateKey created\");\n\t\t}\n\t\t\n\t\ttry {\n\t\t\tString jws = Jwts.builder().setClaims(claims).signWith(SignatureAlgorithm.RS256, priv).compact();\n\n\t\t\tLOGGER.debug(\"generateJwt - JWT token generated successfully with {} claims\", claims.size());\n\t\t\tif (LOGGER.isTraceEnabled()) {\n\t\t\t\tLOGGER.trace(\"generateJwt - jws : \" + jws);\n\t\t\t}\n\n\t\t\t// Record JWT generation metrics\n\t\t\tif (authorizationMetrics != null) {\n\t\t\t\tauthorizationMetrics.recordJwtGeneration(jws.length());\n\t\t\t}\n\n\t\t\treturn jws;\n\t\t\t\n\t\t} catch (Exception e) {\n\t\t\tif (authorizationMetrics != null) {\n\t\t\t\tauthorizationMetrics.recordJwtGenerationError();\n\t\t\t}\n\t\t\tthrow e;\n\t\t}\n\t}\n\n\t/**\n\t * Reads privatekey File and generates keyFactor\n\t * \n\t * @param factory\n\t * @param filename\n\t * @return\n\t * @throws InvalidKeySpecException\n\t * @throws FileNotFoundException\n\t * @throws IOException\n\t */\n\tprivate static PrivateKey generatePrivateKey(KeyFactory factory, String filename)\n\t\t\tthrows InvalidKeySpecException, FileNotFoundException, IOException {\n\t\tPemFile pemFile = new PemFile(filename);\n\t\tbyte[] content = pemFile.getPemObject().getContent();\n\t\tPKCS8EncodedKeySpec privKeySpec = new PKCS8EncodedKeySpec(content);\n\t\treturn factory.generatePrivate(privKeySpec);\n\t}\n\n\t/**\n\t * Logs authorization claims for debugging purposes\n\t * \n\t * @param claims The claims map to log\n\t */\n\tprivate void logAuthorizationClaims(Map<String, Object> claims) {\n\t\tif (claims == null || !LOGGER.isDebugEnabled()) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\t// Log authorization-specific claims\n\t\t\tif (claims.containsKey(Constants.rolesMap)) {\n\t\t\t\tObject roles = claims.get(Constants.rolesMap);\n\t\t\t\tif (roles instanceof java.util.List) {\n\t\t\t\t\t@SuppressWarnings(\"unchecked\")\n\t\t\t\t\tjava.util.List<String> rolesList = (java.util.List<String>) roles;\n\t\t\t\t\tLOGGER.debug(\"JWT generation - Roles claim: {} roles\", rolesList.size());\n\t\t\t\t\tif (LOGGER.isTraceEnabled()) {\n\t\t\t\t\t\tLOGGER.trace(\"JWT generation - Roles: {}\", rolesList);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (claims.containsKey(Constants.permissionsMap)) {\n\t\t\t\tObject permissions = claims.get(Constants.permissionsMap);\n\t\t\t\tif (permissions instanceof java.util.List) {\n\t\t\t\t\t@SuppressWarnings(\"unchecked\")\n\t\t\t\t\tjava.util.List<String> permissionsList = (java.util.List<String>) permissions;\n\t\t\t\t\tLOGGER.debug(\"JWT generation - Permissions claim: {} permissions\", permissionsList.size());\n\t\t\t\t\tif (LOGGER.isTraceEnabled()) {\n\t\t\t\t\t\tLOGGER.trace(\"JWT generation - Permissions: {}\", permissionsList);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (claims.containsKey(Constants.businessUnitsMap)) {\n\t\t\t\tObject businessUnits = claims.get(Constants.businessUnitsMap);\n\t\t\t\tif (businessUnits instanceof java.util.List) {\n\t\t\t\t\t@SuppressWarnings(\"unchecked\")\n\t\t\t\t\tjava.util.List<?> businessUnitsList = (java.util.List<?>) businessUnits;\n\t\t\t\t\tLOGGER.debug(\"JWT generation - Business Units claim: {} business units\", businessUnitsList.size());\n\t\t\t\t\tif (LOGGER.isTraceEnabled()) {\n\t\t\t\t\t\tLOGGER.trace(\"JWT generation - Business Units: {}\", businessUnitsList);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (claims.containsKey(Constants.readOnlyAccessMap)) {\n\t\t\t\tObject readOnlyAccess = claims.get(Constants.readOnlyAccessMap);\n\t\t\t\tLOGGER.debug(\"JWT generation - Read-only Access claim: {}\", readOnlyAccess);\n\t\t\t}\n\n\t\t\t// Log standard claims\n\t\t\tif (claims.containsKey(Constants.userMap)) {\n\t\t\t\tLOGGER.debug(\"JWT generation - User claim present\");\n\t\t\t}\n\n\t\t\tif (claims.containsKey(Constants.tenantIdentiferMap)) {\n\t\t\t\tLOGGER.debug(\"JWT generation - Tenant identifier claim: {}\", claims.get(Constants.tenantIdentiferMap));\n\t\t\t}\n\n\t\t} catch (Exception e) {\n\t\t\tLOGGER.warn(\"Error logging authorization claims: {}\", e.getMessage());\n\t\t}\n\t}\n\n\t/**\n\t * Validates authorization claims structure to ensure JWT compatibility\n\t * \n\t * @param claims The claims map to validate\n\t */\n\tprivate void validateAuthorizationClaims(Map<String, Object> claims) {\n\t\tif (claims == null) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\t// Validate roles claim\n\t\t\tif (claims.containsKey(Constants.rolesMap)) {\n\t\t\t\tObject roles = claims.get(Constants.rolesMap);\n\t\t\t\tif (roles != null && !(roles instanceof java.util.List)) {\n\t\t\t\t\tLOGGER.warn(\"JWT generation - Roles claim is not a List: {}\", roles.getClass().getSimpleName());\n\t\t\t\t} else if (roles instanceof java.util.List) {\n\t\t\t\t\t@SuppressWarnings(\"unchecked\")\n\t\t\t\t\tjava.util.List<String> rolesList = (java.util.List<String>) roles;\n\t\t\t\t\tif (rolesList.size() > 50) { // Reasonable limit\n\t\t\t\t\t\tLOGGER.warn(\"JWT generation - Large number of roles ({}), may cause token size issues\", rolesList.size());\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Validate permissions claim\n\t\t\tif (claims.containsKey(Constants.permissionsMap)) {\n\t\t\t\tObject permissions = claims.get(Constants.permissionsMap);\n\t\t\t\tif (permissions != null && !(permissions instanceof java.util.List)) {\n\t\t\t\t\tLOGGER.warn(\"JWT generation - Permissions claim is not a List: {}\", permissions.getClass().getSimpleName());\n\t\t\t\t} else if (permissions instanceof java.util.List) {\n\t\t\t\t\t@SuppressWarnings(\"unchecked\")\n\t\t\t\t\tjava.util.List<String> permissionsList = (java.util.List<String>) permissions;\n\t\t\t\t\tif (permissionsList.size() > 100) { // Reasonable limit\n\t\t\t\t\t\tLOGGER.warn(\"JWT generation - Large number of permissions ({}), may cause token size issues\", permissionsList.size());\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Validate business units claim\n\t\t\tif (claims.containsKey(Constants.businessUnitsMap)) {\n\t\t\t\tObject businessUnits = claims.get(Constants.businessUnitsMap);\n\t\t\t\tif (businessUnits != null && !(businessUnits instanceof java.util.List)) {\n\t\t\t\t\tLOGGER.warn(\"JWT generation - Business Units claim is not a List: {}\", businessUnits.getClass().getSimpleName());\n\t\t\t\t} else if (businessUnits instanceof java.util.List) {\n\t\t\t\t\t@SuppressWarnings(\"unchecked\")\n\t\t\t\t\tjava.util.List<?> businessUnitsList = (java.util.List<?>) businessUnits;\n\t\t\t\t\tif (businessUnitsList.size() > 20) { // Reasonable limit\n\t\t\t\t\t\tLOGGER.warn(\"JWT generation - Large number of business units ({}), may cause token size issues\", businessUnitsList.size());\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Validate read-only access claim\n\t\t\tif (claims.containsKey(Constants.readOnlyAccessMap)) {\n\t\t\t\tObject readOnlyAccess = claims.get(Constants.readOnlyAccessMap);\n\t\t\t\tif (readOnlyAccess != null && !(readOnlyAccess instanceof Boolean)) {\n\t\t\t\t\tLOGGER.warn(\"JWT generation - Read-only Access claim is not a Boolean: {}\", readOnlyAccess.getClass().getSimpleName());\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Check overall token size estimate\n\t\t\tint estimatedClaimsCount = claims.size();\n\t\t\tif (estimatedClaimsCount > 20) {\n\t\t\t\tLOGGER.warn(\"JWT generation - Large number of claims ({}), may cause token size issues\", estimatedClaimsCount);\n\t\t\t}\n\n\t\t} catch (Exception e) {\n\t\t\tLOGGER.warn(\"Error validating authorization claims: {}\", e.getMessage());\n\t\t}\n\t}\n}\n", "baseTimestamp": 1756624243636, "deltas": [{"timestamp": 1756624246221, "changes": [{"type": "DELETE", "lineNumber": 22, "oldContent": "import javax.xml.bind.DatatypeCo;"}]}, {"timestamp": 1756624260087, "changes": [{"type": "INSERT", "lineNumber": 22, "content": "import javax.xml.bind.DatatypeCo;"}]}]}}}