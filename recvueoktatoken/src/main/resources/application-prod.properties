# Production Environment Configuration
# This configuration is optimized for production performance and security

# =============================================================================
# AUTHORIZATION ENHANCEMENT - PRODUCTION SETTINGS
# =============================================================================

# Enable authorization enhancement for production
authorization.enabled=true
authorization.caching-enabled=true

# =============================================================================
# FEATURE FLAGS - PRODUCTION
# =============================================================================

# Enable all JWT claims for production
feature.authorization.enabled=true
feature.authorization.roles-enabled=true
feature.authorization.permissions-enabled=true
feature.authorization.business-units-enabled=true
feature.authorization.read-only-access-enabled=true

# Disable detailed logging for production performance
feature.authorization.detailed-logging-enabled=false
feature.authorization.metrics-enabled=true
feature.authorization.cache-statistics-enabled=true

# =============================================================================
# CACHE CONFIGURATION - PRODUCTION
# =============================================================================

# Large cache for production load
authorization.cache.expiration-minutes=58
authorization.cache.max-size=50000
authorization.cache.statistics-enabled=true

# =============================================================================
# DATABASE CONFIGURATION - PRODUCTION
# =============================================================================

# Production database timeouts
authorization.database.connection-timeout-seconds=30
authorization.database.query-timeout-seconds=15
authorization.database.max-retries=3
authorization.database.retry-delay-ms=1000

# Enable batch processing for performance
authorization.database.batch-processing-enabled=true
authorization.database.batch-size=100

# =============================================================================
# JWT CONFIGURATION - PRODUCTION
# =============================================================================

# Include all claims in production
authorization.jwt.include-roles=true
authorization.jwt.include-permissions=true
authorization.jwt.include-business-units=true
authorization.jwt.include-read-only-access=true

# Production limits to prevent token bloat
authorization.jwt.max-token-size-bytes=4096
authorization.jwt.max-roles=50
authorization.jwt.max-permissions=100
authorization.jwt.max-business-units=20

# =============================================================================
# MONITORING - PRODUCTION
# =============================================================================

# Enable monitoring but disable detailed logging
authorization.monitoring.metrics-enabled=true
authorization.monitoring.detailed-logging-enabled=false
authorization.monitoring.slow-query-threshold-ms=2000
authorization.monitoring.cache-metrics-enabled=true
authorization.monitoring.token-size-monitoring-enabled=true
authorization.monitoring.health-check-interval-seconds=60

# =============================================================================
# FALLBACK CONFIGURATION - PRODUCTION
# =============================================================================

# Enable robust fallback mechanisms
authorization.fallback.enable-cache-fallback=true
authorization.fallback.enable-empty-data-fallback=true
authorization.fallback.max-cache-age-minutes=120
authorization.fallback.circuit-breaker-failure-threshold=5
authorization.fallback.circuit-breaker-timeout-seconds=60

# =============================================================================
# REDIS CONFIGURATION - PRODUCTION
# =============================================================================

# Production Redis cluster configuration
# Update these values for your production Redis setup
spring.redis.cluster.nodes=${REDIS_CLUSTER_NODES:redis1:6379,redis2:6379,redis3:6379}
spring.redis.cluster.max-redirects=3
spring.redis.timeout=2000ms
spring.redis.password=${REDIS_PASSWORD:}

# Production connection pool
spring.redis.jedis.pool.max-active=16
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.min-idle=2
spring.redis.jedis.pool.max-wait=2000ms

# SSL/TLS configuration (uncomment if using SSL)
#spring.redis.ssl=true

# =============================================================================
# DATABASE CONFIGURATION - PRODUCTION
# =============================================================================

# Production database connection
spring.datasource.url=${DATABASE_URL}
spring.datasource.username=${DATABASE_USERNAME}
spring.datasource.password=${DATABASE_PASSWORD}
spring.datasource.driver-class-name=${DATABASE_DRIVER:com.mysql.cj.jdbc.Driver}

# Production HikariCP settings
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.leak-detection-threshold=60000

# Connection validation
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.validation-timeout=5000

# =============================================================================
# LOGGING CONFIGURATION - PRODUCTION
# =============================================================================

# Production logging levels
logging.level.com.recvueoktatoken.authorizer=INFO
logging.level.com.recvueoktatoken.authorizer.service=INFO
logging.level.com.recvueoktatoken.authorizer.request=INFO
logging.level.com.recvueoktatoken.coreuser.dao=INFO

# Reduce noise from other components
logging.level.org.springframework.data.redis=WARN
logging.level.org.hibernate=WARN
logging.level.org.apache.http=WARN

# Log to file in production
logging.file.name=/var/log/recvueoktatoken/application.log
logging.file.max-size=100MB
logging.file.max-history=30
logging.file.total-size-cap=1GB

# Log pattern for production
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n

# =============================================================================
# ACTUATOR CONFIGURATION - PRODUCTION
# =============================================================================

# Limit actuator endpoints for security
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=never
management.endpoint.health.show-components=false

# Security for actuator endpoints
management.endpoints.web.base-path=/actuator
management.server.port=8081

# Metrics configuration
management.metrics.export.prometheus.enabled=true
management.metrics.distribution.percentiles-histogram.http.server.requests=true
management.metrics.tags.application=recvueoktatoken
management.metrics.tags.environment=production

# =============================================================================
# SECURITY CONFIGURATION - PRODUCTION
# =============================================================================

# Server configuration
server.port=8080
server.servlet.context-path=/

# SSL/TLS configuration (uncomment if using HTTPS)
#server.ssl.enabled=true
#server.ssl.key-store=${SSL_KEYSTORE_PATH}
#server.ssl.key-store-password=${SSL_KEYSTORE_PASSWORD}
#server.ssl.key-store-type=PKCS12

# Security headers
server.servlet.session.cookie.secure=true
server.servlet.session.cookie.http-only=true

# =============================================================================
# JVM CONFIGURATION - PRODUCTION
# =============================================================================

# JVM memory settings (set via environment variables or startup scripts)
# -Xms2g -Xmx4g
# -XX:+UseG1GC
# -XX:MaxGCPauseMillis=200
# -XX:+HeapDumpOnOutOfMemoryError
# -XX:HeapDumpPath=/var/log/recvueoktatoken/

# =============================================================================
# APPLICATION CONFIGURATION - PRODUCTION
# =============================================================================

# Spring Boot production settings
spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=none

# Connection pool monitoring
spring.jpa.properties.hibernate.generate_statistics=false

# Disable banner for cleaner logs
spring.main.banner-mode=off

# =============================================================================
# EXTERNAL SERVICES - PRODUCTION
# =============================================================================

# Okta configuration
okta.oauth2.issuer=${OKTA_ISSUER_URL}
okta.oauth2.client-id=${OKTA_CLIENT_ID}
okta.oauth2.client-secret=${OKTA_CLIENT_SECRET}

# =============================================================================
# ENVIRONMENT VARIABLES REFERENCE
# =============================================================================

# Required environment variables for production:
# - DATABASE_URL: JDBC URL for the database
# - DATABASE_USERNAME: Database username
# - DATABASE_PASSWORD: Database password
# - REDIS_CLUSTER_NODES: Redis cluster nodes (comma-separated)
# - REDIS_PASSWORD: Redis password (if authentication enabled)
# - OKTA_ISSUER_URL: Okta issuer URL
# - OKTA_CLIENT_ID: Okta client ID
# - OKTA_CLIENT_SECRET: Okta client secret

# Optional environment variables:
# - DATABASE_DRIVER: Database driver class name
# - SSL_KEYSTORE_PATH: Path to SSL keystore
# - SSL_KEYSTORE_PASSWORD: SSL keystore password