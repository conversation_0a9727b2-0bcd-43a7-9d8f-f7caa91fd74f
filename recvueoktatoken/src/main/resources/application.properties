springfox.documentation.swagger.v2.path=/api-docs

app.name = recvue
recvue.okta.url=https://recvue.okta.com
recvue.okta.userUrl=/api/v1/authn
recvue.okta.user=/api/v1/users
recvue.okta.apiKey =******************************************
recvue.okta.clientId =0oa839dtwL8XoTvbF2p6

jwt.header=X-Forwarded-User
jwt.publicKey.path=/rsatest.pub.pem
jwt.algorithm = RSA
jwt.claim.user.key=user
jwt.secret = classpath:rsatest.pub.pem
jwt.expires_in = 86400

#Enable/Disable Rate limit
auth.ratelimit=false

#read environment variable in production

spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.jdbc-url=jdbc:oracle:thin:@${DBAAS_DEFAULT_CONNECT_DESCRIPTOR}
spring.datasource.username=${DBAAS_USER_NAME}
spring.datasource.password=${DBAAS_USER_PASSWORD}
spring.datasource.jndi-name=java:jdbc/adorb-dev

spring.datasource.minimum-idle=5
spring.datasource.maximumPoolSize=${HIKARI_MAX_ACTIVE}


#spring.datasource.username=ADORB
#spring.datasource.password=adorbdvdb^3#6
#spring.datasource.jndi-name=java:jdbc/adorb-dev
#spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
#spring.datasource.jdbc-url=******************************************************************


spring.jpa.show-sql=false
spring.jpa.hibernate.dialect=org.hibernate.dialect.Oracle5Dialect
spring.jpa.database-platform=org.hibernate.dialect.Oracle10gDialect
spring.jpa.properties.hibernate.current_session_context_class=org.springframework.orm.hibernate5.SpringSessionContext

#Second Level Cache Properties
hibernate.cache.use_second_level_cache=true
hibernate.cache.provider_class = org.hibernate.cache.EhCacheProvider
hibernate.cache.region.factory_class=org.hibernate.cache.ehcache.EhCacheRegionFactory

#actuator properties
management.endpoints.web.base-path=/api/v2.0
management.endpoints.web.path-mapping.health=/health/security
management.endpoint.health.show-details=always
management.health.db.enabled=false
management.health.diskspace.enabled=false
management.health.redis.enabled = false

logging.level.org.springframework.web=ERROR
logging.level.com.recvueoktatoken=DEBUG
logging.level.org.redisson=ERROR

#Redis config
spring.redis.host=${REDIS_SERVER_URL}
spring.redis.port=${REDIS_SERVER_PORT}
spring.redis.timeout=${REDIS_REQUEST_TIMOUT}
#spring.redis.username=
spring.redis.password=${REDIS_SERVER_PASSWORD}
spring.redis.jedis.pool.max-idle=${REDIS_MAX_IDLE_CONNECTION_COUNT}
spring.redis.jedis.pool.min-idle=8
spring.redis.jedis.pool.max-wait=${REDIS_MAX_WAIT_TIME}
spring.redis.jedis.pool.max-active=${REDIS_MAX_CONNECTION_COUNT}
