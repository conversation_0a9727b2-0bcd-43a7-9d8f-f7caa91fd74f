api-version = /v2
springfox.documentation.swagger.v2.path=/api-docs
#read environment variable in production
spring.datasource.username=ADORB
spring.datasource.password="U4SvsgsAcJQ7tT9b"
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.jdbc-url=*************************************************************************************
#Nagarro Connection Testing
#spring.datasource.username=ADORB
#spring.datasource.password="Ad3vyset2##E"
#spring.datasource.jndi-name=java:jdbc/adorb-dev
#spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
#spring.datasource.jdbc-url=*********************************************************************************
spring.jpa.show-sql=false
spring.jpa.hibernate.dialect=org.hibernate.dialect.Oracle5Dialect
#spring.jpa.database-platform=org.hibernate.dialect.Oracle10gDialect
spring.jpa.database-platform = org.hibernate.dialect.Oracle12cDialect
spring.jpa.properties.hibernate.current_session_context_class=org.springframework.orm.hibernate5.SpringSessionContext
#Second Level Cache Properties
hibernate.cache.use_second_level_cache=true
hibernate.cache.provider_class = org.hibernate.cache.EhCacheProvider
hibernate.cache.region.factory_class=org.hibernate.cache.ehcache.EhCacheRegionFactory
logging.level.com.recvue=DEBUG
logging.level.org.springframework.web: ERROR
logging.level.org.hibernate: ERROR
logging.level.org.springframework: ERROR
jwt.algorithm = RSA
jwt.expires_in = 86400
jwt.header=X-Forwarded-User
jwt.publicKey.path=/rsatest.pub.pem
jwt.secret = classpath:rsatest.pub.pem
app.name = recvue
#actuator properties
management.endpoints.web.base-path=/api/v2.0
management.endpoints.web.path-mapping.health=/health/settings
management.endpoint.health.show-details=always
management.health.db.enabled=false
management.health.diskspace.enabled=false
#SNS passkeys
sns.accesskey=1
sns.secretkey=1
#email credentials
smtp.host=1
smtp.username=1
smtp.password=1
smtp.port=1
#kafka properties
kafka.server.url=1
kafka.server.port=1
kafka.server.username=1
kafka.server.password=1
kafka.topic.name=1
#redis Properties
#redis.server.url=${REDIS_SERVER_URL}
#redis.server.port=${REDIS_SERVER_PORT}
#redis.server.password=${REDIS_SERVER_PASSWORD}
#redis.timeout.millis=${REDIS_REQUEST_TIMOUT}
#redis.maxConn.count=${REDIS_MAX_CONNECTION_COUNT}
#redis.maxIdleConn.count=${REDIS_MAX_IDLE_CONNECTION_COUNT}
#redis.maxWaitTime.millis=${REDIS_MAX_WAIT_TIME}
#redis local Properties
redis.server.url=localhost
redis.server.port=6379

spring.redis.host=localhost
spring.redis.port=6379



redis.maxConn.count=1024
redis.maxIdleConn.count=1000
redis.maxWaitTime.millis=36000
spring.redis.timeout=3600ms

spring.redis.jedis.pool.max-active=4
spring.redis.jedis.pool.max-idle=4
spring.redis.jedis.pool.min-idle=1
spring.redis.jedis.pool.max-wait=1000ms

kafka.billrun.topic.name =1
kafka.broker.url =1
#kafka.billrun.topic.name =TestNotifications
#kafka.broker.url = *************:6667
kafka.topic.name.list = Imports
#Camunda user details
camunda.user=1
camunda.pwd=1
camunda.task.url=1
#To call event apis (ms_Settings)
api.version=api/v2.0
#app.url=${APP_URL}
app.url=https://deviris.recvue.com
#application environment
#environment = ${ENVIRONMENT}
environment = deviris

auth.ratelimit=false

# Authorization configuration
authorization.enabled=true
authorization.cache.enabled=false


recvue.okta.url=https://recvue.okta.com
recvue.okta.userUrl=/api/v1/authn
recvue.okta.user=/api/v1/users
recvue.okta.apiKey =******************************************
recvue.okta.clientId =0oa839dtwL8XoTvbF2p6
server.port=9022


