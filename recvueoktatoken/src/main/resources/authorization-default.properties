# Authorization Enhancement Configuration
# This file contains default configuration values for the authorization enhancement feature

# =============================================================================
# MAIN FEATURE FLAGS
# =============================================================================

# Enable/disable the entire authorization enhancement feature
# When disabled, JWT tokens will not include authorization claims
authorization.enabled=true

# Enable/disable authorization context caching
# When disabled, authorization data will be fetched from database on every request
authorization.caching-enabled=true

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================

# Cache expiration time in minutes (should match user token cache expiration)
authorization.cache.expiration-minutes=58

# Cache key prefix for authorization contexts
authorization.cache.key-prefix=auth_context

# Maximum number of authorization contexts to cache
authorization.cache.max-size=10000

# Enable cache statistics collection for monitoring
authorization.cache.statistics-enabled=true

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Connection timeout for authorization queries in seconds
authorization.database.connection-timeout-seconds=30

# Query timeout for authorization queries in seconds
authorization.database.query-timeout-seconds=15

# Maximum number of retries for failed database operations
authorization.database.max-retries=3

# Retry delay in milliseconds between failed attempts
authorization.database.retry-delay-ms=1000

# Enable batch processing for multiple authorization requests
authorization.database.batch-processing-enabled=true

# Batch size for processing multiple authorization requests
authorization.database.batch-size=100

# =============================================================================
# JWT TOKEN CONFIGURATION
# =============================================================================

# Include roles claim in JWT tokens
authorization.jwt.include-roles=true

# Include permissions claim in JWT tokens
authorization.jwt.include-permissions=true

# Include business units claim in JWT tokens
authorization.jwt.include-business-units=true

# Include read-only access claim in JWT tokens
authorization.jwt.include-read-only-access=true

# Maximum JWT token size in bytes (warning threshold)
authorization.jwt.max-token-size-bytes=4096

# Maximum number of roles to include in JWT token (prevents token bloat)
authorization.jwt.max-roles=50

# Maximum number of permissions to include in JWT token (prevents token bloat)
authorization.jwt.max-permissions=100

# Maximum number of business units to include in JWT token (prevents token bloat)
authorization.jwt.max-business-units=20

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Enable performance metrics collection
authorization.monitoring.metrics-enabled=true

# Enable detailed logging for authorization operations (use with caution in production)
authorization.monitoring.detailed-logging-enabled=false

# Log slow authorization queries (threshold in milliseconds)
authorization.monitoring.slow-query-threshold-ms=1000

# Enable cache hit/miss ratio monitoring
authorization.monitoring.cache-metrics-enabled=true

# Enable JWT token size monitoring
authorization.monitoring.token-size-monitoring-enabled=true

# Health check interval in seconds
authorization.monitoring.health-check-interval-seconds=60

# =============================================================================
# FALLBACK CONFIGURATION
# =============================================================================

# Enable fallback to cached data when database is unavailable
authorization.fallback.enable-cache-fallback=true

# Enable fallback to empty authorization data when all else fails
authorization.fallback.enable-empty-data-fallback=true

# Maximum age of cached data to use for fallback (in minutes)
authorization.fallback.max-cache-age-minutes=120

# Circuit breaker failure threshold (number of consecutive failures before opening circuit)
authorization.fallback.circuit-breaker-failure-threshold=5

# Circuit breaker timeout in seconds (how long to wait before trying again)
authorization.fallback.circuit-breaker-timeout-seconds=60

# =============================================================================
# REDIS CONFIGURATION (if not already configured)
# =============================================================================

# Redis connection configuration for authorization caching
# These settings are used if Redis is not already configured in the application

# Redis host
#spring.redis.host=localhost

# Redis port
#spring.redis.port=6379

# Redis password (if required)
#spring.redis.password=

# Redis database index for authorization cache
#spring.redis.database=0

# Redis connection timeout
#spring.redis.timeout=2000ms

# Redis connection pool configuration
#spring.redis.jedis.pool.max-active=8
#spring.redis.jedis.pool.max-idle=8
#spring.redis.jedis.pool.min-idle=0
#spring.redis.jedis.pool.max-wait=-1ms

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Set logging levels for authorization components
# Uncomment and adjust as needed for debugging

# Main authorization service logging
#logging.level.com.recvueoktatoken.authorizer.service=INFO

# Authorization cache logging
#logging.level.com.recvueoktatoken.authorizer.cache=INFO

# Authorization DAO logging
#logging.level.com.recvueoktatoken.coreuser.dao=INFO

# JWT utilities logging
#logging.level.com.recvueoktatoken.authorizer.request.JWTUtils=INFO

# Token validation logging
#logging.level.com.recvueoktatoken.authorizer.request.IdmTokenValidations=INFO

# =============================================================================
# ENVIRONMENT-SPECIFIC OVERRIDES
# =============================================================================

# Development environment settings
#---
#spring.config.activate.on-profile=dev
#authorization.monitoring.detailed-logging-enabled=true
#authorization.monitoring.slow-query-threshold-ms=500

# Production environment settings
#---
#spring.config.activate.on-profile=prod
#authorization.monitoring.detailed-logging-enabled=false
#authorization.monitoring.slow-query-threshold-ms=2000
#authorization.cache.max-size=50000

# Test environment settings
#---
#spring.config.activate.on-profile=test
#authorization.caching-enabled=false
#authorization.monitoring.metrics-enabled=false