# Development Environment Configuration
# This configuration is optimized for development and debugging

# =============================================================================
# AUTHORIZATION ENHANCEMENT - DEVELOPMENT SETTINGS
# =============================================================================

# Enable all features for development testing
authorization.enabled=false
authorization.caching-enabled=true

# =============================================================================
# FEATURE FLAGS - DEVELOPMENT
# =============================================================================

# Enable all JWT claims for testing
feature.authorization.enabled=true
feature.authorization.roles-enabled=true
feature.authorization.permissions-enabled=true
feature.authorization.business-units-enabled=true
feature.authorization.read-only-access-enabled=true

# Enable detailed logging for debugging
feature.authorization.detailed-logging-enabled=true
feature.authorization.metrics-enabled=true
feature.authorization.cache-statistics-enabled=true

# =============================================================================
# CACHE CONFIGURATION - DEVELOPMENT
# =============================================================================

# Smaller cache for development
authorization.cache.expiration-minutes=10
authorization.cache.max-size=1000
authorization.cache.statistics-enabled=true

# =============================================================================
# DATABASE CONFIGURATION - DEVELOPMENT
# =============================================================================

# Shorter timeouts for faster feedback during development
authorization.database.connection-timeout-seconds=10
authorization.database.query-timeout-seconds=5
authorization.database.max-retries=2
authorization.database.retry-delay-ms=500

# Enable batch processing for testing
authorization.database.batch-processing-enabled=true
authorization.database.batch-size=50

# =============================================================================
# JWT CONFIGURATION - DEVELOPMENT
# =============================================================================

# Include all claims for testing
authorization.jwt.include-roles=true
authorization.jwt.include-permissions=true
authorization.jwt.include-business-units=true
authorization.jwt.include-read-only-access=true

# Lower limits for development testing
authorization.jwt.max-token-size-bytes=2048
authorization.jwt.max-roles=25
authorization.jwt.max-permissions=50
authorization.jwt.max-business-units=10

# =============================================================================
# MONITORING - DEVELOPMENT
# =============================================================================

# Enable all monitoring for development
authorization.monitoring.metrics-enabled=true
authorization.monitoring.detailed-logging-enabled=true
authorization.monitoring.slow-query-threshold-ms=500
authorization.monitoring.cache-metrics-enabled=true
authorization.monitoring.token-size-monitoring-enabled=true
authorization.monitoring.health-check-interval-seconds=30

# =============================================================================
# FALLBACK CONFIGURATION - DEVELOPMENT
# =============================================================================

# Enable all fallback mechanisms for testing
authorization.fallback.enable-cache-fallback=true
authorization.fallback.enable-empty-data-fallback=true
authorization.fallback.max-cache-age-minutes=60
authorization.fallback.circuit-breaker-failure-threshold=3
authorization.fallback.circuit-breaker-timeout-seconds=30

# =============================================================================
# REDIS CONFIGURATION - DEVELOPMENT
# =============================================================================

# Local Redis for development
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.database=1
spring.redis.timeout=1000ms

# Smaller connection pool for development
spring.redis.jedis.pool.max-active=4
spring.redis.jedis.pool.max-idle=4
spring.redis.jedis.pool.min-idle=1
spring.redis.jedis.pool.max-wait=1000ms

# =============================================================================
# DATABASE CONFIGURATION - DEVELOPMENT
# =============================================================================

# H2 in-memory database for development (if not using external DB)
#spring.datasource.url=jdbc:h2:mem:testdb
#spring.datasource.driver-class-name=org.h2.Driver
#spring.datasource.username=sa
#spring.datasource.password=

# HikariCP settings for development
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.connection-timeout=10000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=600000

# =============================================================================
# LOGGING CONFIGURATION - DEVELOPMENT
# =============================================================================

# Enable debug logging for authorization components
logging.level.com.recvueoktatoken.authorizer=DEBUG
logging.level.com.recvueoktatoken.authorizer.service=DEBUG
logging.level.com.recvueoktatoken.authorizer.request=DEBUG
logging.level.com.recvueoktatoken.coreuser.dao=DEBUG

# Redis logging
logging.level.org.springframework.data.redis=DEBUG

# SQL logging (if using JPA)
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# =============================================================================
# ACTUATOR CONFIGURATION - DEVELOPMENT
# =============================================================================

# Expose all actuator endpoints for development
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.endpoint.health.show-components=always

# Enable metrics
management.metrics.export.prometheus.enabled=true
management.metrics.distribution.percentiles-histogram.http.server.requests=true

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# Enable Spring Boot DevTools
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true

# Disable caching for development
spring.thymeleaf.cache=false
spring.resources.cache.period=0

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================

# Enable test data creation
authorization.dev.create-test-data=true
authorization.dev.test-users=dev-user-1,dev-user-2,dev-admin

# Mock external services for development
authorization.dev.mock-okta=false
authorization.dev.mock-database=false