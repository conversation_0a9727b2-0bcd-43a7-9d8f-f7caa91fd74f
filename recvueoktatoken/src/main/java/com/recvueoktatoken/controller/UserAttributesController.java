/**
 * 
 */
package com.recvueoktatoken.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.recvueoktatoken.common.entity.CoreRoles;
import com.recvueoktatoken.common.entity.UserAttributes;
import com.recvueoktatoken.common.entity.User;
import com.recvueoktatoken.common.service.ApiAuthUserService;
import com.recvueoktatoken.common.service.UserService;
import com.recvueoktatoken.model.RolesResponse;
import com.recvueoktatoken.model.UserAttributesResponse;

@RestController
public class UserAttributesController {

	private final Logger LOG = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private ApiAuthUserService apiAuthUserService;

	@Autowired
	private UserService userService;

	@RequestMapping(value = "/api/v2.0/user/attributes", method = RequestMethod.GET, produces = "application/json")
	public ResponseEntity<Object> getUserDetails() {
	    final String UNAUTHORIZED_MESSAGE = "Unauthorized to perform operation.";

	    try {
	        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
	        if (authentication == null || !(authentication.getPrincipal() instanceof User)) {
	            LOG.info("Authentication failed or invalid user principal.");
	            return buildUnauthorizedResponse(UNAUTHORIZED_MESSAGE);
	        }

	        User authenticatedUser = (User) authentication.getPrincipal();
	        User user = apiAuthUserService.getUserByName(authenticatedUser.getUsername());

	        if (user == null) {
	            LOG.info("Username from token not found in DB: {}", authenticatedUser.getUsername());
	            return buildUnauthorizedResponse(UNAUTHORIZED_MESSAGE);
	        }

	        List<UserAttributes> userAttributes = userService.getCoreUserAttributeByUserId(user.getUserId());

	        UserAttributesResponse response = buildUserAttributesResponse(user, userAttributes);

	        LOG.info("User details fetched for: {}", response.getUserName());
	        LOG.info("Full Name: {}", response.getFullName());
	        LOG.info("User Attributes Count: {}", userAttributes.size());

	        return ResponseEntity.ok(response);

	    } catch (Exception ex) {
	        LOG.error("Error while fetching user details", ex);
	        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
	                .body(Collections.singletonMap("error", "Internal Server Error"));
	    }
	}

	private ResponseEntity<Object> buildUnauthorizedResponse(String message) {
	    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
	            .body(Collections.singletonMap("error", message));
	}

	private UserAttributesResponse buildUserAttributesResponse(User user, List<UserAttributes> userAttributes) {
	    UserAttributesResponse response = new UserAttributesResponse();
	    response.setUserId(user.getUserId());
	    response.setTenantId(user.getTenantId());
	    response.setUserName(user.getUsername());

	    String fullName = String.format("%s %s", 
	        Optional.ofNullable(user.getFirstName()).orElse(""),
	        Optional.ofNullable(user.getLastName()).orElse("")).trim();
	    response.setFullName(fullName);

	    response.setEmail(user.getEmailAddress());
	    response.setUserAttributes(userAttributes);
	    response.setDefaultHomeTab(user.getDefaultHomeTab());
	    response.setDefaultUIPreference(user.getDefaultUIPreference());
	    response.setUserBoards(user.getUserBoards());
	    response.setTimeZone(user.getTimezone());
	    response.setCurrencyCode(user.getCurrencyCode());
	    response.setDateFormat(user.getDateFormat());
	    response.setNumberFormat(user.getNumberFormat());
	    response.setTimeFormat(user.getTimeFormat());
	    response.setReadOnlyUser(user.getReadOnlyUser());
	    response.setRoles(mapRoles(new ArrayList<>(user.getRoles())));
	    response.setDefaultDashboard(user.getDefaultDashboard());

	    return response;
	}
	
	private List<RolesResponse> mapRoles(List<CoreRoles> roles) {
	    return roles.stream()
	        .map(role -> {
	            RolesResponse res = new RolesResponse();
	            res.setRoleId(role.getRoleId());
	            res.setName(role.getRoleName());
	            res.setDescription(role.getRoleInformation1());
	            return res;
	        })
	        .collect(Collectors.toList());
	}
	
	
}
