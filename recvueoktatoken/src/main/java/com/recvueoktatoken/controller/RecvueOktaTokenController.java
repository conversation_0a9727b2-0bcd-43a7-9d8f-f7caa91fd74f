/**
 * 
 */
package com.recvueoktatoken.controller;

import java.util.Collections;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.recvueoktatoken.model.Response;
import com.recvueoktatoken.utils.DecodeHeader;
import com.recvueoktatoken.utils.OktaUtils;

@RestController
public class RecvueOktaTokenController {

	@Value("${recvue.okta.url}")
	private String RECVUE_OKTA_URL;

	@Autowired
	private OktaUtils oktaUtils;

	@Autowired
	private DecodeHeader decodeHeader;

	private final Logger LOG = LoggerFactory.getLogger(this.getClass());
	
	@RequestMapping(value = "/api/v2.0/token", method = RequestMethod.GET, produces = "application/json")
	@ResponseBody
	public ResponseEntity<Object> getRecvueOktaToken(HttpServletRequest httpServletRequest,
			@RequestHeader("Authorization") String authorizationHeader) {
		try {
			
			/*
			 * commenting code to print request headers
			 
			
			// get tenant info
			String serverName = httpServletRequest.getServerName();
			String tenant = serverName.substring(0, serverName.indexOf("."));
			System.out.println("serverName " + serverName);
			System.out.println("tenant " + tenant);
			System.out.println("URL " + httpServletRequest.getRequestURI());
			Enumeration headerNames = httpServletRequest.getHeaderNames();
	        while (headerNames.hasMoreElements()) {
	            String key = (String) headerNames.nextElement();
	            String value = httpServletRequest.getHeader(key);
	            System.out.println(key +" "+ value );
	        }
			*/
	        
	        
			// decode authorizationHeader
			String[] userCredentials = decodeHeader.decodeAuthorization(authorizationHeader);

			// Get token from OKTA using API
			Response accessToken = oktaUtils.getUserAccessToken(userCredentials,httpServletRequest.getServerName());
			if (accessToken == null) {
				LOG.info("accessToken is null");
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Collections.singletonMap("error", "Unauthorized to perform operation."));
			} else {
				LOG.info("accessToken is not null");
				return ResponseEntity.status(HttpStatus.OK).body(accessToken);
			}

		} catch (Exception e) {
			LOG.error(e.getMessage().toString());
			return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Collections.singletonMap("error", e.getMessage()));
		}
	}
}
