/**
 * 
 */
package com.recvueoktatoken.controller;

import java.util.Collections;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.recvueoktatoken.common.entity.User;
import com.recvueoktatoken.common.security.TokenHelper;
import com.recvueoktatoken.common.service.ApiAuthUserService;
import com.recvueoktatoken.model.UserDetailsResponse;

@RestController
public class UserDetailsController {
	private final Logger LOG = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private ApiAuthUserService apiAuthUserService;

	@Autowired
	TokenHelper tokenHelper;

	@RequestMapping(value = "/api/v2.0/user", method = RequestMethod.GET, produces = "application/json")
	@ResponseBody
	public ResponseEntity<Object> getUserDetails() {
		try {
			Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
			if (authentication != null) {
				User u = (User) authentication.getPrincipal();
				User user = apiAuthUserService.getUserByName(u.getUsername());
				if (user == null) {
					LOG.info("username from token can't be found in DB.");
					return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
							.body(Collections.singletonMap("error", "Unauthorized to perform operation."));
				} else {
					SecurityContextHolder.getContext()
							.setAuthentication(new UsernamePasswordAuthenticationToken(user, null, null));
					UserDetailsResponse userDetailsResponse = new UserDetailsResponse();
					userDetailsResponse.setUsername(user.getUsername());
					return ResponseEntity.status(HttpStatus.OK)
							.body(userDetailsResponse);
				}
			} else {
				LOG.info("error: Unauthorized to perform operation.");
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
						.body(Collections.singletonMap("error", "Unauthorized to perform operation."));
			}
		} catch (Exception e) {
			LOG.error("error: " + e.getMessage());
			//e.printStackTrace();
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
					.body(Collections.singletonMap("error", e.getMessage()));
		}
	}

}
