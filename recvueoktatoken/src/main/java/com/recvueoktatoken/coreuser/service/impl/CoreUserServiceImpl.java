package com.recvueoktatoken.coreuser.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.hibernate.Session;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.recvueoktatoken.common.constants.RestConstants;
import com.recvueoktatoken.common.entity.CoreRoles;
import com.recvueoktatoken.common.entity.CoreUser;
import com.recvueoktatoken.common.entity.CoreUserAttributes;
import com.recvueoktatoken.common.entity.User;
import com.recvueoktatoken.common.multitenancy.TenantContext;
import com.recvueoktatoken.coreuser.dao.CoreUserDao;
import com.recvueoktatoken.coreuser.dto.CoreRoleDto;
import com.recvueoktatoken.coreuser.dto.CoreUserAttributesDto;
import com.recvueoktatoken.coreuser.dto.CoreUserDTO;
import com.recvueoktatoken.coreuser.dto.OktaUser;
import com.recvueoktatoken.coreuser.dto.Profile;
import com.recvueoktatoken.coreuser.service.CoreUserService;
import com.recvueoktatoken.utils.Converters;
import com.recvueoktatoken.utils.DTOMapper;


@Transactional(readOnly = false, propagation = Propagation.REQUIRED)
@Service
public class CoreUserServiceImpl implements CoreUserService {

	@Autowired
	CoreUserDao coreUserDao;

	@Value("${recvue.okta.url}")
	private String oktaApiUrl;

	@Value("${recvue.okta.apiKey}")
	private String oktaApiToken;


	@Value("${recvue.okta.user}")
	private String oktaUser;


	private static final Logger logger = LoggerFactory.getLogger(CoreUserServiceImpl.class);

	private final RestTemplate restTemplate;

	public CoreUserServiceImpl(RestTemplate restTemplate) {
		this.restTemplate = restTemplate;
	}

	@Override
	public Page<Object> getAllCoreUserDtls(String userId, String userName, String emailAddress, String status, String role,
			Integer page, Integer size, String[] sort, String search, String[] fields) {


		List<Order> o = new ArrayList<>();
		if (sort != null) {
			for (String sparam : sort) {
				if (sparam.contains(":")) {
					String[] param = sparam.split(":", 2);
					o.add(new Order(Direction.valueOf(param[1].toUpperCase()), param[0]));
				} else {
					o.add(new Order(Direction.valueOf("ASC"), sparam));
				}
			}
		}

		StringBuilder queryBuilder = new StringBuilder("from CoreUser cu ");

		if (userId != null && !userId.isEmpty()) {
			queryBuilder.append(" left outer join fetch cu.coreRoles left outer join fetch cu.coreUserAttributes where cu.userId = '").append(userId).append("' ");
		}

		if (userName != null && !userName.isEmpty()) {
			queryBuilder.append(" left outer join fetch cu.coreRoles left outer join fetch cu.coreUserAttributes where cu.userName = '").append(userName).append("' ");
		}

		if (emailAddress != null && !emailAddress.isEmpty()) {
			queryBuilder.append(" left outer join fetch cu.coreRoles left outer join fetch cu.coreUserAttributes  where cu.emailAddress = '").append(emailAddress).append("' ");
		}

		if (status != null && !status.isEmpty()) {
			if ("active".equalsIgnoreCase(status)) {
				status = "A";
			} else if ("inactive".equalsIgnoreCase(status)) {
				status = "I";
			}
			queryBuilder.append(" where cu.status = '").append(status).append("' ");
		}

		if (role != null && !role.isEmpty()) {
			queryBuilder.append(" left outer join fetch cu.coreRoles cr where cr.roleName = '").append(role).append("' ");
		}

		String searchFilter = "";
		if (search != null && search.trim().length() > 0) {
			searchFilter = " and ( upper(fullName) like :searchFullNameStr OR " +
					"upper(firstName) like :searchFirstNameStr OR " +
					"upper(lastName) like :searchLastNameStr OR " +
					"upper(userName) like :searchUserNameStr)";
		}

		Sort sortOrder = Sort.by(o);
		String sortStr = sortOrder.toString().replaceAll(":", " ");
		String baseQuery = queryBuilder.toString();
		String fullQuery = baseQuery + searchFilter + " order by " + sortStr;

		String countQuery = parsigQueryMethod(baseQuery + searchFilter);

		List<CoreUser> src = coreUserDao.getAllCoreUserDtls(page, size, sort, search, fullQuery);
		if (src == null) {
			return null;
		}

		List<CoreUserDTO> coreUserObjectEntity = mapToDTO(src, userId, userName, emailAddress);


		Long countRequests = (size == null && page == null)
				? (long) src.size()
						: coreUserDao.getUsersCount(countQuery, search);

		if (size == null && page == null) {
			size = src.size();
			page = 1;
		}

		PageRequest pageable = PageRequest.of(page - 1, size, sortOrder);
		@SuppressWarnings({ "unchecked", "rawtypes" })
		Page<Object> data = new PageImpl(coreUserObjectEntity, pageable, countRequests);		
		return data;

	}

	// Utility method to map user list to DTO based on the condition
	private List<CoreUserDTO> mapToDTO(List<CoreUser> users, String userId, String userName, String emailAddress) {
		if ((userId != null && !userId.isEmpty()) || 
				(userName != null && !userName.isEmpty()) || 
				(emailAddress != null && !emailAddress.isEmpty())) {

			return DTOMapper.mapCoreUserList(users);
		} else {
			return DTOMapper.mapCoreUserListBasic(users);
		}
	}

	private String parsigQueryMethod(String qry) {
		List<String> toRemove = Arrays.asList("SELECT", "o");
		String text = null;
		String delimiter = " ";
		String scount = "SELECT count(*) ";
		text = Pattern.compile(delimiter).splitAsStream(qry).filter(s -> !toRemove.contains(s))
				.collect(Collectors.joining(delimiter));
		return scount.concat(text);
	}	

	@Override
	public Long createUser(CoreUserDTO coreUserDto, User user) throws Exception {

		List<CoreRoleDto> coreRoleDtoList = coreUserDto.getCoreRoleDto();
		List<CoreUserAttributesDto> coreUserAttributesDto = coreUserDto.getCoreUserAttributesDto();
		
		Long roleId = null;
		String oktaGroup = null;

		// Check for duplicate roles
		if (!isRoleContainsDuplicate(coreRoleDtoList)) {
			logger.error(RestConstants.DUPLICATE_ROLE_ERROR_MESSAGE);
			throw new Exception(RestConstants.DUPLICATE_ROLE_ERROR_MESSAGE);
		}

		// Check for existing username
		if (!coreUserDao.isUserNameUnique(coreUserDto.getUserName())) {
			throw new Exception("User name already exists in database");
		}

		if (coreRoleDtoList == null || coreRoleDtoList.size() != 1) {
			throw new IllegalArgumentException("Create user with multiple roles failed.Currently, we are not supporting add or update multiple roles");
		}

		CoreRoleDto coreRoleDto = coreRoleDtoList.get(0);
		String roleName = coreRoleDto.getRoleName();
		String readOnlyUser = coreUserDto.getReadOnlyUser();
		
		if (readOnlyUser == null || readOnlyUser.trim().isEmpty()) {
			throw new IllegalArgumentException("Read only user cannot be null or empty.");
		}

		if (roleName == null || roleName.trim().isEmpty()) {
			throw new IllegalArgumentException("Role name cannot be null or empty.");
		}

		try {
			logger.info("Validating role name: {}", roleName);

			// Optional check or mapping using external system/service	
			if (coreUserDto.getAuthenticationSource().equalsIgnoreCase("okta")) {
				try {

					oktaGroup = checkoktaGroupRoleId(roleName);
					logger.info("Resolved Okta Role: {}", oktaGroup);

				} catch (Exception e) {
					logger.error("Error while checking Okta group role for '{}': {}", roleName, e.getMessage(), e);
					throw new RuntimeException("Failed to resolve Okta role for role name: " + roleName, e);
				}
			}

			// Check if role exists in DB and retrieve its ID
			roleId = coreUserDao.getRoleIdIfExistsByName(roleName, readOnlyUser);
			if (roleId == null) {
				throw new IllegalStateException("Role '" + roleName + "' does not exist in the database with Read Only access '"+readOnlyUser+"'.");
			}

			logger.info("Resolved Role ID: {}", roleId);

		} catch (Exception e) {
			logger.error("Failed to resolve or validate role '{}': {}", roleName, e.getMessage(), e);
			throw new RuntimeException("Role validation failed: " + e.getMessage(), e);
		}


		OktaUser Request;
		String oktaRes;
		logger.info("Inside saveCoreUser :{}", coreUserDto.getFirstName());
		CoreUser coreUser = new CoreUser();
		Converters convert = new Converters();
		coreUser = convert.convertDtoToEntity(coreUserDto, coreUser);		
		
			
		logger.info("Inside saveCoreUser : {}", coreUser.getFirstName());
		coreUser.setStatus("A");
		if ("Federation".equalsIgnoreCase(coreUser.getAuthenticationSource())) {
			coreUser.setAuthenticationSource("Federation");
		}
		else if ("Okta".equalsIgnoreCase(coreUser.getAuthenticationSource())) {
			coreUser.setAuthenticationSource("Okta");
		}
		Long userId = coreUserDao.createUser(coreUser, roleId, user);		
		saveCoreUserAttributes(coreUserAttributesDto, userId, user.getTenantId());		

		if (coreUserDto.getAuthenticationSource().equalsIgnoreCase("okta")) {
			Request = mapCoreUserToOktaUser(coreUser);
			try {
				oktaRes = createoktaUser(Request);
				logger.info("Created user in Okta : {}", oktaRes);
				ObjectMapper objectMapper = new ObjectMapper();
				OktaUser oktaUser = objectMapper.readValue(oktaRes, OktaUser.class);
				logger.info("Created user in Okta : {}", oktaUser.getId());
				createoktaGroupUser(oktaGroup, oktaUser.getId());
				logger.info("Added user in Okta group : {}", oktaUser.getId());

			} catch (Exception e) {			
				e.printStackTrace();
			}
		}		
		return userId;
	}

	@Override
	public Long updateUser(CoreUserDTO coreUserDto, User user) throws Exception {

		List<CoreRoleDto> coreRoleDtoList = coreUserDto.getCoreRoleDto();			
		List<CoreUserAttributesDto> coreUserAttributesDto = coreUserDto.getCoreUserAttributesDto();

		Long roleId = null;	
		Long userId = null;
		OktaUser Request;
		String oktaRes;
		String oktaGroup = null;

		logger.info("Inside updateCoreUser :{}", coreUserDto.getFirstName());
		String userName = coreUserDto.getUserName();
		userId = coreUserDao.getUserIdIfExistsByName(userName);
		if (userId == null) {
			throw new Exception("User name does not exist");
		}

		String assignedRole = coreUserDao.getUserRoleByName(userName);
		logger.info("Inside updateCoreUser - Assigned Role : {}", assignedRole);

		CoreRoleDto coreRoleDto = coreRoleDtoList.get(0);
		String roleName = coreRoleDto.getRoleName();
		String readOnlyUser = coreUserDto.getReadOnlyUser();			

		if (roleName == null || roleName.trim().isEmpty()) {
			throw new IllegalArgumentException("Role name cannot be null or empty.");
		}

		// Check if role exists in DB and retrieve its ID
		roleId = coreUserDao.getRoleIdIfExistsByName(roleName, readOnlyUser);
		if (roleId == null) {
			throw new IllegalStateException("Role '" + roleName + "' does not exist in the database with Read Only access '"+readOnlyUser+"'.");
		}

		try {
			logger.info("Validating role name: {}", roleName);
			// Optional check or mapping using external system/service	
			if (coreUserDto.getAuthenticationSource().equalsIgnoreCase("okta")) {
				try {

					if (!assignedRole.equalsIgnoreCase(roleName)) {

						oktaGroup = checkoktaGroupRoleId(roleName);
						logger.info("Resolved Okta Role: {}", oktaGroup);
						deleteOktaGroupUser(userName);
					}

				} catch (Exception e) {
					logger.error("Error while checking Okta group role for '{}': {}", roleName, e.getMessage(), e);
					throw new RuntimeException("Failed to resolve Okta role for role name: " + roleName, e);
				}
			}				


		} catch (Exception e) {
			logger.error("Failed to resolve or validate role '{}': {}", roleName, e.getMessage(), e);
			throw new RuntimeException("Role validation failed: " + e.getMessage(), e);
		}		


		CoreUser coreUser = new CoreUser();
		Converters convert = new Converters();
		coreUser = convert.convertDtoToEntity(coreUserDto, coreUser);
		logger.info("Inside updateCoreUser : {}", coreUser.getFirstName());
		coreUser.setUserId(userId);
		if ("Federation".equalsIgnoreCase(coreUser.getAuthenticationSource())) {
			coreUser.setAuthenticationSource("Federation");
		}
		else if ("Okta".equalsIgnoreCase(coreUser.getAuthenticationSource())) {
			coreUser.setAuthenticationSource("Okta");
		}
		coreUserDao.updateUser(coreUser, roleId, user);
		saveCoreUserAttributes(coreUserAttributesDto, userId, user.getTenantId());
		if (coreUserDto.getAuthenticationSource().equalsIgnoreCase("okta")) {
			Request = mapCoreUserToOktaUser(coreUser);
			try {

				// Retrieve Okta user ID
				String oktaUserId = getOktaUserId(userName);
				if (oktaUserId == null) {
					logger.error("Okta user ID not found for user: {}", userName);
					throw new IllegalArgumentException("Okta user ID is null for user: " + userName);
				}
				oktaRes = updateoktaUser(Request, oktaUserId);
				logger.info("Updated user in Okta : {}", oktaRes);
				ObjectMapper objectMapper = new ObjectMapper();
				OktaUser oktaUser = objectMapper.readValue(oktaRes, OktaUser.class);
				logger.info("Updated user in Okta : {}", oktaUser.getId());

				if (!assignedRole.equalsIgnoreCase(roleName)) {
					createoktaGroupUser(oktaGroup, oktaUser.getId());
					logger.info("Updated user in Okta group : {}", oktaUser.getId());
				}

			} catch (Exception e) {			
				e.printStackTrace();
			}
		}
		return userId;
	}

	public Long removeUserAccess(CoreUserDTO coreUserDto, User user) throws Exception {

		logger.info("Inside removeUserAccess :{}", coreUserDto.getUserName());		
		String userName = coreUserDto.getUserName();		
		Long userId = null;
		if (userName != null) {
			userId = coreUserDao.getUserIdIfExistsByName(userName);
			if (userId == null) {
				throw new Exception("User name does not exist");
			}			
			deleteOktaGroupUser(userName);
			coreUserDao.updateUserStatus(userId, user);
		}
		return null;

	}

	private boolean isRoleContainsDuplicate(List<CoreRoleDto> coreRoleDtoList) {		
		if(coreRoleDtoList != null && !coreRoleDtoList.isEmpty()) {
			Set<CoreRoleDto> set = coreRoleDtoList.stream()
					.collect(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CoreRoleDto::getRoleName))));
			return (set.size() == coreRoleDtoList.size());
		}
		return true;
	} 
	
	
	@Override
	public String getOktaUserId(String  userName) throws Exception {
		try {		
			
			String roleName = coreUserDao.getUserRoleByName(userName);
			logger.info("user role name : {}", roleName);	
			
			String url = oktaApiUrl + oktaUser +"/"+ userName;
			HttpEntity<String> request = new HttpEntity<>(createHeaders());
			ResponseEntity<String> response = makeApiRequest(url, HttpMethod.GET, request);
			
			ObjectMapper objectMapper = new ObjectMapper();
			OktaUser oktaUser = objectMapper.readValue(response.getBody(), OktaUser.class);
			logger.info("user id in Okta : {}", oktaUser.getId());	
			
			return oktaUser.getId();
		} catch (HttpClientErrorException e) {            
			throw e;
		} catch (Exception e) {   	
			throw e;
		}
	}	

	@Override
	public String createoktaUser(OktaUser userRequest) throws Exception {
		try {
			String profile = new ObjectMapper().writeValueAsString(userRequest);
			HttpEntity<String> request = new HttpEntity<>(profile, createHeaders());
			ResponseEntity<String> response = makeApiRequest(oktaApiUrl + oktaUser, HttpMethod.POST, request);
			return response.getBody();
		} catch (HttpClientErrorException e) {            
			throw e;
		} catch (Exception e) {   	
			throw e;
		}

	}
	
	@Override
	public String updateoktaUser(OktaUser userRequest, String userId) throws Exception {
		try {
			String profile = new ObjectMapper().writeValueAsString(userRequest);
			HttpEntity<String> request = new HttpEntity<>(profile, createHeaders());
			ResponseEntity<String> response = makeApiRequest(oktaApiUrl + oktaUser+ "/" + userId, HttpMethod.POST, request);
			return response.getBody();
		} catch (HttpClientErrorException e) {            
			throw e;
		} catch (Exception e) {   	
			throw e;
		}

	}


	public String activateOktaUser(String userId) {
		try {
			String url = oktaApiUrl + oktaUser+ "/" + userId +"/lifecycle/activate";  	
			logger.debug(url);
			HttpEntity<String> request = new HttpEntity<>(createHeaders());
			ResponseEntity<String> response = makeApiRequest(url, HttpMethod.POST, request);
			logger.debug(response.getBody());
			return response.getBody();
		} catch (HttpClientErrorException e) {
			throw e;
		} catch (Exception e) {        	
			throw e;
		}
	}


	public String deactivateOktaUser(String userId) {
		try {
			String url = oktaApiUrl + oktaUser+ "/" + userId +"/lifecycle/deactivate";
			logger.debug(url);
			HttpEntity<String> request = new HttpEntity<>(createHeaders());
			ResponseEntity<String> response = makeApiRequest(url, HttpMethod.POST, request);
			logger.debug(response.getBody());
			return response.getBody();
		} catch (HttpClientErrorException e) {
			throw e;
		} catch (Exception e) {
			throw e;
		}
	}

	public String createoktaGroupUser(String groupId, String userId) throws Exception {
		try {
			String url = oktaApiUrl + "/api/v1/groups/"+groupId+"/users/"+ userId;
			logger.debug(url);
			HttpEntity<String> request = new HttpEntity<>(createHeaders());
			ResponseEntity<String> response = makeApiRequest(url, HttpMethod.PUT, request);
			logger.debug(response.getBody());
			return response.getBody();
		} catch (HttpClientErrorException e) {            
			throw e;
		} catch (Exception e) {        	
			throw e;
		}
	}

	public String deleteOktaGroupUser(String userName) throws Exception {
	    try {
	        // Retrieve user role
	        String roleName = coreUserDao.getUserRoleByName(userName);
	        if (roleName == null) {
	            logger.error("User role not found for user: {}", userName);
	            throw new IllegalArgumentException("User role is null for user: " + userName);
	        }

	        // Retrieve Okta user ID
	        String userId = getOktaUserId(userName);
	        if (userId == null) {
	            logger.error("Okta user ID not found for user: {}", userName);
	            throw new IllegalArgumentException("Okta user ID is null for user: " + userName);
	        }

	        // Retrieve Okta group ID based on role
	        String groupId = checkoktaGroupRoleId(roleName);
	        if (groupId == null) {
	            logger.error("Okta group ID not found for role: {}", roleName);
	            throw new IllegalArgumentException("Okta group ID is null for role: " + roleName);
	        }

	        // Build Okta API URL to delete user from group
	        String url = String.format("%s/api/v1/groups/%s/users/%s", oktaApiUrl, groupId, userId);
	        logger.debug("Making API request to URL: {}", url);

	        // Create request with headers
	        HttpEntity<String> request = new HttpEntity<>(createHeaders());

	        // Make DELETE API call
	        ResponseEntity<String> response = makeApiRequest(url, HttpMethod.DELETE, request);
	        logger.debug("API Response: {}", response.getBody());

	        return response.getBody();
	    } catch (HttpClientErrorException e) {
	        logger.error("HTTP error while deleting Okta group user: {}", e.getMessage(), e);
	        throw new Exception("Failed to delete Okta group user due to HTTP error: " + e.getMessage(), e);
	    } catch (IllegalArgumentException e) {
	        logger.error("Validation error: {}", e.getMessage());
	        throw e;
	    } catch (Exception e) {
	        logger.error("Unexpected error occurred while deleting Okta group user: {}", e.getMessage(), e);
	        throw new Exception("Unexpected error while deleting Okta group user", e);
	    }
	}


	public String checkoktaGroupRoleId(String roleName) throws Exception {
		try {
			String tenantIdentifier = TenantContext.getCurrentTenant();
			String searchParam = "profile.name eq \""+tenantIdentifier+"@"+roleName+"\"";
			//https://recvue.okta.com/api/v1/groups?search=profile.name eq "deviris@AR"
			String url = oktaApiUrl + "/api/v1/groups?search="+searchParam;
			logger.debug(url);
			HttpEntity<String> request = new HttpEntity<>(createHeaders());
			ResponseEntity<String> response = makeApiRequest(url, HttpMethod.GET, request);
			logger.debug(response.getBody());
			JSONArray roleArray = new JSONArray(response.getBody());
			if (roleArray.length() == 0) {
				throw new IllegalArgumentException("JSON array is empty.");
			}
			JSONObject jsonObject = roleArray.getJSONObject(0);
			if (!jsonObject.has("id") || jsonObject.get("id") == null || jsonObject.getString("id").isEmpty()) {
				throw new IllegalArgumentException("Missing or empty 'id' field in JSON.");
			}		
			return jsonObject.getString("id");
		} catch (HttpClientErrorException e) {
			throw e;
		} catch (Exception e) {        	
			throw e;
		}

	}
	
	@Override
	public void saveCoreUserAttributes(List<CoreUserAttributesDto> coreUserAttributesDto, Long userId, Long tenant) {
		if (coreUserAttributesDto == null || coreUserAttributesDto.isEmpty()) {
	        return;
	    }		
	    for (CoreUserAttributesDto attr : coreUserAttributesDto) {
	    	
	    	CoreUserAttributes coreUserAttributes = new CoreUserAttributes();
			Converters convert = new Converters();
			coreUserAttributes = convert.convertDtoToEntity(attr, coreUserAttributes);			
			coreUserAttributes.setUserId(userId);
			coreUserAttributes.setTenantId(tenant);
	    	coreUserDao.saveCoreUserAttributes(coreUserAttributes);
	    }
	}


	private HttpHeaders createHeaders() {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add("Authorization", "SSWS " + oktaApiToken);
		return headers;
	}

	private ResponseEntity<String> makeApiRequest(String url, HttpMethod method, HttpEntity<String> request) {
		try {
			return restTemplate.exchange(url, method, request, String.class);
		} catch (HttpClientErrorException e) {
			logger.error("Error from Okta: {}", e.getResponseBodyAsString());
			throw e;
		} catch (Exception e) {
			throw e;
		}
	}

	public static OktaUser mapCoreUserToOktaUser(CoreUser coreUser) {
		OktaUser oktaUser = new OktaUser();
		Profile profile = oktaUser.getProfile();
		profile.setFirstName(coreUser.getFirstName());
		profile.setLastName(coreUser.getLastName());
		profile.setEmail(coreUser.getEmailAddress());
		profile.setLogin(coreUser.getUserName());
		profile.setCity(coreUser.getCity());
		profile.setCountryCode(coreUser.getCountry());
		profile.setTitle(coreUser.getTitle());
		profile.setOrganization(coreUser.getCompany());
		profile.setState(coreUser.getState());
		profile.setPreferredLanguage(coreUser.getLanguageCode());

		return oktaUser;
	}

	

}
