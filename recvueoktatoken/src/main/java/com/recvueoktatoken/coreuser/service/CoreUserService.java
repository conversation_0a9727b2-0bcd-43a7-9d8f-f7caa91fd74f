package com.recvueoktatoken.coreuser.service;

import java.util.List;

import org.springframework.data.domain.Page;

import com.recvueoktatoken.common.entity.CoreUserAttributes;
import com.recvueoktatoken.common.entity.User;
import com.recvueoktatoken.coreuser.dto.CoreUserAttributesDto;
import com.recvueoktatoken.coreuser.dto.CoreUserDTO;
import com.recvueoktatoken.coreuser.dto.OktaUser;

public interface CoreUserService {
	
	public Page<Object> getAllCoreUserDtls(String userId, String userName, String email, String status, String role,
            Integer page, Integer size, String[] sort, String search, String[] fields);
	
	public Long createUser(CoreUserDTO coreUser, User user) throws Exception;
	public Long updateUser(CoreUserDTO coreUser, User user) throws Exception;
	public String createoktaUser(OktaUser userRequest) throws Exception;
	public String updateoktaUser(OktaUser userRequest, String userId) throws Exception;
	public String deactivateOktaUser(String userName);
	public String activateOktaUser(String userName);
	public Long removeUserAccess(CoreUserDTO coreUserDto, User user) throws Exception;
	public String getOktaUserId(String userName) throws Exception;
	public void saveCoreUserAttributes(List<CoreUserAttributesDto> coreUserAttributesDto, Long userId, Long tenant);
	
}
