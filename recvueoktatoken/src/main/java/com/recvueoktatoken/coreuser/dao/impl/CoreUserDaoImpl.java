package com.recvueoktatoken.coreuser.dao.impl;

import java.util.Date;
import java.util.List;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.recvueoktatoken.common.constants.Constants;
import com.recvueoktatoken.common.constants.RolesAction;
import com.recvueoktatoken.common.dao.impl.GenericDAOImpl;
import com.recvueoktatoken.common.entity.CoreRoles;
import com.recvueoktatoken.common.entity.CoreUser;
import com.recvueoktatoken.common.entity.CoreUserAttributes;
import com.recvueoktatoken.common.entity.CoreUserRoles;
import com.recvueoktatoken.common.entity.User;
import com.recvueoktatoken.coreuser.dao.CoreUserDao;
import com.recvueoktatoken.coreuser.dto.CoreRoleDto;

@Repository
public class CoreUserDaoImpl extends GenericDAOImpl implements CoreUserDao {

	private static final long serialVersionUID = 1L;
	private static final Logger logger = LoggerFactory.getLogger(CoreUserDaoImpl.class);

	@SuppressWarnings("unchecked")
	@Override
	public List<CoreUser> getAllCoreUserDtls(Integer page, Integer size, String[] sort, String search, String query) {

		Query queryObj = null;
		queryObj = getCurrentSession().createQuery(query);

		if (search != null && search.trim().length() > 0) {
			queryObj.setParameter("searchFullNameStr", '%' + search.toUpperCase() + '%');
			queryObj.setParameter("searchFirstNameStr", '%' + search.toUpperCase() + '%');
			queryObj.setParameter("searchLastNameStr", '%' + search.toUpperCase() + '%');
			queryObj.setParameter("searchUserNameStr", '%' + search.toUpperCase() + '%');

		}

		if (size != null) {
			queryObj.setFirstResult((page - 1) * size);
			queryObj.setMaxResults(size);
		}

		List<CoreUser> list = (List<CoreUser>) queryObj.getResultList();
		return (list != null && !list.isEmpty()) ? list : null;
	}

	@Override
	public Long getUsersCount(String qryCount, String search) {
		Query query = null;
		query = getCurrentSession().createQuery(qryCount);

		if (search != null && search.trim().length() > 0) {
			query.setParameter("searchFullNameStr", '%' + search.toUpperCase() + '%');
			query.setParameter("searchFirstNameStr", '%' + search.toUpperCase() + '%');
			query.setParameter("searchLastNameStr", '%' + search.toUpperCase() + '%');
			query.setParameter("searchUserNameStr", '%' + search.toUpperCase() + '%');
		}
		return (Long) query.getResultList().get(0);
	}

	@Override
	public Long createUser(CoreUser coreUser, Long roleId, User user) {
		logger.info("Initializing user creation: {}", coreUser.toString());

		// Populate coreUser fields with common details
		populateCoreUserDetails(coreUser, user);

		// Save the core user and obtain the generated user ID
		Long userId = (Long) getCurrentSession().save(coreUser);
		logger.info("User saved with ID: {}", userId);

		// Assign role to the user
		assignUserRole(userId, roleId, user);

		// Flush the session to persist changes
		getCurrentSession().flush();

		return userId;
	}

	@Override
	public void updateUser(CoreUser coreUser, Long roleId, User user) {
		logger.debug("Updating user : {}", coreUser.toString());

		// Populate coreUser fields with common details
		populateCoreUserDetails(coreUser, user);

		// Save the core user and obtain the generated user ID
		getCurrentSession().update(coreUser);

		// Assign role to the user
		assignUserRole(coreUser.getUserId(), roleId, user);

		// Flush the session to persist changes
		getCurrentSession().flush();
	}

	private void populateCoreUserDetails(CoreUser coreUser, User user) {
		coreUser.setTenantId(user.getTenantId());
		coreUser.setCreatedBy(user.getUserId());
		coreUser.setLastUpdatedBy(user.getUserId());
		coreUser.setCreationDate(getCreationDate());
		coreUser.setLastUpdateDate(getLastUpdateDate());
	}

	private void assignUserRole(Long userId, Long roleId, User user) {
		Query query = getCurrentSession().createNativeQuery(
				"INSERT INTO CORE_USER_ROLES (USER_ID, ROLE_ID, DATE_FROM, TENANT_ID, LAST_UPDATE_DATE, " +
						"LAST_UPDATED_BY, CREATED_BY, CREATION_DATE) " +
						"VALUES (:userid, :roleId, sysdate, :tenantId, sysdate, :updatedBy, :createdBy, sysdate)");
		query.setParameter("roleId", roleId);
		query.setParameter("userid", userId);
		query.setParameter("tenantId", user.getTenantId());
		query.setParameter("updatedBy", user.getUserId());
		query.setParameter("createdBy", user.getUserId());
		query.executeUpdate();
		getCurrentSession().flush();
		getCurrentSession().clear();
	}

	public void updateUserStatus(Long userId, User user) {
		Query query = getCurrentSession().createNativeQuery(
				"UPDATE CORE_USER " +
						"SET STATUS = 'I', " +
						"LAST_UPDATE_DATE = :updateDate, " +
						"LAST_UPDATED_BY = :updatedBy " +
						"WHERE USER_ID = :userId AND TENANT_ID = :tenantId");
		query.setParameter("userId", userId);
		query.setParameter("tenantId", user.getTenantId());
		query.setParameter("updateDate", getCreationDate());
		query.setParameter("updatedBy", user.getUserId());
		query.executeUpdate();
	}

	@Override
	public CoreUser getCoreUserById(long roleId) {
		return getCurrentSession().get(CoreUser.class, roleId);
	}

	@Override
	public void updateCoreUser(CoreUser coreUser) {
		coreUser.setCreationDate(getCreationDate());
		coreUser.setLastUpdateDate(getLastUpdateDate());
		try (Session session = getCurrentSession()) {
			session.saveOrUpdate(coreUser);
			session.flush();
		}
	}

	@Override
	public List<CoreUser> getUsersListByStatus(String Status) {

		String hql = " from CoreUser cu left outer join fetch cu.coreRoles where status=? ";

		return getCurrentSession()
				.createQuery(hql, CoreUser.class)
				.setParameter(0, Constants.INACTIVE)
				.list();
	}

	@Override
	public List<CoreUserAttributes> getCoreUserAttributes(String userName) {
		String hql = "from CoreUserAttributes where userId = " +
				"(select userId from CoreUser where userName = :userName)";
		return getCurrentSession()
				.createQuery(hql, CoreUserAttributes.class)
				.setParameter("userName", userName)
				.list();
	}

	@Override
	public boolean isUserNameUnique(String userName) {
		Criteria criteria = getCurrentSession().createCriteria(CoreUser.class);
		criteria.setProjection(Projections.property("userName"));
		criteria.add(Restrictions.eq("userName", userName).ignoreCase());
		List<String> userList = criteria.list();
		return userList.isEmpty();
	}

	public Long getRoleIdIfExistsByName(String roleName, String readOnlyUser) {
		String sql = "SELECT role_id FROM core_roles WHERE role_name = :roleName and read_only = :readOnlyUser";
		Query query = getCurrentSession().createNativeQuery(sql);
		query.setParameter("roleName", roleName);
		query.setParameter("readOnlyUser", readOnlyUser);

		try {
			Object result = query.getSingleResult();
			return result != null ? ((Number) result).longValue() : null;
		} catch (NoResultException e) {
			return null;
		}
	}

	public Long getUserIdIfExistsByName(String userName) {
		String sql = "SELECT user_id FROM core_user WHERE user_name = :userName";
		Query query = getCurrentSession().createNativeQuery(sql);
		query.setParameter("userName", userName);

		try {
			Object result = query.getSingleResult();
			return result != null ? ((Number) result).longValue() : null;
		} catch (NoResultException e) {
			return null;
		}
	}

	public String getUserRoleByName(String userName) {

		String sql = "SELECT cr.role_name FROM core_user cu LEFT OUTER JOIN core_user_roles cur ON cu.user_id = cur.user_id LEFT OUTER JOIN core_roles cr ON cur.role_id = cr.role_id 	WHERE cu.user_name = :userName";
		Query query = getCurrentSession().createNativeQuery(sql);
		query.setParameter("userName", userName);
		try {
			Object result = query.getSingleResult();
			return result != null ? ((String) result).toString() : null;
		} catch (NoResultException e) {
			return null;
		}
	}

	@Override
	public void saveCoreUserAttributes(CoreUserAttributes userAttributesList) {
		Session session = getCurrentSession();
		session.saveOrUpdate(userAttributesList);
		session.flush(); // ensure changes are persisted
		session.clear(); // detach all entities to free memory
	}

	private CoreUserRoles mapRoleModelToEntity(CoreRoleDto coreRoleDto, Long userId, CoreUser coreUser, User user) {
		CoreUserRoles coreUserRoles = new CoreUserRoles(userId);
		coreUserRoles.setRoleId(coreRoleDto.getRoleId());
		// coreUserRoles.setUserId(userId);

		Date sysDate = new Date();
		// if(RolesAction.ADD.equals(coreRoleDto.getAction())){
		coreUserRoles.setCreationDate(sysDate);
		coreUserRoles.setCreatedBy(user.getUserId());
		// }
		coreUserRoles.setLastUpdateDate(sysDate);
		coreUserRoles.setLastUpdatedBy(user.getUserId());
		if (coreUser.getTenantId() != null) {
			coreUserRoles.setTenantId(coreUser.getTenantId());
		}
		return coreUserRoles;
	}

	// Authorization-related methods implementation

	@Override
	public CoreUser getUserWithAuthorizationContext(String username) {
		logger.debug("Fetching user with authorization context for username: {}", username);

		String hql = "SELECT DISTINCT cu FROM CoreUser cu " +
				"LEFT JOIN FETCH cu.coreRoles cr " +
				"LEFT JOIN FETCH cr.corePermissions cp " +
				"LEFT JOIN FETCH cr.businessUnits bu " +
				"LEFT JOIN FETCH bu.businessUnits " +
				"WHERE cu.userName = :username AND cu.status = 'A'";

		try {
			return getCurrentSession()
					.createQuery(hql, CoreUser.class)
					.setParameter("username", username)
					.getSingleResult();
		} catch (NoResultException e) {
			logger.warn("No active user found with username: {}", username);
			return null;
		} catch (Exception e) {
			logger.error("Error fetching user with authorization context for username: {}", username, e);
			throw e;
		}
	}

	@Override
	public List<String> getUserRoleNames(String username) {
		logger.debug("Fetching role names for username: {}", username);

		String hql = "SELECT DISTINCT cr.roleName FROM CoreUser cu " +
				"JOIN cu.coreRoles cr " +
				"WHERE cu.userName = :username AND cu.status = 'A'";

		try {
			@SuppressWarnings("unchecked")
			List<String> roleNames = getCurrentSession()
					.createQuery(hql)
					.setParameter("username", username)
					.getResultList();

			logger.debug("Found {} roles for username: {}", roleNames.size(), username);
			return roleNames;
		} catch (Exception e) {
			logger.error("Error fetching role names for username: {}", username, e);
			throw e;
		}
	}

	@Override
	public List<String> getUserPermissionNames(String username) {
		logger.debug("Fetching permission names for username: {}", username);

		String hql = "SELECT DISTINCT cp.perName FROM CoreUser cu " +
				"JOIN cu.coreRoles cr " +
				"JOIN cr.corePermissions cp " +
				"WHERE cu.userName = :username AND cu.status = 'A'";

		try {
			@SuppressWarnings("unchecked")
			List<String> permissionNames = getCurrentSession()
					.createQuery(hql)
					.setParameter("username", username)
					.getResultList();

			logger.debug("Found {} permissions for username: {}", permissionNames.size(), username);
			return permissionNames;
		} catch (Exception e) {
			logger.error("Error fetching permission names for username: {}", username, e);
			throw e;
		}
	}

	@Override
	public List<com.recvueoktatoken.authorizer.model.BusinessUnitInfo> getUserBusinessUnits(String username) {
		logger.debug("Fetching business units for username: {}", username);

		String hql = "SELECT DISTINCT bu.orgId, bu.name FROM CoreUser cu " +
				"JOIN cu.coreRoles cr " +
				"JOIN cr.businessUnits crbu " +
				"JOIN crbu.businessUnits bu " +
				"WHERE cu.userName = :username AND cu.status = 'A'";

		try {
			@SuppressWarnings("unchecked")
			List<Object[]> results = getCurrentSession()
					.createQuery(hql)
					.setParameter("username", username)
					.getResultList();

			List<com.recvueoktatoken.authorizer.model.BusinessUnitInfo> businessUnits = new java.util.ArrayList<>();
			for (Object[] result : results) {
				Long orgId = (Long) result[0];
				String name = (String) result[1];
				businessUnits.add(new com.recvueoktatoken.authorizer.model.BusinessUnitInfo(orgId, name));
			}

			logger.debug("Found {} business units for username: {}", businessUnits.size(), username);
			return businessUnits;
		} catch (Exception e) {
			logger.error("Error fetching business units for username: {}", username, e);
			throw e;
		}
	}

	@Override
	public boolean isReadOnlyUser(String username) {
		logger.debug("Checking read-only status for username: {}", username);

		String hql = "SELECT cu.readOnlyUser FROM CoreUser cu " +
				"WHERE cu.userName = :username AND cu.status = 'A'";

		try {
			String readOnlyFlag = getCurrentSession()
					.createQuery(hql, String.class)
					.setParameter("username", username)
					.getSingleResult();

			boolean isReadOnly = "Y".equalsIgnoreCase(readOnlyFlag);
			logger.debug("User {} read-only status: {}", username, isReadOnly);
			return isReadOnly;
		} catch (NoResultException e) {
			logger.warn("No active user found with username: {}", username);
			return false;
		} catch (Exception e) {
			logger.error("Error checking read-only status for username: {}", username, e);
			throw e;
		}
	}

}
