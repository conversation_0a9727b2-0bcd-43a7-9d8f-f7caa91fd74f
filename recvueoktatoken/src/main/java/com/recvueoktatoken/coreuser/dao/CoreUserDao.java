package com.recvueoktatoken.coreuser.dao;

import java.util.List;

import com.recvueoktatoken.authorizer.model.BusinessUnitInfo;
import com.recvueoktatoken.common.entity.CoreUser;
import com.recvueoktatoken.common.entity.CoreUserAttributes;
import com.recvueoktatoken.common.entity.User;

public interface CoreUserDao {

	public List<CoreUser> getAllCoreUserDtls(Integer page, Integer size, String[] sort, String search, String query);
	public Long getUsersCount(String qryCount, String search);
	public Long createUser(CoreUser coreUser, Long roleId, User user);
	public CoreUser getCoreUserById(long roleId);
	public void updateUser(CoreUser coreUser, Long roleId, User user);
	public void updateCoreUser(CoreUser coreUser);
	public List<CoreUserAttributes> getCoreUserAttributes(String userName);
	public List<CoreUser> getUsersListByStatus(String Status);
	
	public boolean isUserNameUnique(String userName);
	public Long getRoleIdIfExistsByName(String roleName, String readOnlyUser);
	public void updateUserStatus(Long userId, User user);
	public Long getUserIdIfExistsByName(String userName);
	public String getUserRoleByName(String userName);
	public void saveCoreUserAttributes(CoreUserAttributes userAttributesList);
	
	// Authorization-related methods
	public CoreUser getUserWithAuthorizationContext(String username);
	public List<String> getUserRoleNames(String username);
	public List<String> getUserPermissionNames(String username);
	public List<BusinessUnitInfo> getUserBusinessUnits(String username);
	public boolean isReadOnlyUser(String username);

}
	
