package com.recvueoktatoken.coreuser.controller;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.HttpClientErrorException;

import com.recvueoktatoken.common.constants.Constants;
import com.recvueoktatoken.common.constants.RestConstants;
import com.recvueoktatoken.common.entity.User;
import com.recvueoktatoken.common.multitenancy.TenantContext;
import com.recvueoktatoken.common.service.ApiAuthUserService;
import com.recvueoktatoken.coreuser.dto.CoreRoleDto;
import com.recvueoktatoken.coreuser.dto.CoreUserDTO;
import com.recvueoktatoken.coreuser.service.CoreUserService;
import com.recvueoktatoken.model.DefaultResponse;
import com.recvueoktatoken.utils.Pagination;
import com.recvueoktatoken.utils.RestControllerUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import jakarta.validation.Valid;

@Api(tags = "CoreUser", description = "Contain APIs related to CoreUser")
@RestController
@RequestMapping("/api/v2.0/users")
public class CoreUserController implements ICoreUserController {

	private static final Logger logger = LoggerFactory.getLogger(CoreUserController.class);

	//public final String USER_INFO_END_POINT = "/users";

	@Autowired
	protected ApiAuthUserService apiAuthService;

	@Autowired
	protected CoreUserService coreUserService;

	@Autowired
	private RestControllerUtil restControllerUtil;


	/**
	 * Method to get All User Info
	 */
	@ApiOperation(value = "Get All User Details", nickname = "getAllUserDetails", notes = "Get All User Details", produces = "application/json")
	@ApiResponses(value = {
			@ApiResponse(code = 200, message = "Successfully retrieved list", response = CoreUserDTO.class),
			@ApiResponse(code = 401, message = "Unauthorized", response = DefaultResponse.class),
			@ApiResponse(code = 404, message = "Not Found", response = DefaultResponse.class),
			@ApiResponse(code = 400, message = "Invalid Request", response = DefaultResponse.class),
			@ApiResponse(code = 500, message = "Failure", response = DefaultResponse.class) })
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	@GetMapping//(value = USER_INFO_END_POINT)
	public ResponseEntity<?> getAllUserDetails(
			@RequestParam(value = "userName", required = false) String userName,
			@RequestParam(value = "userId", required = false) String userId,
			@RequestParam(value = "email", required = false) String emailAddress,
			@RequestParam(value = "status", required = false) String status,
			@RequestParam(value = "role", required = false) String role,
			@RequestParam(value = "size", required = false) Integer size,
			@RequestParam(value = "page", required = false) Integer page,
			@RequestParam(value = "sort", required = false) String[] sort,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "fields", required = false) String[] fields) {

		logger.debug("PT Request Recieved :Get All User Info ");
		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		DefaultResponse dr = new DefaultResponse();
		Pagination<Object> contentError = new Pagination<>();
		contentError.setStatusCode(RestConstants.INTERNAL_SERVER_ERROR_STATUS_CODE);

		if (authentication != null) {
			User u = (User) authentication.getPrincipal();
			User user = apiAuthService.getUserByName(u.getUsername());
			if (user == null) {
				dr.setMessage(Constants.UNAUTHORISED_STATUS_ERROR_MESSAGE);
				dr.setStatusCode(java.net.HttpURLConnection.HTTP_UNAUTHORIZED);
				return new ResponseEntity<Object>(dr, HttpStatus.UNAUTHORIZED);
			} else {
				try {
					if (!(size == null && page == null)) {
						if (size == null) {
							size = 10;
						}
						size = (size <= 0) ? 10 : size;

						if (page == null) {
							page = 0;
						}
						page = (page <= 0) ? 1 : page;
					}

					if (sort == null) {
						sort = new String[1];
						sort[0] = "cu.fullName";
					} else {
						if (sort != null) {
							for (String sparam : sort) {
								if (!sparam.contains(":")) {
									return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Collections
											.singletonMap("Incorrect Sort Parameter.", HttpStatus.BAD_REQUEST));
								}
							}
						}
					}
					Pagination<Object> content = new Pagination<>();										
					Page<Object> pagedata = coreUserService.getAllCoreUserDtls(userId, userName, emailAddress, status, role, page, size, sort, search, fields);

					if (null != pagedata) {
						content.setData(pagedata.getContent());
						com.recvueoktatoken.utils.Page p = new com.recvueoktatoken.utils.Page();
						p.setSize(pagedata.getSize());
						p.setTotalElements(pagedata.getTotalElements());
						p.setTotalPages(pagedata.getTotalPages());
						p.setNumber(pagedata.getNumber() + 1);
						content.setPage(p);
						logger.debug("PT: Request completed at Server :Get Users Details Object");
						return ResponseEntity.ok(content);
					} else {						
						dr.setMessage(Constants.NO_RECORDS_FOUND);
						dr.setStatusCode(java.net.HttpURLConnection.HTTP_NOT_FOUND);
						return new ResponseEntity(dr, HttpStatus.NOT_FOUND);
					}
				} catch (Exception ex) {
					logger.error("Exception",ex);
					dr.setMessage(ex.getMessage());
					dr.setStatusCode(java.net.HttpURLConnection.HTTP_INTERNAL_ERROR);
					return new ResponseEntity(dr, HttpStatus.INTERNAL_SERVER_ERROR);
				}
			}
		} else {
			dr.setStatusCode(java.net.HttpURLConnection.HTTP_INTERNAL_ERROR);
			return new ResponseEntity(dr, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@Override
	@PostMapping//(value = USER_INFO_END_POINT)
	public ResponseEntity<?> createUser(@RequestBody @Valid CoreUserDTO coreUserDto) {
		try {
			logger.info("Received request to create user: {}", coreUserDto.getFirstName());
			Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
			DefaultResponse dr = new DefaultResponse();

			if (authentication != null) {

				User u = (User) authentication.getPrincipal();
				logger.info("User Name is : {}", u.getUsername());
				User user = apiAuthService.getUserByName(u.getUsername());
				if (user == null) {
					dr.setMessage(Constants.UNAUTHORISED_STATUS_ERROR_MESSAGE);
					dr.setStatusCode(java.net.HttpURLConnection.HTTP_UNAUTHORIZED);
					return new ResponseEntity<Object>(dr, HttpStatus.UNAUTHORIZED);
				}

				if (coreUserDto != null) {
					if (StringUtils.isEmpty(coreUserDto.getUserName())) {

						return ResponseEntity.status(HttpStatus.BAD_REQUEST)
								.body(restControllerUtil.createFailedResponse(null,
										RestConstants.INVALID_STATUS_CODE, "User name can not be null or empty"));			
					}
					
					if (StringUtils.isEmpty(coreUserDto.getFirstName())) {

						return ResponseEntity.status(HttpStatus.BAD_REQUEST)
								.body(restControllerUtil.createFailedResponse(null,
										RestConstants.INVALID_STATUS_CODE, "First name can not be null or empty"));			
					}
					
					if (StringUtils.isEmpty(coreUserDto.getLastName())) {

						return ResponseEntity.status(HttpStatus.BAD_REQUEST)
								.body(restControllerUtil.createFailedResponse(null,
										RestConstants.INVALID_STATUS_CODE, "Last name can not be null or empty"));			
					}
					
					if (StringUtils.isEmpty(coreUserDto.getEmailAddress())) {

						return ResponseEntity.status(HttpStatus.BAD_REQUEST)
								.body(restControllerUtil.createFailedResponse(null,
										RestConstants.INVALID_STATUS_CODE, "Email address can not be null or empty"));			
					}
					
					String authSource = coreUserDto.getAuthenticationSource();
					
					if (StringUtils.isEmpty(authSource)) {
					    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
					            .body(restControllerUtil.createFailedResponse(null,
					                    RestConstants.INVALID_STATUS_CODE, 
					                    "Authentication source cannot be null or empty"));
					}

					if (!authSource.equalsIgnoreCase("okta") && !authSource.equalsIgnoreCase("Federation")) {
					    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
					            .body(restControllerUtil.createFailedResponse(null,
					                    RestConstants.INVALID_STATUS_CODE, 
					                    "Authentication source should be either 'Okta' or 'Federation'"));
					}
					
					logger.info("Create User: {}", coreUserDto.getFirstName());
					Long userId = coreUserService.createUser(coreUserDto, user);
					return ResponseEntity.status(HttpStatus.CREATED).body(restControllerUtil
							.createSuccessResponse(userId, RestConstants.CREATED_STATUS_CODE, "User created successfully"));
				}

			}
			logger.debug(RestConstants.USERNAME_VALIDATION_ERROR_MESSAGE);
			return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
					.body(restControllerUtil.createFailedResponse(null,
							RestConstants.UNAUTHORISED_STATUS_CODE,
							RestConstants.UNAUTHORISED_STATUS_ERROR_MESSAGE));
		} catch (Exception e) {
			logger.error("Exception occured while creating user ", e);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(restControllerUtil
					.createFailedResponse(null, RestConstants.INTERNAL_SERVER_ERROR_STATUS_CODE, e.getMessage()));
		}

	}
	
	
	@Override
	@PutMapping//(value = USER_INFO_END_POINT)
	public ResponseEntity<?> updateUser(@Valid @RequestBody CoreUserDTO coreUserDto) {
		try {
			logger.info("Received request to update user: {}", coreUserDto.getFirstName());
			Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
			DefaultResponse dr = new DefaultResponse();

			if (authentication != null) {

				User u = (User) authentication.getPrincipal();
				logger.info("User Name is : {}", u.getUsername());
				User user = apiAuthService.getUserByName(u.getUsername());
				if (user == null) {
					dr.setMessage(Constants.UNAUTHORISED_STATUS_ERROR_MESSAGE);
					dr.setStatusCode(java.net.HttpURLConnection.HTTP_UNAUTHORIZED);
					return new ResponseEntity<Object>(dr, HttpStatus.UNAUTHORIZED);
				}

				if (coreUserDto != null) {
					if (StringUtils.isEmpty(coreUserDto.getUserName())) {

						return ResponseEntity.status(HttpStatus.BAD_REQUEST)
								.body(restControllerUtil.createFailedResponse(null,
										RestConstants.INVALID_STATUS_CODE, "User name can not be null or empty"));
					}	
					
					if (StringUtils.isEmpty(coreUserDto.getFirstName())) {

						return ResponseEntity.status(HttpStatus.BAD_REQUEST)
								.body(restControllerUtil.createFailedResponse(null,
										RestConstants.INVALID_STATUS_CODE, "First name can not be null or empty"));			
					}
					
					if (StringUtils.isEmpty(coreUserDto.getLastName())) {

						return ResponseEntity.status(HttpStatus.BAD_REQUEST)
								.body(restControllerUtil.createFailedResponse(null,
										RestConstants.INVALID_STATUS_CODE, "Last name can not be null or empty"));			
					}
					
					if (StringUtils.isEmpty(coreUserDto.getEmailAddress())) {

						return ResponseEntity.status(HttpStatus.BAD_REQUEST)
								.body(restControllerUtil.createFailedResponse(null,
										RestConstants.INVALID_STATUS_CODE, "Email address can not be null or empty"));			
					}					
					
					String authSource = coreUserDto.getAuthenticationSource();
					
					if (StringUtils.isEmpty(authSource)) {
					    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
					            .body(restControllerUtil.createFailedResponse(null,
					                    RestConstants.INVALID_STATUS_CODE, 
					                    "Authentication source cannot be null or empty"));
					}

					if (!authSource.equalsIgnoreCase("okta") && !authSource.equalsIgnoreCase("Federation")) {
					    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
					            .body(restControllerUtil.createFailedResponse(null,
					                    RestConstants.INVALID_STATUS_CODE, 
					                    "Authentication source should be either 'Okta' or 'Federation'"));
					}
					
					if (StringUtils.isEmpty(coreUserDto.getReadOnlyUser())) {

						return ResponseEntity.status(HttpStatus.BAD_REQUEST)
								.body(restControllerUtil.createFailedResponse(null,
										RestConstants.INVALID_STATUS_CODE, "Read only user can not be null or empty"));			
					}
					
					List<CoreRoleDto> coreRoleDtoList = coreUserDto.getCoreRoleDto();
					if (coreRoleDtoList != null && coreRoleDtoList.size() != 1) {
						throw new IllegalArgumentException("Update multiple roles failed.Currently, we are not supporting add or update multiple roles");
					}				
					
					logger.info("updateCoreUser: {}", coreUserDto.getFirstName());
					Long userId = coreUserService.updateUser(coreUserDto, user);
					return ResponseEntity.status(HttpStatus.CREATED).body(restControllerUtil
							.createSuccessResponse(userId, RestConstants.CREATED_STATUS_CODE, "User updated successfully"));
				}

			}
			logger.debug(RestConstants.USERNAME_VALIDATION_ERROR_MESSAGE);
			return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
					.body(restControllerUtil.createFailedResponse(null,
							RestConstants.UNAUTHORISED_STATUS_CODE,
							RestConstants.UNAUTHORISED_STATUS_ERROR_MESSAGE));
		} catch (Exception e) {
			logger.error("Exception occured while creating user ", e);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(restControllerUtil
					.createFailedResponse(null, RestConstants.INTERNAL_SERVER_ERROR_STATUS_CODE, e.getMessage()));
		}

	}


	@PostMapping(value = "/activate", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<?> activateUser(@Valid @RequestBody CoreUserDTO coreUserDTO) {
		try {
			String userName = coreUserDTO.getUserName();
			logger.info("Received request to activate user: {}", userName);

			if (StringUtils.isBlank(userName)) {
				logger.warn("User ID is null or empty");
				return ResponseEntity.badRequest().body("Invalid User ID");
			}
			String response = coreUserService.activateOktaUser(userName);
			return ResponseEntity.status(HttpStatus.OK).body(restControllerUtil
					.createSuccessResponse(null, RestConstants.SUCCESS_STATUS_CODE, "User activated successfully"));

		} catch (HttpClientErrorException e) {
			return handleHttpClientErrorException(e);
		} catch (Exception e) {			
			return handleGeneralException(e, "Activate user");
		}

	}


	@PostMapping(value = "/deactivate", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<?> deactivateUser(@Valid @RequestBody CoreUserDTO coreUserDTO) {

		try {
			String userName = coreUserDTO.getUserName();
			logger.info("Received request to deactivate user: {}", userName);

			if (StringUtils.isBlank(userName)) {
				logger.warn("User name is null or empty");
				return ResponseEntity.badRequest().body("Invalid User name");
			}

			coreUserService.deactivateOktaUser(userName);
			return ResponseEntity.status(HttpStatus.OK).body(restControllerUtil
					.createSuccessResponse(null, RestConstants.SUCCESS_STATUS_CODE, "User deactivated successfully"));


		} catch (HttpClientErrorException e) {	
			return handleOktaErrorException(e);			
		} catch (Exception e) {		
			return handleGeneralException(e, "deactivate users");
		}
	}

	@PostMapping(value = "/remove-access", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<?> removeUserAccess(@Valid @RequestBody CoreUserDTO coreUserDTO) {

		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		if (authentication == null) {
			return buildErrorResponse(Constants.UNAUTHORISED_STATUS_ERROR_MESSAGE, HttpStatus.UNAUTHORIZED);
		}

		User currentUser = (User) authentication.getPrincipal();
		logger.info("User Name is: {}", currentUser.getUsername());

		User user = apiAuthService.getUserByName(currentUser.getUsername());
		if (user == null) {
			return buildErrorResponse(Constants.UNAUTHORISED_STATUS_ERROR_MESSAGE, HttpStatus.UNAUTHORIZED);
		}

		try {
			String userName = coreUserDTO.getUserName();
			logger.info("Received request to remove user access: {}", userName);

			if (StringUtils.isBlank(userName)) {
				logger.warn("User name is null or empty");
				return ResponseEntity.badRequest().body("User name is null or empty");
			}			
			coreUserService.removeUserAccess(coreUserDTO, user);		
			return ResponseEntity.status(HttpStatus.OK).body(restControllerUtil
					.createSuccessResponse(null, RestConstants.SUCCESS_STATUS_CODE, "User access removed successfully"));

		} catch (HttpClientErrorException e) {
			return handleHttpClientErrorException(e);
		} catch (Exception e) {
			return handleGeneralException(e, "remove user access");
		}
	}

	private ResponseEntity<Object> buildErrorResponse(String message, HttpStatus status) {
		DefaultResponse dr = new DefaultResponse();
		dr.setMessage(message);
		dr.setStatusCode(status.value());
		return new ResponseEntity<>(dr, status);
	}

	private ResponseEntity<Map<String, Object>> handleHttpClientErrorException(HttpClientErrorException e) {
		logger.error("An HTTP error occurred: {}", e.getMessage(), e);
		Map<String, Object> errorResponse = new HashMap<>();
		errorResponse.put("error", e.getMessage());
		errorResponse.put("status", e.getStatusCode().value());

		JSONObject jsonObject = new JSONObject(e.getResponseBodyAsString());
		if (!jsonObject.has("errorSummary") || jsonObject.get("errorSummary") == null || jsonObject.getString("errorSummary").isEmpty()) {
			errorResponse.put("message", e.getResponseBodyAsString());
		}else
			errorResponse.put("message", jsonObject.getString("errorSummary"));        
		return ResponseEntity.status(e.getStatusCode()).body(errorResponse);
	}
	
	
	private ResponseEntity<Map<String, Object>> handleOktaErrorException(HttpClientErrorException e) {
		logger.error("An HTTP error occurred: {}", e.getMessage(), e);
		Map<String, Object> errorResponse = new HashMap<>();
		errorResponse.put("error", e.getMessage());
		errorResponse.put("status", e.getStatusCode().value());	
			errorResponse.put("message", "User is already deactivated or user not found in Okta");
		return ResponseEntity.status(e.getStatusCode()).body(errorResponse);
	}

	private ResponseEntity<Map<String, Object>> handleGeneralException(Exception e, String action) {
		logger.error("An error occurred while {}", action, e);
		Map<String, Object> errorResponse = new HashMap<>();
		errorResponse.put("error", "An error occurred while " + action);
		errorResponse.put("status", HttpStatus.INTERNAL_SERVER_ERROR.value());
		errorResponse.put("message", e.getMessage());
		return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
	}

}
