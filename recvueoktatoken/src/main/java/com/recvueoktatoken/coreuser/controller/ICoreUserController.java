package com.recvueoktatoken.coreuser.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.recvueoktatoken.coreuser.dto.CoreUserDTO;

import jakarta.validation.Valid;

public interface ICoreUserController {
	
	public ResponseEntity<?> getAllUserDetails(
	        @RequestParam(value = "userName", required = false) String userName,
	        @RequestParam(value = "userId", required = false) String userId,
	        @RequestParam(value = "email", required = false) String emailAddress,
	        @RequestParam(value = "status", required = false) String status,
	        @RequestParam(value = "role", required = false) String role,
	        @RequestParam(value = "size", required = false) Integer size,
	        @RequestParam(value = "page", required = false) Integer page,
	        @RequestParam(value = "sort", required = false) String[] sort,
	        @RequestParam(value = "search", required = false) String search,
	        @RequestParam(value = "fields", required = false) String[] fields);
	
	public ResponseEntity<?> createUser(@Valid @RequestBody CoreUserDTO coreUser);
	public ResponseEntity<?> updateUser(@Valid @RequestBody CoreUserDTO coreUser);

}
