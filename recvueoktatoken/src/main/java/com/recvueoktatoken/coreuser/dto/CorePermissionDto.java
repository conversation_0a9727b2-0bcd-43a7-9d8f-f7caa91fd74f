package com.recvueoktatoken.coreuser.dto;

import com.recvueoktatoken.common.constants.RolesAction;

public class CorePermissionDto {
	
    private String perDescription;

    private String perName;

    private Long perId;
    
    private RolesAction action = RolesAction.UPDATE;
    
    private String perReadOnly;
    

	public String getPerDescription() {
		return perDescription;
	}

	public void setPerDescription(String perDescription) {
		this.perDescription = perDescription;
	}

	public String getPerName() {
		return perName;
	}

	public void setPerName(String perName) {
		this.perName = perName;
	}

	public Long getPerId() {
		return perId;
	}

	public void setPerId(Long perId) {
		this.perId = perId;
	}

	public RolesAction getAction() {
		return action;
	}

	public void setAction(RolesAction action) {
		this.action = action;
	}

	public String getPerReadOnly() {
		return perReadOnly;
	}

	public void setPerReadOnly(String perReadOnly) {
		this.perReadOnly = perReadOnly;
	}
}
