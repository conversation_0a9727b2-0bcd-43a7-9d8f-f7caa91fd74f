package com.recvueoktatoken.coreuser.dto;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.Valid;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OktaUser implements Serializable {
	
	private static final long serialVersionUID = 1L;
		
	 	@Valid
	 	@JsonProperty("profile")
	    private Profile profile;
	    
	    @Valid
	    @JsonProperty("id")
	    private String id;
	    
	    public OktaUser() {
	        this.profile = new Profile();
	    }
	    
	    public String getId() {
			return id;
		}
		public void setId(String id) {
			this.id = id;
		}
		public Profile getProfile() {
	        return profile;
	    }
	    public void setProfile(Profile profile) {
	        this.profile = profile;
	    }
	
	
    
    
    
	

}
