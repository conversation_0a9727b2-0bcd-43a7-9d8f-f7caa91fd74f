package com.recvueoktatoken.coreuser.dto;

import java.math.BigInteger;
import java.util.List;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.recvueoktatoken.utils.DateFormat;


@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreRoleDto {    

    private Long roleId;

    private Long employeeId;

    @Temporal(TemporalType.TIMESTAMP)
    @DateFormat(dateFormat = "yyyy-MM-dd", message = "Given dateFrom is not in the expected format. The format should be yyyy-MM-dd.")
    private String dateFrom;

    @Temporal(TemporalType.TIMESTAMP)
    @DateFormat(dateFormat = "yyyy-MM-dd", message = "Given dateFrom is not in the expected format. The format should be yyyy-MM-dd.")
    private String dateTo;

    private String roleName;

    private String attribute1;

    private String attribute2;

    private String attribute3;

    private String attribute4;

    private String attribute5;

    private String attribute6;

    private String attribute7;

    private String attribute8;

    private String attribute9;

    private String attribute10;

    private String roleInformation1;

    private String roleInformation2;

    private String role3Information3;

    private BigInteger approvalAuthority;

    private String comments;
    
    private String readOnlyRole;
    
    private List<CorePermissionDto> corePermissionDto;
    
	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public Long getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(Long employeeId) {
		this.employeeId = employeeId;
	}

	public String getRoleName() {
		return roleName;
	}

	public String getDateFrom() {
		return dateFrom;
	}

	public void setDateFrom(String dateFrom) {
		this.dateFrom = dateFrom;
	}

	public String getDateTo() {
		return dateTo;
	}

	public void setDateTo(String dateTo) {
		this.dateTo = dateTo;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	public String getAttribute1() {
		return attribute1;
	}

	public void setAttribute1(String attribute1) {
		this.attribute1 = attribute1;
	}

	public String getAttribute2() {
		return attribute2;
	}

	public void setAttribute2(String attribute2) {
		this.attribute2 = attribute2;
	}

	public String getAttribute3() {
		return attribute3;
	}

	public void setAttribute3(String attribute3) {
		this.attribute3 = attribute3;
	}

	public String getAttribute4() {
		return attribute4;
	}

	public void setAttribute4(String attribute4) {
		this.attribute4 = attribute4;
	}

	public String getAttribute5() {
		return attribute5;
	}

	public void setAttribute5(String attribute5) {
		this.attribute5 = attribute5;
	}

	public String getAttribute6() {
		return attribute6;
	}

	public void setAttribute6(String attribute6) {
		this.attribute6 = attribute6;
	}

	public String getAttribute7() {
		return attribute7;
	}

	public void setAttribute7(String attribute7) {
		this.attribute7 = attribute7;
	}

	public String getAttribute8() {
		return attribute8;
	}

	public void setAttribute8(String attribute8) {
		this.attribute8 = attribute8;
	}

	public String getAttribute9() {
		return attribute9;
	}

	public void setAttribute9(String attribute9) {
		this.attribute9 = attribute9;
	}

	public String getAttribute10() {
		return attribute10;
	}

	public void setAttribute10(String attribute10) {
		this.attribute10 = attribute10;
	}


	public String getRoleInformation1() {
		return roleInformation1;
	}

	public void setRoleInformation1(String roleInformation1) {
		this.roleInformation1 = roleInformation1;
	}

	public String getRoleInformation2() {
		return roleInformation2;
	}

	public void setRoleInformation2(String roleInformation2) {
		this.roleInformation2 = roleInformation2;
	}

	public String getRole3Information3() {
		return role3Information3;
	}

	public void setRole3Information3(String role3Information3) {
		this.role3Information3 = role3Information3;
	}

	public BigInteger getApprovalAuthority() {
		return approvalAuthority;
	}

	public void setApprovalAuthority(BigInteger approvalAuthority) {
		this.approvalAuthority = approvalAuthority;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public List<CorePermissionDto> getCorePermissionDto() {
		return corePermissionDto;
	}

	public void setCorePermissionDto(List<CorePermissionDto> corePermissionDto) {
		this.corePermissionDto = corePermissionDto;
	}

	public String getReadOnlyRole() {
		return readOnlyRole;
	}

	public void setReadOnlyRole(String readOnlyRole) {
		this.readOnlyRole = readOnlyRole;
	}
}
