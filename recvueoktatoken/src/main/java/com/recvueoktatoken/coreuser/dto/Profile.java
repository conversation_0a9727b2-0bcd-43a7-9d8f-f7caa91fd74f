package com.recvueoktatoken.coreuser.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Profile implements Serializable {

	private static final long serialVersionUID = 1L;

	@NotBlank(message = "First name is required")
	@JsonProperty("firstName")
	private String firstName;

	@NotBlank(message = "Last name is required")
	@JsonProperty("lastName")
	private String lastName;

	@NotBlank(message = "Email is required")
	@Email(message = "Invalid email format")
	@JsonProperty("email")
	private String email;

	@NotBlank(message = "Login is required")
	@Email(message = "Invalid login format")
	@JsonProperty("login")
	private String login;

	@NotBlank(message = "Mobile phone is required")
	@Pattern(regexp = "^\\d{3}-\\d{3}-\\d{4}$", message = "Invalid phone format (expected: ************)")
	private String mobilePhone;   

	private String title;
	private String city;
	private String organization;
	private String state;
	private String countryCode;
	private String preferredLanguage;
	
	
	public String getPreferredLanguage() {
		return preferredLanguage;
	}
	public void setPreferredLanguage(String preferredLanguage) {
		this.preferredLanguage = preferredLanguage;
	}
	public String getOrganization() {
		return organization;
	}
	public void setOrganization(String organization) {
		this.organization = organization;
	}
	public String getCountryCode() {
		return countryCode;
	}
	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}


	// Getters and Setters
	public String getFirstName() { return firstName; }
	public void setFirstName(String firstName) { this.firstName = firstName; }
	public String getLastName() { return lastName; }
	public void setLastName(String lastName) { this.lastName = lastName; }
	public String getEmail() { return email; }
	public void setEmail(String email) { this.email = email; }
	public String getLogin() { return login; }
	public void setLogin(String login) { this.login = login; }
	public String getMobilePhone() { return mobilePhone; }
	public void setMobilePhone(String mobilePhone) { this.mobilePhone = mobilePhone; }

	@Override
	public String toString() {
		return "Profile [firstName=" + firstName + ", lastName=" + lastName + ", email=" + email + ", login=" + login
				+ ", mobilePhone=" + mobilePhone + "]";
	}


}
