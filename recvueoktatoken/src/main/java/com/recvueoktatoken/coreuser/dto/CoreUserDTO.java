package com.recvueoktatoken.coreuser.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;

import com.fasterxml.jackson.annotation.JsonInclude;

//@JsonFilter("CoreUserFilter")
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreUserDTO implements Serializable {
	
	private static final long serialVersionUID = 1L;
		
    private Long userId;
    private String userName;
    private String description;
    private Date lastLogonDate;
    private Long employeeId;
    private String emailAddress;
    private String fax;
    private Long customerId;
    private Long supplierId;
    private String fullName;
    private String title;
    private String company;
    private String city;
    private String state;
    private String country;
    private String timezone;
    private String status;
    private String currencyCode;
    private String languageCode;
    private String firstName;
    private String lastName;
    private Long tenantId;
    private Date creationDate;
    private Long createdBy;
    private Long lastUpdatedBy;
    private Date lastUpdateDate;
    private String readOnlyUser;
    private String authenticationSource;
    private String dateFormat;
    private String timeFormat;
    private String numberFormat;    
    private String defaultHomeTab;    
    private String userBoards;    
    private String defaultUIPreference;
    private String apiUser;	
	
	private List<CoreRoleDto> coreRoleDto;
	
	private List<CoreUserAttributesDto> coreUserAttributesDto;
    
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public Date getLastLogonDate() {
		return lastLogonDate;
	}
	public void setLastLogonDate(Date lastLogonDate) {
		this.lastLogonDate = lastLogonDate;
	}
	public Long getEmployeeId() {
		return employeeId;
	}
	public void setEmployeeId(Long employeeId) {
		this.employeeId = employeeId;
	}
	public String getEmailAddress() {
		return emailAddress;
	}
	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}
	public String getFax() {
		return fax;
	}
	public void setFax(String fax) {
		this.fax = fax;
	}
	public Long getCustomerId() {
		return customerId;
	}
	public void setCustomerId(Long customerId) {
		this.customerId = customerId;
	}
	public Long getSupplierId() {
		return supplierId;
	}
	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}
	public String getFullName() {
		return fullName;
	}
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getCompany() {
		return company;
	}
	public void setCompany(String company) {
		this.company = company;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getCountry() {
		return country;
	}
	public void setCountry(String country) {
		this.country = country;
	}
	public String getTimezone() {
		return timezone;
	}
	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getCurrencyCode() {
		return currencyCode;
	}
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	public String getLanguageCode() {
		return languageCode;
	}
	public void setLanguageCode(String languageCode) {
		this.languageCode = languageCode;
	}
	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public Long getTenantId() {
		return tenantId;
	}
	public void setTenantId(Long tenantId) {
		this.tenantId = tenantId;
	}
	public Date getCreationDate() {
		return creationDate;
	}
	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}
	public Long getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(Long createdBy) {
		this.createdBy = createdBy;
	}
	public Long getLastUpdatedBy() {
		return lastUpdatedBy;
	}
	public void setLastUpdatedBy(Long lastUpdatedBy) {
		this.lastUpdatedBy = lastUpdatedBy;
	}
	public Date getLastUpdateDate() {
		return lastUpdateDate;
	}
	public void setLastUpdateDate(Date lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}
	public String getReadOnlyUser() {
		return readOnlyUser;
	}
	public void setReadOnlyUser(String readOnlyUser) {
		this.readOnlyUser = readOnlyUser;
	}
	public List<CoreRoleDto> getCoreRoleDto() {
		return coreRoleDto;
	}
	public void setCoreRoleDto(List<CoreRoleDto> coreRoleDto) {
		this.coreRoleDto = coreRoleDto;
	}
	
	public List<CoreUserAttributesDto> getCoreUserAttributesDto() {
		return coreUserAttributesDto;
	}
	public void setCoreUserAttributesDto(List<CoreUserAttributesDto> coreUserAttributesDto) {
		this.coreUserAttributesDto = coreUserAttributesDto;
	}
	public String getAuthenticationSource() {
		return authenticationSource;
	}
	public void setAuthenticationSource(String authenticationSource) {
		this.authenticationSource = authenticationSource;
	}
	
	public String getDateFormat() {
		return dateFormat;
	}
	public void setDateFormat(String dateFormat) {
		this.dateFormat = dateFormat;
	}
	
	public String getTimeFormat() {
		return timeFormat;
	}
	public void setTimeFormat(String timeFormat) {
		this.timeFormat = timeFormat;
	}
	
	public String getNumberFormat() {
		return numberFormat;
	}
	public void setNumberFormat(String numberFormat) {
		this.numberFormat = numberFormat;
	}
	public String getDefaultHomeTab() {
		return defaultHomeTab;
	}
	public void setDefaultHomeTab(String defaultHomeTab) {
		this.defaultHomeTab = defaultHomeTab;
	}
	public String getUserBoards() {
		return userBoards;
	}
	public void setUserBoards(String userBoards) {
		this.userBoards = userBoards;
	}
	public String getDefaultUIPreference() {
		return defaultUIPreference;
	}
	public void setDefaultUIPreference(String defaultUIPreference) {
		this.defaultUIPreference = defaultUIPreference;
	}
	
	public String getApiUser() {
		return apiUser;
	}
	public void setApiUser(String apiUser) {
		this.apiUser = apiUser;
	}
	
	

}
