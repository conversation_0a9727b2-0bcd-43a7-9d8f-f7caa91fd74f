/**
 * 
 */
package com.recvueoktatoken.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OktaGroup {
	private String id;
	private String type;
	private OktaGroupProfile profile;

	public OktaGroup() {
		
	}

	public OktaGroup(String id, String type, OktaGroupProfile profile) {
		this.id = id;
		this.type = type;
		this.profile = profile;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public OktaGroupProfile getProfile() {
		return profile;
	}

	public void setProfile(OktaGroupProfile profile) {
		this.profile = profile;
	}

	public class OktaGroupProfile {
		private String name;
		private String description;

		public OktaGroupProfile() {

		}

		public OktaGroupProfile(String name, String description) {

			this.name = name;
			this.description = description;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public String getDescription() {
			return description;
		}

		public void setDescription(String description) {
			this.description = description;
		}
	}

}
