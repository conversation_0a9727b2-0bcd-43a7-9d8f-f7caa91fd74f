/**
 * 
 */
package com.recvueoktatoken.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OktaUser {
	private String id;
	private String status;
	private UserProfile profile;

	public OktaUser() {
	}

	public OktaUser(String id, String status, UserProfile profile) {
		this.id = id;
		this.status = status;
		this.profile = profile;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public UserProfile getProfile() {
		return profile;
	}

	public void setProfile(UserProfile profile) {
		this.profile = profile;
	}
	
	@JsonIgnoreProperties(ignoreUnknown = true)
	public class UserProfile {
		private String login;
		private String firstName;
		private String lastName;

		public UserProfile() {
		}

		public UserProfile(String login, String firstName, String lastName) {
			this.login = login;
			this.firstName = firstName;
			this.lastName = lastName;
		}

		public String getLogin() {
			return login;
		}

		public void setLogin(String login) {
			this.login = login;
		}

		public String getFirstName() {
			return firstName;
		}

		public void setFirstName(String firstName) {
			this.firstName = firstName;
		}

		public String getLastName() {
			return lastName;
		}

		public void setLastName(String lastName) {
			this.lastName = lastName;
		}

	}

	@Override
	public int hashCode() {
		// TODO Auto-generated method stub
		return super.hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		OktaUser newUser = (OktaUser) obj;
		if(id.equals(newUser.getId())) {
			return true;
		}else {
			return false;
		}
	}
	
	
	
	
	
}
