/**
 * 
 */
package com.recvueoktatoken.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OktaUserAuthResponse {
	private String status;
	private String sessionToken;
	private Embedded _embedded;

	public OktaUserAuthResponse() {
	}

	public OktaUserAuthResponse(String status, String sessionToken, Embedded _embedded) {
		this.status = status;
		this.sessionToken = sessionToken;
		this._embedded = _embedded;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getSessionToken() {
		return sessionToken;
	}

	public void setSessionToken(String sessionToken) {
		this.sessionToken = sessionToken;
	}

	public Embedded get_embedded() {
		return _embedded;
	}

	public void set_embedded(Embedded _embedded) {
		this._embedded = _embedded;
	}

	public class Embedded {
		private OktaUser user;

		public Embedded() {
		}

		public Embedded(OktaUser user) {
			this.user = user;
		}

		public OktaUser getUser() {
			return user;
		}

		public void setUser(OktaUser user) {
			this.user = user;
		}

	}

}
