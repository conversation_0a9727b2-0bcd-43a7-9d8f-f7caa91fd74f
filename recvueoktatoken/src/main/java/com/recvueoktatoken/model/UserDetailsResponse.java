/**
 * 
 */
package com.recvueoktatoken.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class UserDetailsResponse {
	String userName;
	String organization;

	public UserDetailsResponse() {

	}

	public String getOrganization() {
		return organization;
	}

	public void setOrganization(String organization) {
		this.organization = organization;
	}

	public String getUsername() {
		return userName;
	}

	public void setUsername(String username) {
		this.userName = username;
	}


	@Override
	public String toString() {
		// TODO Auto-generated method stub
		return ("username="+userName+" organization="+organization);
	}
	
	

}