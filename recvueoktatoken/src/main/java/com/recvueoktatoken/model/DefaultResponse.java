package com.recvueoktatoken.model;

import java.io.Serializable;

import javax.validation.Valid;

import io.swagger.annotations.ApiModelProperty;

public class DefaultResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	
	protected Long id;
	protected int statusCode;
	protected String message;

	/**
	 * @return the id
	 */

	public Long getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * 2000 - Success Status Code 2001 - Failure Status Code    2002 - Warning Status Code
	 * @return statusCode
	**/
	@ApiModelProperty(required=true, example = "200", value = "200 - Success Status Code")
	public int getStatusCode() {
		return statusCode;
	}

	/**
	 * @param statusCode the statusCode to set
	 */
	public void setStatusCode(int statusCode) {
		this.statusCode = statusCode;
	}

	/**
	 * @return the message
	 */
	@ApiModelProperty(value = "Success Message or Error Message.",required=true, allowEmptyValue=false)
	public String getMessage() {
		return message;
	}

	/**
	 * @param message the message to set
	 */
	public void setMessage(String message) {
		this.message = message;
	}

}