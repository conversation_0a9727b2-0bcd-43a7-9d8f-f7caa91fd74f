/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.recvueoktatoken.model;

import java.util.List;

import com.recvueoktatoken.common.entity.UserAttributes;

public class UserAttributesResponse  {
    
	private Long userId;
	private Long tenantId;
    private String userName;
    private String email;
    private String fullName;
    private String defaultHomeTab;
    private String userBoards;
    private String defaultUIPreference;
    private String timeZone;
    private String currencyCode;
    private String dateFormat;
    private String numberFormat;
    private String timeFormat;
    private String readOnlyUser;
    private String defaultDashboard;
    
    private List<RolesResponse> roles;
    
    private List<UserAttributes> userAttributes;
    
    

    public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getTenantId() {
		return tenantId;
	}

	public void setTenantId(Long tenantId) {
		this.tenantId = tenantId;
	}

	public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public List<UserAttributes> getUserAttributes() {
        return userAttributes;
    }

    public void setUserAttributes(List<UserAttributes> userAttributes) {
        this.userAttributes = userAttributes;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

	public List<RolesResponse> getRoles() {
		return roles;
	}

	public void setRoles(List<RolesResponse> roles) {
		this.roles = roles;
	}
	
	/**
	 * @return the defaultHomeTab
	 */
	public String getDefaultHomeTab() {
		return defaultHomeTab;
	}

	/**
	 * @param defaultHomeTab the defaultHomeTab to set
	 */
	public void setDefaultHomeTab(String defaultHomeTab) {
		this.defaultHomeTab = defaultHomeTab;
	}

	/**
	 * @return the userBoards
	 */
	public String getUserBoards() {
		return userBoards;
	}

	/**
	 * @param userBoards the userBoards to set
	 */
	public void setUserBoards(String userBoards) {
		this.userBoards = userBoards;
	}

	/**
	 * @return the defaultUIPreference
	 */
	public String getDefaultUIPreference() {
		return defaultUIPreference;
	}

	/**
	 * @param defaultUIPreference the defaultUIPreference to set
	 */
	public void setDefaultUIPreference(String defaultUIPreference) {
		this.defaultUIPreference = defaultUIPreference;
	}


	/**
	 * @return the timeZone
	 */
	public String getTimeZone() {
		return timeZone;
	}


	/**
	 * @param timeZone the timeZone to set
	 */
	public void setTimeZone(String timeZone) {
		this.timeZone = timeZone;
	}

	
	/**
	 * @return the CurrencyCode
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}

	/**
	 * @param timeZone the CurrencyCode to set
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	/**
	 * 
	 * @return the timeFormat
	 */
	public String getTimeFormat() {
		return timeFormat;
	}

	/**
	 * 
	 * @param timeFormat the timeFormat to set
	 */
	public void setTimeFormat(String timeFormat) {
		this.timeFormat = timeFormat;
	}

	/**
	 * 
	 * @return the readOnlyUser
	 */
	public String getReadOnlyUser() {
		return readOnlyUser;
	}
	
	
	/**
	 * 
	 * @param readOnlyUser the readOnlyUser to set
	 */
	public void setReadOnlyUser(String readOnlyUser) {
		this.readOnlyUser = readOnlyUser;
	}	
	
	/**
	 * 
	 * @return the dateFormat
	 */
	public String getDateFormat() {
		return dateFormat;
	}

	/**
	 * 
	 * @param dateFormat the dateFormat to set
	 */
	public void setDateFormat(String dateFormat) {
		this.dateFormat = dateFormat;
	}

	/**
	 * 
	 * @return the numberFormat
	 */
	public String getNumberFormat() {
		return numberFormat;
	}

	/**
	 * 
	 * @param numberFormat the numberFormat to set
	 */
	public void setNumberFormat(String numberFormat) {
		this.numberFormat = numberFormat;
	}
	
	public String getDefaultDashboard() {
		return defaultDashboard;
	}

	public void setDefaultDashboard(String defaultDashboard) {
		this.defaultDashboard = defaultDashboard;
	}
}
