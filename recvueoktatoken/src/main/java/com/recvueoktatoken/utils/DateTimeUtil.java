package com.recvueoktatoken.utils;


import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;
import org.slf4j.LoggerFactory;

import com.recvueoktatoken.common.constants.Constants;

/**
 *
 * <AUTHOR>
 */
public class DateTimeUtil {

    private static final org.slf4j.Logger LOG = LoggerFactory.getLogger(DateTimeUtil.class);

    /**
     * Converts a date string to a given format
     *
     * @param dateStr
     * @return
     */
    public static String convertDateToDDMMMYYYYFormat(String dateStr) {
        SimpleDateFormat fromFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss.u");
        SimpleDateFormat toFormat = new SimpleDateFormat("dd-MMM-yyyy");
        Date date;
        String toDate = dateStr;
        try {
            date = fromFormat.parse(dateStr);
            toDate = toFormat.format(date);
        } catch (ParseException ex) {
            LOG.error("convertDateToDDMMMYYYYFormat ParseException", ex);
        }
        return toDate;
    }

    /**
     * Get date in specified format
     *
     * @param format
     * @param dateStr
     * @return
     */
    public static Date getDateWithFormat(String format, String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date res = null;
        try {
            res = sdf.parse(dateStr);
        } catch (ParseException ex) {
            LOG.error("getDateWithFormat ParseException", ex);
        }
        LOG.trace("res from date parsed to format: " + res);
        return res;
    }

    /**
     * param dbDate, should be always in UTC timezone, as per current database
     * timezone.
     *
     * @param userTmezoneFromDb
     * @param dbDate
     * @return
     */
    public static String getTimeForUserTZ(String userTmezoneFromDb, Date dbDate) {
        String dateInUserTZ = "";
        if (dbDate != null) {
            TimeZone fromTZ = TimeZone.getTimeZone(Constants.UTC_TIMEZONE);
            TimeZone toTZ;
            DateFormat formatter = new SimpleDateFormat("dd-MMM-yyyy HH:mm:ss a zzz");
            formatter.setTimeZone(fromTZ);
            formatter.format(dbDate);
            if (userTmezoneFromDb != null) {
                toTZ = TimeZone.getTimeZone(userTmezoneFromDb);
            } else {
                toTZ = TimeZone.getTimeZone(Constants.UTC_TIMEZONE);
            }
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(dbDate);
            calendar.setTimeZone(toTZ);
            formatter.setTimeZone(toTZ);
            dateInUserTZ = formatter.format(calendar.getTime());
        }
        return dateInUserTZ;
    }

    /**
     * Get date in specified format but throw exception
     *
     * @param format
     * @param dateStr
     * @return
     */
    public static Date getDateWithFormatThrowEx(String format, String dateStr) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date res = null;
        try {
            res = sdf.parse(dateStr);
        } catch (ParseException ex) {
            LOG.error("getDateWithFormat ParseException", ex);
            throw ex;
        }
        LOG.debug("res from date parsed to format: " + res);
        return res;
    }

    /**
     * This method is used to convert the given date to given format.
     *
     * @param dateStr This is the date String to be converted to required
     * format.
     * @param format This is format String to which format dateStr should
     * change.
     * @return Date The converted date object will be returned.
     */
    public Date strToDate(String dateStr, String format) {
        DateFormat df = new SimpleDateFormat(format);
        Date startDate;
        try {
            startDate = df.parse(dateStr);
        } catch (ParseException parseException) {
            LOG.debug("Given date is not in the expected format."
                    + "Date convertion is failed due to: "
                    + parseException.getMessage());
            startDate = null;
        }
        return startDate;
    }

    /**
     * Return current timestamp - mainly for log file naming
     *
     * @return
     */
    public static String getCurrentTimeStamp() {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
        return dateFormat.format(date);
    }
    
     /**
     * Validate Date format YYYY-MM-DD and returns date
     *
     * @param dateStr
     * @return
     * @throws CustomException
     */
    public static Date validateStrDateFormat(String dateStr) throws Exception {
        DateUtil dateUtil = new DateUtil();
        if (dateStr == null || dateStr.length() == 0) {
            return null;
        } else {
            boolean valid = dateUtil.verifyDateFormat(dateStr, Constants.YYYY_MM_DD);
            if (valid) {
                return dateUtil.strToDate(dateStr, Constants.YYYY_MM_DD);
            } else {
                throw new Exception("Given date is not in expected format.The format should be yyyy-MM-dd.");
            }
        }
    }
    
    /**
     *
     * @param dateToCheckSStart
     * @param dateToCheckEnd
     * @param startDate
     * @param endDate
     * @return
     */
    public static boolean checkBetween(Date dateToCheckSStart, Date dateToCheckEnd, Date startDate, Date endDate) {
        boolean start = (dateToCheckSStart.compareTo(startDate) >= 0 && dateToCheckSStart.compareTo(endDate) <= 0);
        boolean end = (dateToCheckEnd.compareTo(startDate) >= 0 && dateToCheckEnd.compareTo(endDate) <= 0);
        boolean beyondstartcheck = (dateToCheckSStart.compareTo(startDate) <= 0);
        boolean beyondenddcheck = (dateToCheckEnd.compareTo(endDate) >= 0);
        LOG.debug("start :: " + start + " :: end::: " + end);
        LOG.debug("beyondstartcheck :: " + beyondstartcheck + " :: beyondenddcheck::: " + beyondenddcheck);
        return (start || end) || (beyondstartcheck && beyondenddcheck);
    }

    /**
     * Change DATE format in given format
     *
     * @param date
     * @param toformat
     * @return
     * @throws ParseException
     */
    public static Date changeDateFormat(Date date, String toformat) throws ParseException {
        if (date != null) {
            SimpleDateFormat formatter = new SimpleDateFormat(toformat);
            String newDateStr = formatter.format(date);
            return formatter.parse(newDateStr);
        } else {
            return null;
        }
    }

}
