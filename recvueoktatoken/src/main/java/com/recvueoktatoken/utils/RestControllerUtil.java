package com.recvueoktatoken.utils;


import com.recvueoktatoken.common.constants.RestConstants;
import com.recvueoktatoken.model.DefaultResponse;
import org.springframework.stereotype.Component;

@Component
public class RestControllerUtil {
	/**
	 * Returns default response object for success condition
	 *
	 * @param errorMsg
	 * @return
	 */
	public DefaultResponse createFailedResponse(Long id, int statusCode, String errorMsg) {
		DefaultResponse defaultResponse = new DefaultResponse();
		defaultResponse.setId(id);
		defaultResponse.setStatusCode(statusCode);
		defaultResponse.setMessage(errorMsg);
		return defaultResponse;
	}
	
	/**
	 * Returns default response object for success condition
	 *
	 * @param id
	 * @param successMsg
	 */
	public DefaultResponse createSuccessResponse(Long id, int statusCode, String successMsg) {
		String successMessage = null == successMsg ? RestConstants.SUCESS_MESSGE : successMsg;
		DefaultResponse defaultResponse = new DefaultResponse();
		defaultResponse.setId(id);
		defaultResponse.setStatusCode(statusCode);
		defaultResponse.setMessage(successMessage);
		return defaultResponse;
	}
	
	/**
	 * Returns default response object for success condition
	 *
	 * @param id
	 * @param successMsg
	 */
	public DefaultResponse createSuccessResponse(int statusCode, String successMsg) {
		String successMessage = null == successMsg ? RestConstants.SUCESS_MESSGE : successMsg;
		DefaultResponse defaultResponse = new DefaultResponse();		
		defaultResponse.setStatusCode(statusCode);
		defaultResponse.setMessage(successMessage);
		return defaultResponse;
	}
	
}
