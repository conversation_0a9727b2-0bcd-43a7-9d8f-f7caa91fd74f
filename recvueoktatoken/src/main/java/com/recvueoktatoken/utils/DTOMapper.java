package com.recvueoktatoken.utils;


import java.util.List;
import java.util.stream.Collectors;

import org.modelmapper.ModelMapper;
import org.modelmapper.config.Configuration.AccessLevel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.recvueoktatoken.common.entity.CoreUser;
import com.recvueoktatoken.coreuser.dto.CorePermissionDto;
import com.recvueoktatoken.coreuser.dto.CoreRoleDto;
import com.recvueoktatoken.coreuser.dto.CoreUserAttributesDto;
import com.recvueoktatoken.coreuser.dto.CoreUserDTO;

public class DTOMapper {

	private static final ModelMapper modelMapper;

	static {
		modelMapper = new ModelMapper();
		modelMapper.getConfiguration()
		.setFieldMatchingEnabled(true)
		.setFieldAccessLevel(AccessLevel.PRIVATE)
		.setSkipNullEnabled(true); // THIS avoids mapping null values
	}
	private static final Logger logger = LoggerFactory.getLogger(DTOMapper.class);
	
	
	public static List<CoreUserDTO> mapCoreUserListBasic(List<CoreUser> users) {
	    return users.stream()
	            .map(user -> {
	                CoreUserDTO dto = modelMapper.map(user, CoreUserDTO.class);

	                // Skip nested roles and attributes
	                dto.setCoreRoleDto(null);
	                dto.setCoreUserAttributesDto(null);

	                return dto;
	            })
	            .collect(Collectors.toList());
	}	

	public static List<CoreUserDTO> mapCoreUserList(List<CoreUser> users) {
		return users.stream().map(DTOMapper::mapCoreUser).collect(Collectors.toList());
	}
	
	
	private static CoreUserDTO mapCoreUser(CoreUser user) {
        CoreUserDTO dto = modelMapper.map(user, CoreUserDTO.class);

        // Map Set<CoreRole> → Set<CoreRoleDto>
        List<CoreRoleDto> roles = user.getCoreRoles().stream()
                .map(role -> modelMapper.map(role, CoreRoleDto.class))
                .collect(Collectors.toList());
        dto.setCoreRoleDto(roles);

        // Map List<CoreUserAttributes> → List<CoreUserAttributesDto>
        List<CoreUserAttributesDto> attributes = user.getCoreUserAttributes().stream()
                .map(attr -> modelMapper.map(attr, CoreUserAttributesDto.class))
                .collect(Collectors.toList());
        dto.setCoreUserAttributesDto(attributes);
        return dto;
    }
	
	

	private static CoreUserDTO mapCoreUserwithPermissions(CoreUser user) {

		CoreUserDTO dto = modelMapper.map(user, CoreUserDTO.class);

		// Map CoreRoles
		List<CoreRoleDto> roles = user.getCoreRoles().stream()
				.map(role -> {
					CoreRoleDto roleDto = modelMapper.map(role, CoreRoleDto.class);

					// Map CorePermissions inside each role
					if (role.getCorePermissions() != null) {
						List<CorePermissionDto> permissions = role.getCorePermissions().stream()
								.map(permission -> modelMapper.map(permission, CorePermissionDto.class))
								.collect(Collectors.toList());
						roleDto.setCorePermissionDto(permissions);
					}
					return roleDto;
				})
				.collect(Collectors.toList());
		dto.setCoreRoleDto(roles);

			        // Map CoreUserAttributes
			        if (user.getCoreUserAttributes() != null) {
			            List<CoreUserAttributesDto> attributes = user.getCoreUserAttributes().stream()
			                    .map(attr -> modelMapper.map(attr, CoreUserAttributesDto.class))
			                    .collect(Collectors.toList());
			            dto.setCoreUserAttributesDto(attributes);
			        }

		return dto;

	}
}