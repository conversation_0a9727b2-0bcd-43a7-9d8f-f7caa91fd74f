//package com.recvueoktatoken.utils;
//
//import java.time.Duration;
//import java.util.HashMap;
//import java.util.function.Supplier;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import com.recvueoktatoken.authorizer.request.Constants;
//import com.recvueoktatoken.authorizer.request.IdmTokenValidations;
//
//import io.github.bucket4j.Bandwidth;
//import io.github.bucket4j.Bucket;
//import io.github.bucket4j.BucketConfiguration;
//import io.github.bucket4j.Refill;
//import io.github.bucket4j.distributed.proxy.ProxyManager;
//
//@Service
//public class RateLimiter {
//	// autowiring dependencies
//	@Autowired
//	public ProxyManager<String> buckets;
//
//	private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
//
//	public Bucket resolveBucket(String key) {
//		Supplier<BucketConfiguration> configSupplier = getConfigSupplierForUser(key);
//
//		// Does not always create a new bucket, but instead returns the existing one if
//		// it exists.
//		return buckets.builder().build(key, configSupplier);
//	}
//
//	private Supplier<BucketConfiguration> getConfigSupplierForUser(String key) {
//		key = key.replace(".recvue.com", "");
//		long tps = (long) ((HashMap<String, Object>) IdmTokenValidations.tokensMap.get(key)).get(Constants.tps);
//		LOGGER.debug("Customer : " + key + ", TPS: " + tps);
//		Refill refill = Refill.intervally(tps, Duration.ofSeconds(1));
//		Bandwidth limit = Bandwidth.classic(tps, refill);
//		return () -> (BucketConfiguration.builder().addLimit(limit).build());
//	}
//}