/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.utils;


import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * Date format validator to support custom javax DateFormat constraint
 * <AUTHOR>
 */
public class DateFormatValidatorForString implements ConstraintValidator<DateFormat, String> {

	String[] dateFormat = {" "};
	DateUtil dateUtil;

	@Override
	public void initialize(DateFormat constraintAnnotation) {
		dateFormat=constraintAnnotation.dateFormat();
		dateUtil = new DateUtil();
	}

	@Override
	public boolean isValid(String date, ConstraintValidatorContext cvc) {
		return validate(date);
	}

	/**
	 * Validate date string
	 */
	private boolean validate(String dateString){
		boolean valid = false;
		//leave null-checking to @NotNull on individual parameters
		if(dateString == null || dateString.length()==0){
			return true;
		}

		for(String format:dateFormat){
			if(null!=format && format.equalsIgnoreCase("yyyy-MM-dd")){
				valid = dateUtil.verifyDateFormat(dateString,"yyyy-MM-dd");
			}
		}
		return valid;
	}

}
