package com.recvueoktatoken.utils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import org.slf4j.LoggerFactory;

/**
* <h1>DateUtil! to handle the date in different formats in project</h1>
* <AUTHOR> KUMAR T
*/
public class DateUtil {
 private static final org.slf4j.Logger LOG = LoggerFactory.getLogger(DateUtil.class);
 
    /**
     * This method is used to convert the given date to given format.
     *
     * @param dateStr This is the date String to be converted to required
     * format.
     * @param format This is format String to which format dateStr should
     * change.
     * @return Date The converted date object will be returned.
     */
    public Date strToDate(String dateStr, String format) {
        DateFormat df = new SimpleDateFormat(format);
        Date startDate;
        try {
            startDate = df.parse(dateStr);
        } catch (ParseException parseException) {
            LOG.debug("Given date is not in the expected format."
                    + "Date convertion is failed due to: "
                    + parseException.getMessage());
            startDate = null;
        }
        return startDate;
    }

    /**
     * This method is used to verify the given date in the given format or not.
     *
     * @param dateStr This is the date String to be converted to required
     * format.
     * @param format This is format String to which format dateStr should
     * change.
     * @return boolean.
     */
    public boolean verifyDateFormat(String dateStr, String format) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        simpleDateFormat.setLenient(false);
        if (dateStr != null) {
            try {
            	simpleDateFormat.setLenient(false);
                Date parsedDate = simpleDateFormat.parse(dateStr.trim());
                if (simpleDateFormat.format(parsedDate).equals(dateStr.trim())) {
                    return true;
                } else {
                    return false;
                }
            } catch (ParseException parseException) {
                LOG.debug("Given date is not in the expected format."
                        + "Date convertion is failed due to: "
                        + parseException.getMessage());
                return false;
            }
        }
        return false;
    }
    
    /*to convert the given date to java.util.Date.(format:MM/dd/yyyy hh:mm:ss)*/
    public Date convertToJavaUtilDate(String givenDate) {
        DateFormat df = new SimpleDateFormat("MM/dd/yyyy hh:mm:ss");
        Date javaDate = null;
        try {
            javaDate = df.parse(givenDate);
        } catch (ParseException e) {
            LOG.debug("Given date is failed to convert as Java util Date : "                       
                        + e.getMessage());
        }
        return javaDate;
    }
    
    /**
     * Utility method to check the date is last day of the month
     * @param d
     * @return 
     */
    public static boolean isLastDayMonth(Date d) {
        if(null != d) {
            return false;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DATE, -1);
        Date lastDayOfMonth = calendar.getTime();
        return lastDayOfMonth.equals(d);
    }
}
