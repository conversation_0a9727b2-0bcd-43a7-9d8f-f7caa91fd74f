/**
 * 
 */
package com.recvueoktatoken.utils;

import java.util.Base64;

import org.springframework.stereotype.Component;

@Component
public class DecodeHeader {
	public String[] decodeAuthorization(String authString) throws Exception {
		
		if (authString == null || "".equals(authString)) {
            throw new Exception("Authorization header value is null"); 
		}
		String decodedAuth = "";
        byte[] bytes = null;
        bytes =  Base64.getDecoder().decode(authString);
        decodedAuth = new String(bytes);
//        System.out.println("decodeAuthorization - decodedAuth:" + decodedAuth);
        String[] tokens = decodedAuth.split(":");
        if (tokens.length != 2) {
//            LOG.debug("decodeAuthorization - Username Password token values are null : " + tokens.length);
            throw new Exception("Username Password token values are null");
        }
        return tokens;
	}
}
