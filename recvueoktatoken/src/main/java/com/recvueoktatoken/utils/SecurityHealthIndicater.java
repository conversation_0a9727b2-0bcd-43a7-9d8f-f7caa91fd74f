package com.recvueoktatoken.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import com.recvueoktatoken.common.entity.User;
import com.recvueoktatoken.common.service.ApiAuthUserService;


@Component
public class SecurityHealthIndicater implements HealthIndicator {
	
	static Logger logger = LoggerFactory.getLogger(SecurityHealthIndicater.class);
	
	@Autowired
	protected ApiAuthUserService apiAuthService;
	
	@Override
	public Health health() {
		try {
			logger.debug("Health Check API:  inside custom health check method");
			Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
			if("anonymousUser".equalsIgnoreCase(authentication.getPrincipal().toString())){
				logger.debug("Health Check API:  Token not found or Anonymous User");
				return Health.up().withDetail("Recvue", "Available").build();
			}else {				
				User u = (User) authentication.getPrincipal();
				User user = apiAuthService.getUserByName(u.getUsername());
				if (user == null) {
					logger.debug("Health Check API:  Invalid User");
					return Health.down().withDetail("Recvue", "Not Available").build();
				} else {
					logger.info("Health Check API:  Service is running");
					return Health.up().withDetail("Recvue", "Available").build();
				}
			}
			
		}catch (Exception e) {
			logger.error("Health Check API: Exception occured in custom health check method : "+e.getMessage());
			return null;
		}
	}

}
