/**
 * 
 */
package com.recvueoktatoken.utils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.recvueoktatoken.model.OktaGroup;
import com.recvueoktatoken.model.OktaUser;
import com.recvueoktatoken.model.OktaUserAuthResponse;
import com.recvueoktatoken.model.Response;

@Component
public class OktaUtils {

	private final Logger LOG = LoggerFactory.getLogger(this.getClass());
	
	@Value("${recvue.okta.url}")
	private String RECVUE_OKTA_URL;

	@Value("${recvue.okta.apiKey}")
	private String RECVUE_OKTA_APIKEY;

	@Value("${recvue.okta.userUrl}")
	private String RECVUE_OKTA_USERURL;

	@Value("${recvue.okta.clientId}")
	private String RECVUE_OKTA_CLIENTID;

	private String redirectUrl;

	@Autowired
	RedisTemplate<String, Response> redisTemplate;
	/*
	 * Function to get validate, get sessionToken and then get accessToken
	 */

	public Response getUserAccessToken(String[] userCredentials, String hostname) throws Exception {
		//Ramky Cache changes Start
		Response accesstokenResponse = null;
		try {
			LOG.info("Accesstoken fetch first from cache");
			accesstokenResponse =  redisTemplate.opsForValue().get("oauth:"+hostname+":"+userCredentials[0]);
			if(accesstokenResponse!=null) {
				Calendar now = Calendar.getInstance();
				DateFormat p= new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy");
				now.setTime(p.parse(accesstokenResponse.getExpiration_date()));
				if(Calendar.getInstance().getTimeInMillis() > now.getTimeInMillis()) {
					accesstokenResponse=null;
				}else {
					LOG.info("Accesstoken fetched from cache");
					return accesstokenResponse;
				}
			}
		}catch(RedisConnectionFailureException ex) {
			LOG.error("Cache:" + ex.getMostSpecificCause().toString());
			StringWriter sw = new StringWriter();
			PrintWriter pw = new PrintWriter(sw);
			ex.printStackTrace(pw);
			LOG.error("Cache:" +sw.toString());
		}catch(Exception | Error ex) {
			LOG.error("Cache:" + ex.getMessage());
			StringWriter sw = new StringWriter();
			PrintWriter pw = new PrintWriter(sw);
			ex.printStackTrace(pw);
			LOG.error("Cache:" +sw.toString());
		}
		//Ramky Cache changes end
		LOG.info("Invoking AuthnCall");
		OktaUserAuthResponse response = getUserAuthResponseByCredentials(userCredentials);
		LOG.info("Completed AuthnCall");
		if (response == null || response.getSessionToken() == null) {
			LOG.info("User not authenticated");
			return null;
		} else {

			/*
			 * commenting code to get user Okta groups
			 * 
			 * List<OktaGroup> oktaGroups =
			 * getOktaGroupsByUser(response.get_embedded().getUser()); if (oktaGroups ==
			 * null || oktaGroups.size() == 0) { return null; }
			 */

			LOG.info("User authenticated");
			LOG.info("User :" + response.get_embedded().getUser().getProfile().getFirstName());
			redirectUrl = "http://localhost:4200";
		
			//Ramky Cache changes Start
			Response tokenresponse = getOktaAccessTokenFromSessionToken(response.getSessionToken());
			try {
				if(tokenresponse!=null) {
					LOG.info("Store accesstoken in cache");
					redisTemplate.opsForValue().set("oauth:"+hostname+":"+userCredentials[0], tokenresponse);
					LOG.info("Stored accesstoken in cache");
				}
			}catch(RedisConnectionFailureException ex) {
				LOG.error("Cache:" + ex.getMostSpecificCause().toString());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				ex.printStackTrace(pw);
				LOG.error("Cache:" +sw.toString());
			}catch(Exception | Error ex) {
				LOG.error("Cache:" + ex.getMessage());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				ex.printStackTrace(pw);
				LOG.error("Cache:" +sw.toString());
			}
			return tokenresponse;
			//Ramky Cache changes end
			
//			return getOktaAccessTokenFromSessionToken(response.getSessionToken());
			/*
			 * commenting code to get match between calling tenant and user Okta groups
			 * 
			 * if (isServerNameInUserGroups(serverName, oktaGroups)) { //
			 * redirectUrl=serverName; redirectUrl="http://localhost:4200"; return
			 * getOktaAccessTokenFromSessionToken(response.getSessionToken()); } else {
			 * return null; }
			 */

		}
	}

	/*
	 * Function that validates user
	 */

	public OktaUserAuthResponse getUserAuthResponseByCredentials(String[] userCredentials) throws Exception {

		// set body
		ObjectMapper mapper = new ObjectMapper();
		JsonNode request = mapper.createObjectNode();
		((ObjectNode) request).put("username", userCredentials[0]);
		((ObjectNode) request).put("password", userCredentials[1]);

		// set headers
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<String> entity = new HttpEntity<String>(request.toString(), headers);

		try {
			// send request and parse result
			RestTemplate restTemplate = new RestTemplate();
			ResponseEntity<String> loginResponse = restTemplate.exchange(RECVUE_OKTA_URL + RECVUE_OKTA_USERURL,
					HttpMethod.POST, entity, String.class);
			// LOG.info("OktaUserAuthResponse : " + loginResponse.getBody());

			OktaUserAuthResponse response = mapper.readValue(loginResponse.getBody(), OktaUserAuthResponse.class);
			return (response);
		} catch (Exception e) {
			LOG.info("Error :" + "getOktaUserAuthResponse :" + e.getMessage());
			return null;
		}

	}

	/*
	 * Function to get Okta groups of the user
	 */

	public List<OktaGroup> getOktaGroupsByUser(OktaUser user) {
		// set headers
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.add("Authorization", "SSWS" + RECVUE_OKTA_APIKEY);
		HttpEntity<String> entity = new HttpEntity<String>(headers);
		try {
			RestTemplate restTemplate = new RestTemplate();
			String oktaUserGroupRequestUrl = RECVUE_OKTA_URL + "/api/v1/users/" + user.getId() + "/groups";

			ResponseEntity<String> getGroupsResponse = restTemplate.exchange(oktaUserGroupRequestUrl, HttpMethod.GET,
					entity, String.class);
			ObjectMapper mapper = new ObjectMapper();
			List<OktaGroup> oktaGroups = mapper.readValue(getGroupsResponse.getBody(),
					new TypeReference<List<OktaGroup>>() {
					});
			// LOG.info("Total Groups " + oktaGroups.size());
			return oktaGroups;
		} catch (Exception e) {
			LOG.info("Error :" + "getUserGroups :" + e.getMessage());
			return null;
		}

	}

	/*
	 * Function to match serverName with user Okta groups
	 */

	public boolean isServerNameInUserGroups(String serverName, List<OktaGroup> oktaGroups) throws Exception {
		for (OktaGroup oktaGroup : oktaGroups) {
			// LOG.info("Group Id" + oktaGroup.getId());
			// LOG.info("Group Name " + oktaGroup.getProfile().getName());
			if (oktaGroup.getProfile().getName().startsWith(serverName + "@")) {
				LOG.info("User found in :" + oktaGroup.getProfile().getName());
				return true;
			}
		}
		return false;
	}

	/*
	 * Function to get accessToken from the sessionToken
	 */

	public Response getOktaAccessTokenFromSessionToken(String oktaSessionToken) {

		String oktaAuthorizeURL = RECVUE_OKTA_URL + "/oauth2/v1/authorize?";

		UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(oktaAuthorizeURL);
		builder.queryParam("client_id", RECVUE_OKTA_CLIENTID);
		builder.queryParam("nonce", redirectUrl);
		builder.queryParam("redirect_uri", redirectUrl);
		builder.queryParam("response_type", "token");
		builder.queryParam("response_mode", "form_post");
		builder.queryParam("sessionToken", oktaSessionToken);
		builder.queryParam("prompt", "none");
		builder.queryParam("scope", "openid");
		builder.queryParam("state", redirectUrl);

		try {
			LOG.info("URL :" + builder.toUriString());
			RestTemplate restTemplate = new RestTemplate();
			HttpHeaders headers = new HttpHeaders();
			HttpEntity entity = new HttpEntity(headers);
			LOG.info("Invoking tokenCall");
			ResponseEntity<String> oktaAuthorizeReponse = restTemplate.exchange(builder.toUriString(), HttpMethod.GET,
					entity, String.class);
			LOG.info("Completed tokenCall");
			if (oktaAuthorizeReponse.getStatusCode() == HttpStatus.OK) {
				//LOG.info(oktaAuthorizeReponse.getBody());
				Document d = Jsoup.parse(oktaAuthorizeReponse.getBody());
				Element e1 = d.getElementsByAttributeValue("name", "access_token").first();
				if (e1 == null) {
					throw new Exception("Access Token not found in response");
				} else {
					String access_token = e1.attr("value");
					LOG.info("Access Token found in the response");
					Long expiresIn;
					Element e2 = d.getElementsByAttributeValue("name", "expires_in").first();
					if(e2==null) {
						LOG.info("expires_in element not found in the response. Setting to default 3600s");
						expiresIn=3600000L;
					}else {
						LOG.info("expires_in element found in the response");
						expiresIn =Long.valueOf(e2.attr("value"))*1000L;
					}
					
					Calendar now = Calendar.getInstance();
					now.setTimeInMillis(now.getTimeInMillis()+expiresIn);
					Response response = new Response();
					response.setAccess_token(access_token);
					response.setExpiration_date(now.getTime().toString());
					return response;
				}

			} else {
				throw new Exception(oktaAuthorizeReponse.getStatusCode().toString());
			}
		} catch (Exception e) {
			LOG.info("Error :" + "OktaAccessToken :" + e.getMessage());
			return null;
		}

	}

}
