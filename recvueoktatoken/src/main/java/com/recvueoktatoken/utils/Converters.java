package com.recvueoktatoken.utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.PropertyAccessorFactory;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.FilterProvider;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.recvueoktatoken.common.constants.Constants;

public class Converters {

	private static final Logger logger = LoggerFactory.getLogger(Converters.class);
	
	
	public  <E, D> List<?> convertEntityToDto(String filterName, String[] fields,int limit,List<E> entityList, List<D> dtoList) {		
		//slogger.debug("Entity Type: " + entityList.getClass().getName() + " DTO Type: " + dtoList.getClass().getName() );
		ObjectMapper mapper = new ObjectMapper();
		FilterProvider filters;
		if (fields == null || fields.length <= 0) {
			fields = new String[] { "*" };
			filters = new SimpleFilterProvider().addFilter(filterName, SimpleBeanPropertyFilter.serializeAll());
		} else {
			filters = new SimpleFilterProvider().addFilter(filterName,
					SimpleBeanPropertyFilter.filterOutAllExcept(fields));

		}
		SimpleDateFormat df = new SimpleDateFormat(Constants.YYYY_MM_DD);
		mapper.setFilterProvider(filters);
		mapper.setDateFormat(df);
		
//		java.util.Date defaultValue = null;
//        Converter converter = new DateConverter(defaultValue);
//        BeanUtilsBean beanUtilsBean = BeanUtilsBean.getInstance(); 
//        beanUtilsBean.getConvertUtils().register(converter, java.util.Date.class);
       

//		List<CoreLookupValue> entityList = coreLookupValueDAO.findAll(limit);
//		List<E> entityList = src ;//coreLookupValueDAO.findAll(limit);
		List<Object> result = new ArrayList<Object>();
		try {
//			for (CoreLookupValue lookupValue : entityList) {
			int i=0;
			for (E lookupValue : entityList) {	
//				LookupValueDTO lv = new LookupValueDTO();
				D lv = dtoList.get(i++);
				BeanUtils.copyProperties(lookupValue, lv);
				
				JsonNode jsonRecord = mapper.readTree(mapper.writer(filters).writeValueAsString(lv));
				result.add(jsonRecord);
			}

		} catch (Exception e) {
			e.printStackTrace();
//			throw new Exception(e.getMessage());
			logger.error(e.getMessage());
		}
		return result;
	}
	
	public  <E, D> JsonNode convertEntityToDto(String filterName, String[] fields,int limit,E entity, D dto) {		
		logger.debug("Entity Type: " + entity.getClass().getName() + " DTO Type: " + dto.getClass().getName() );
		ObjectMapper mapper = new ObjectMapper();
		FilterProvider filters;
		if (fields == null || fields.length <= 0) {
			fields = new String[] { "*" };
			filters = new SimpleFilterProvider().addFilter(filterName, SimpleBeanPropertyFilter.serializeAll());
		} else {
			filters = new SimpleFilterProvider().addFilter(filterName,
					SimpleBeanPropertyFilter.filterOutAllExcept(fields));

		}
		SimpleDateFormat df = new SimpleDateFormat(Constants.YYYY_MM_DD);
		mapper.setFilterProvider(filters);
		mapper.setDateFormat(df);
		
//		java.util.Date defaultValue = null;
//        Converter converter = new DateConverter(defaultValue);
//        BeanUtilsBean beanUtilsBean = BeanUtilsBean.getInstance(); 
//        beanUtilsBean.getConvertUtils().register(converter, java.util.Date.class);
       

//		List<CoreLookupValue> entityList = coreLookupValueDAO.findAll(limit);
//		List<E> entityList = src ;//coreLookupValueDAO.findAll(limit);
//		Object result = new Object();
		JsonNode jsonRecord=null;
		try {
//			for (CoreLookupValue lookupValue : entityList) {
			int i=0;
//			for (E lookupValue : entityList) {	
//				LookupValueDTO lv = new LookupValueDTO();
//				D lv = dtoList.get(i++);
				BeanUtils.copyProperties(entity, dto);
				
				jsonRecord = mapper.readTree(mapper.writer(filters).writeValueAsString(dto));
//				result=jsonRecord;
//			}

		} catch (Exception e) {
			e.printStackTrace();
//			throw new Exception(e.getMessage());
			logger.error(e.getMessage());
		}
		return jsonRecord;//result;
	}

	public   <E, D> E convertDtoToEntity(D dto, E entity) {
		BeanUtils.copyProperties(dto, entity);
		return entity;
	}

	public   <E, D> D convertEntityToDto(E entity,D dto) {
		BeanUtils.copyProperties(entity,dto);
		return dto;
	}
	
	public   <E, D> E convertDtoToEntity(D dto, E entity,Map<String,String> jsonReq) {

		BeanWrapper srcWrap = PropertyAccessorFactory.forBeanPropertyAccess(dto);
	    BeanWrapper trgWrap = PropertyAccessorFactory.forBeanPropertyAccess(entity);
	    jsonReq.keySet().forEach(p -> trgWrap.setPropertyValue(p, srcWrap.getPropertyValue(p)));
	    
//		for (Iterator<Entry<String, String>> iterator = jsonReq.entrySet().iterator(); iterator.hasNext();) {
//			 iterator.next();
//			 entity.getClass().
//		}
//		BeanUtils.copyProperties(dto, entity);
		return entity;
	}
	
	public <E,D> void copyNonNullProperties(E src, D target) {
	    BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
	}

	public <E> String[] getNullPropertyNames (E source) {
	    final BeanWrapper src = new BeanWrapperImpl(source);
	    java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

	    Set<String> emptyNames = new HashSet<String>();
	    for(java.beans.PropertyDescriptor pd : pds) {
	        Object srcValue = src.getPropertyValue(pd.getName());
	        if (srcValue == null) emptyNames.add(pd.getName());
	    }
	    String[] result = new String[emptyNames.size()];
	    return emptyNames.toArray(result);
	}
	
	public static String convertObjectArrayToString(Object[] arr, String delimiter,String quotes) {
		StringBuilder sb = new StringBuilder();
		for (Object obj : arr)
			sb.append(quotes).append(obj.toString()).append(quotes).append(delimiter);
		return sb.substring(0, sb.length() - 1);

	}

	public static String getDateTimeForUserTZ(String userTmezone, Date date) {
        String dateInUserTZ = "";
        if (date != null) {
            TimeZone fromTZ = TimeZone.getTimeZone("UTC");
            TimeZone toTZ;
            DateFormat formatter = new SimpleDateFormat("dd-MMM-yyyy HH:mm:ss a zzz");
            formatter.setTimeZone(fromTZ);
            formatter.format(date);
            if(userTmezone != null){
                toTZ = TimeZone.getTimeZone(userTmezone);
            }else{
                toTZ = TimeZone.getTimeZone("UTC");
            }
            
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(date);
            calendar.setTimeZone(toTZ);
            formatter.setTimeZone(toTZ);
//            return calendar.getTime();
            dateInUserTZ = formatter.format(calendar.getTime());
        }
        return  dateInUserTZ;
    }
	
	public  <E, D> List<?> convertEntityToDtoWithDateFormat(String filterName, String[] fields,int limit,List<E> entityList, List<D> dtoList,String dateFormat) {		
		logger.debug("Entity Type: " + entityList.getClass().getName() + " DTO Type: " + dtoList.getClass().getName() );
		ObjectMapper mapper = new ObjectMapper();
		FilterProvider filters;
		if (fields == null || fields.length <= 0) {
			fields = new String[] { "*" };
			filters = new SimpleFilterProvider().addFilter(filterName, SimpleBeanPropertyFilter.serializeAll());
		} else {
			filters = new SimpleFilterProvider().addFilter(filterName,
					SimpleBeanPropertyFilter.filterOutAllExcept(fields));

		}
		mapper.setFilterProvider(filters);
		if(null!=dateFormat) {
			SimpleDateFormat df = new SimpleDateFormat(dateFormat);
			mapper.setDateFormat(df);
		}
		
		List<Object> result = new ArrayList<Object>();
		try {
			int i=0;
			for (E lookupValue : entityList) {	
				D lv = dtoList.get(i++);
				BeanUtils.copyProperties(lookupValue, lv);
				
				JsonNode jsonRecord = mapper.readTree(mapper.writer(filters).writeValueAsString(lv));
				result.add(jsonRecord);
			}

		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage());
		}
		return result;
	}
	
}
