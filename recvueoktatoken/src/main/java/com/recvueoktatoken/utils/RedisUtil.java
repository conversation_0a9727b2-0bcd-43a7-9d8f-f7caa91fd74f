package com.recvueoktatoken.utils;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties.Jedis;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import com.recvueoktatoken.authorizer.model.UserToken;
import com.recvueoktatoken.model.Response;

@Configuration
public class RedisUtil {

	static Logger logger = LoggerFactory.getLogger(RedisUtil.class);

	@Autowired
	public RedisProperties redisProp;

	// Setting up the Jedis connection factory.
	@Bean
	JedisConnectionFactory jedisConnectionFactory() {
		Jedis jedis = redisProp.getJedis();

		GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
		poolConfig.setMaxIdle(jedis.getPool().getMaxIdle());
		poolConfig.setMinIdle(jedis.getPool().getMinIdle());
		poolConfig.setMaxWaitMillis(jedis.getPool().getMaxWait().toMillis());
		poolConfig.setMaxTotal(jedis.getPool().getMaxActive());

		RedisStandaloneConfiguration conf = new RedisStandaloneConfiguration(redisProp.getHost().trim(),
				redisProp.getPort());
		if (redisProp.getPassword() != null) {
			conf.setPassword(RedisPassword.of(redisProp.getPassword().trim()));
		}

		JedisClientConfiguration clientConf = JedisClientConfiguration.builder().connectTimeout(redisProp.getTimeout())
				.readTimeout(redisProp.getTimeout()).usePooling().poolConfig(poolConfig).build();
		JedisConnectionFactory jedisConFactory = new JedisConnectionFactory(conf, clientConf);

		logger.info("Redis Host: " + jedisConFactory.getHostName());
		logger.info("Redis Port: " + jedisConFactory.getPort());
		logger.info("Redis Pwd: ["+ jedisConFactory.getPassword() +"]");
		logger.info("Redis Timeout: " + jedisConFactory.getTimeout());
		logger.info("Redis MaxIdle: " + jedis.getPool().getMaxIdle());
		logger.info("Redis MinIdle: " + jedis.getPool().getMinIdle());
		logger.info("Redis MaxWait: " + jedis.getPool().getMaxWait().toMillis());
		logger.info("Redis MaxActive: " + jedis.getPool().getMaxActive());
		return jedisConFactory;
	}

	// Setting up the Redis template object.
	@Bean
	public RedisTemplate<String, Response> redisTemplate() {
		final RedisTemplate<String, Response> redisTemplate = new RedisTemplate<String, Response>();
		redisTemplate.setConnectionFactory(jedisConnectionFactory());
		redisTemplate.setKeySerializer(new StringRedisSerializer());
		redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<Response>(Response.class));
		logger.info("Redis configuration completed.");
		return redisTemplate;
	}

	// Setting up the Redis User token template object.
	@Bean(name = "redisTokenTemplate")
	public RedisTemplate<String, UserToken> redisTokenTemplate() {
		final RedisTemplate<String, UserToken> redisTokenTemplate = new RedisTemplate<String, UserToken>();
		redisTokenTemplate.setConnectionFactory(jedisConnectionFactory());
		redisTokenTemplate.setKeySerializer(new StringRedisSerializer());
		redisTokenTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<UserToken>(UserToken.class));
		logger.info("Redis User Token configuration completed.");
		return redisTokenTemplate;
	}

}
