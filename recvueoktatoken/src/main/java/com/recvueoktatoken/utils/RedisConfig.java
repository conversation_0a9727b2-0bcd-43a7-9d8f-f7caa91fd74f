//package com.recvueoktatoken.utils;
//
//import javax.cache.CacheManager;
//import javax.cache.Caching;
//
//import org.redisson.config.Config;
//import org.redisson.config.SingleServerConfig;
//import org.redisson.jcache.configuration.RedissonConfiguration;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//
//import com.giffing.bucket4j.spring.boot.starter.config.cache.SyncCacheResolver;
//import com.giffing.bucket4j.spring.boot.starter.config.cache.jcache.JCacheCacheResolver;
//
//import io.github.bucket4j.distributed.proxy.ProxyManager;
//import io.github.bucket4j.grid.jcache.JCacheProxyManager;
//
//@Configuration
//public class RedisConfig {
//
//	static Logger logger = LoggerFactory.getLogger(RedisConfig.class);
//
//	@Autowired
//	public RedisProperties redisProp;
//
//	@Bean
//	Config config() {
//		Config config = new Config();
//		SingleServerConfig ssc = config.useSingleServer();
//		if (redisProp.getPassword() != null && !redisProp.getPassword().isEmpty()) {
//			ssc.setPassword(redisProp.getPassword().trim());
//		}
//		ssc.setAddress("redis://" + redisProp.getHost().trim() + ":" + redisProp.getPort());
////        ssc.setClientName("ratelimit-client");
////        ssc.setDatabase(0);
//		ssc.setConnectionMinimumIdleSize(redisProp.getJedis().getPool().getMinIdle());
////        ssc.setConnectionPoolSize(5);
////        ssc.setIdleConnectionTimeout(5000);
////        ssc.setConnectTimeout(30000);
////        ssc.setTimeout(120000);
//		return config;
//	}
//
//	@Bean
//	CacheManager cacheManager(Config config) {
//		CacheManager manager = Caching.getCachingProvider().getCacheManager();
//		manager.createCache("ratelimit-cache", RedissonConfiguration.fromConfig(config));
//		return manager;
//	}
//
//	@Bean
//	ProxyManager<String> proxyManager(CacheManager cacheManager) {
//		return new JCacheProxyManager<>(cacheManager.getCache("ratelimit-cache"));
//	}
//
//	/**
//	 * reference:
//	 * https://github.com/MarcGiffing/bucket4j-spring-boot-starter/issues/73
//	 */
//	@Bean
//	@Primary
//	SyncCacheResolver bucket4jCacheResolver(CacheManager cacheManager) {
//		return new JCacheCacheResolver(cacheManager);
//	}
//}