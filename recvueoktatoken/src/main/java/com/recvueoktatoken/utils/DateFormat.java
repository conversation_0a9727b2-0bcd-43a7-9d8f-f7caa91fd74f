/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.utils;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

/**
 * Date format constraint interface for javax validations
 * <AUTHOR>
 */
@Documented
@Constraint(validatedBy = DateFormatValidatorForString.class)
@Target({ METHOD, FIELD})
@Retention(RUNTIME)
public @interface DateFormat {
	String message() default "Test message";
	Class<?>[] groups() default {};
	Class<? extends Payload>[] payload() default {};
	String[] dateFormat();
}
