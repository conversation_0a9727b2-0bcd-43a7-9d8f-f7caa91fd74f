package com.recvueoktatoken;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

//@SpringBootApplication(exclude= {RedissonAutoConfiguration.class})
@SpringBootApplication
@EnableJpaRepositories
public class RecvueoktatokenApplication {

	private static final Logger logger = LoggerFactory.getLogger(RecvueoktatokenApplication.class);
	
	public static void main(String[] args) {
		String stage="";
		for(String arg:args) {
            logger.info(arg);
            if(arg.contains("stage.name") && arg.contains("=")) {
            	stage=arg.split("=",2)[1];
            	logger.info(stage);
            	stage=stage;
            }
        }
		System.setProperty("stage.name", (stage));
		SpringApplication.run(RecvueoktatokenApplication.class, args);
	}

	@Bean
	@ConfigurationProperties("spring.datasource")
	public DataSource dataSource() {
		return DataSourceBuilder.create().build();
	}

}
