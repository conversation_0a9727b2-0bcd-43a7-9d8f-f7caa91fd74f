/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.common.security;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import com.recvueoktatoken.common.entity.User;
import datadog.trace.api.interceptor.MutableSpan;
import io.opentracing.Span;
import io.opentracing.util.GlobalTracer;

@Component
@Order(3)
public class TokenAuthenticationFilter implements javax.servlet.Filter {
	private  final org.slf4j.Logger LOG = LoggerFactory.getLogger(TokenAuthenticationFilter.class);

	@Value("${jwt.header}")
	private String AUTH_HEADER;

	@Autowired
	TokenHelper tokenHelper;

	private String getToken(HttpServletRequest request) {
		// LOG.info("getToken AUTH_HEADER - " + AUTH_HEADER);
		// Enumeration<String> headerNames = request.getHeaderNames();
		// while (headerNames.hasMoreElements()) {
		// String hName = headerNames.nextElement();
		// LOG.info("hName: " + hName + ",value:" +
		// String.valueOf(request.getHeader(hName)));
		// }
		String authHeader = request.getHeader(AUTH_HEADER);
		if (authHeader != null) {
			return authHeader;
		}
		return null;
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		Span span = GlobalTracer.get().activeSpan();
        if (span != null) {
           try{
        	 MutableSpan localRootSpan =  ((MutableSpan) span).getLocalRootSpan();
        	 localRootSpan.setTag("tenantid",((HttpServletRequest)request).getHeader("TENANTIDENTIFIER"));
			}catch(Exception | Error ex){
				LOG.info("Error : No Span");
			}
        }
		String error = "";
		String authToken = getToken((HttpServletRequest) request);
		// LOG.info("Inside tokenAuthenticationFilter, authToken - "+authToken);
		if (authToken != null) {
			// Get username from token
			String userName = tokenHelper.getUserNameFromJWTToken(authToken);
			LOG.info("userName from token "+userName);
			User user = new User();
			user.setUsername(userName);
			// User user = apiAuthUserService.getUserByName(userToken);

			SecurityContextHolder.getContext()
					.setAuthentication(new UsernamePasswordAuthenticationToken(user, null, null));

		}
		if (!error.equals("")) {
			LOG.info(error);
		}
		chain.doFilter(request, response);

	}

	@Override
	public void destroy() {
	}

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
	}

}
