package com.recvueoktatoken.common.security;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

import org.bouncycastle.util.io.pem.PemObject;
import org.bouncycastle.util.io.pem.PemReader;

public class PemFile {

	private PemObject pemObject;

	/**
	 * 
	 * @param inputStream
	 * @throws FileNotFoundException
	 * @throws IOException
	 */
	public PemFile(InputStream inputStream) throws FileNotFoundException, IOException {
		PemReader pemReader = new PemReader(new InputStreamReader(inputStream));
		try {
			this.pemObject = pemReader.readPemObject();
		} finally {
			pemReader.close();
		}
	}

	public PemObject getPemObject() {
		return pemObject;
	}
}
