/**
 * 
 */
package com.recvueoktatoken.common.security;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map.Entry;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

@Component
public class TokenHelper {
	private final Logger LOG = LoggerFactory.getLogger(this.getClass());

	@Value("${jwt.algorithm}")
	private String JWT_ALGORITHM;

	@Value("${jwt.publicKey.path}")
	private String JWT_PUBLICKEY_PATH;

	@Value("${app.name}")
	private String APP_NAME;

	@Value("${jwt.secret}")
	private String SECRET;

	@Value("${jwt.expires_in}")
	private int EXPIRES_IN;

	private SignatureAlgorithm SIGNATURE_ALGORITHM = SignatureAlgorithm.HS512;

	/**
	 * 
	 * @param httpServletRequest
	 * @return userDetails
	 * @throws NoSuchAlgorithmException
	 * @throws FileNotFoundException
	 * @throws InvalidKeySpecException
	 * @throws IOException
	 * @throws Exception
	 */
	public String getUserNameFromJWTToken(String jwtToken) {
		// String jwtToken = getJWTTokenFromRequest(httpServletRequest);
		String username = "";
		Claims claims;
		try {
			claims = getClaimsFromJWTToken(jwtToken);
			username = getUserNameFromClaims(claims);
		} catch (Exception e) {
			e.printStackTrace();
			username = null;
		}

		return username;
	}

	/**
	 * 
	 * @param claims
	 * @return UserDetails
	 * @throws NoSuchAlgorithmException
	 * @throws FileNotFoundException
	 * @throws InvalidKeySpecException
	 * @throws IOException
	 * @throws Exception
	 */
	public String getUserNameFromClaims(Claims claims) {
		String userDetails = null;
		if (claims != null) {
			for (Entry<String, Object> s : claims.entrySet()) {
				LOG.info("tokens from claims: " + s.getKey() + ",val:" + String.valueOf(s.getValue()));
				if (s.getKey().equalsIgnoreCase("user")) {
					LinkedHashMap claimsq = (LinkedHashMap) s.getValue();
					return String.valueOf(claimsq.get("username"));
				}
			}

			// LinkedHashMap userDetailsMap = (LinkedHashMap) claims.get("user");
			// userDetails = userDetailsMap.get("username").toString();
			// if (!(userDetails == null)) {
			// System.out.println("TokenHelper: getUserDetailsFromClaims: username: " +
			// userDetails);
			// }
		}
		return userDetails;
	}

	/**
	 * 
	 * @param jwtToken
	 * @return claims
	 * @throws IOException,
	 *             Exception
	 * @throws InvalidKeySpecException
	 * @throws FileNotFoundException
	 * @throws NoSuchAlgorithmException
	 * 
	 */
	public Claims getClaimsFromJWTToken(String jwtToken)
			throws NoSuchAlgorithmException, FileNotFoundException, InvalidKeySpecException, IOException, Exception {
		Claims claims = null;
		PublicKey publicKey = getPublicKey(JWT_ALGORITHM, JWT_PUBLICKEY_PATH);
		claims = Jwts.parser().setSigningKey(publicKey).parseClaimsJws(jwtToken).getBody();
		if (claims == null) {
			LOG.info("getClaimsFromToken: claims is NULL");
		}
		return claims;
	}

	/**
	 * 
	 * @param algorithm
	 * @param filePath
	 * @return publicKey
	 * @throws NoSuchAlgorithmException
	 * @throws FileNotFoundException
	 * @throws IOException
	 * @throws InvalidKeySpecException
	 * 
	 */
	private PublicKey getPublicKey(String algorithm, String filePath)
			throws NoSuchAlgorithmException, FileNotFoundException, IOException, InvalidKeySpecException {

		KeyFactory factory = KeyFactory.getInstance(algorithm);
		PemFile pemFile = new PemFile(this.getClass().getResourceAsStream(filePath));
		byte[] content = pemFile.getPemObject().getContent();
		X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(content);
		return factory.generatePublic(publicKeySpec);
	}

	/**
	 *
	 * @param username
	 * @return
	 */
	public String generateToken(String username) {
		String jws = Jwts.builder().setIssuer(APP_NAME).setSubject(username).setIssuedAt(generateCurrentDate())
				.setExpiration(generateExpirationDate()).signWith(SIGNATURE_ALGORITHM, SECRET).compact();
		return jws;
	}

	private long getCurrentTimeMillis() {
		return new Date().getTime();
	}

	private Date generateCurrentDate() {
		return new Date(getCurrentTimeMillis());
	}

	private Date generateExpirationDate() {
		return new Date(getCurrentTimeMillis() + this.EXPIRES_IN * 1000);
	}

}
