/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.common.security;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;

@Configuration
@EnableWebSecurity
@Order(1)
public class SecurityConfig extends WebSecurityConfigurerAdapter {

	public static final String JWT_TOKEN_HEADER_PARAM = "X-Forwarded-User";

	/**
	 * 
	 * @param http
	 * @throws Exception
	 */
	 @Override
	    protected void configure(HttpSecurity http) throws Exception {        
		 http.csrf().disable().cors().and()
         .authorizeRequests()
         .antMatchers("/api/v2.0/token","/api/v2.0/health/**","/api/v2.0/authorize", "/api/v2.0/okta/**", "/swagger-resources").permitAll()
         .anyRequest().authenticated();   
		 http.addFilterBefore(jwtAuthenticationTokenFilter(), BasicAuthenticationFilter.class)
	             .authorizeRequests().anyRequest().fullyAuthenticated();
	       
	        
	        http.headers().frameOptions().disable();
	    }
	       
	    @Bean
	    public TokenAuthenticationFilter jwtAuthenticationTokenFilter() throws Exception {
	        return new TokenAuthenticationFilter();
	    }
	   
	    
	    
}
