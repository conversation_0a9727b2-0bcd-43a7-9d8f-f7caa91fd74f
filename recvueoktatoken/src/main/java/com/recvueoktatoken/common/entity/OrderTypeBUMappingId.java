/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.common.entity;


import javax.persistence.Embeddable;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR>
 * Composite PK for OrderTypeBUMapping entity
 */
@Embeddable
public class OrderTypeBUMappingId implements java.io.Serializable{
    
    @ManyToOne
    private BusinessUnits businessUnit;
    
    @ManyToOne
    private OrderTypesAll orderTypeAll;
    
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        OrderTypeBUMappingId that = (OrderTypeBUMappingId) o;

        if (businessUnit != null ? !businessUnit.equals(that.businessUnit) : that.businessUnit != null) return false;
        if (orderTypeAll != null ? !orderTypeAll.equals(that.orderTypeAll) : that.orderTypeAll != null)
            return false;

        return true;
    }

    public int hashCode() {
        int result;
        result = (businessUnit != null ? businessUnit.hashCode() : 0);
        result = 31 * result + (orderTypeAll != null ? orderTypeAll.hashCode() : 0);
        return result;
    }

    public BusinessUnits getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(BusinessUnits businessUnit) {
        this.businessUnit = businessUnit;
    }

    public OrderTypesAll getOrderTypeAll() {
        return orderTypeAll;
    }

    public void setOrderTypeAll(OrderTypesAll orderTypeAll) {
        this.orderTypeAll = orderTypeAll;
    }
    
    
    
}
