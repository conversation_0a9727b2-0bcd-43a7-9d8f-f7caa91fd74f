/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.common.entity;

/**
 *
 * <AUTHOR>
 */
import java.io.Serializable;
import javax.persistence.*;
import java.util.Set;

/**
 * The persistent class for the CORE_PERMISSIONS database table.
 *
 */
@Entity
@Table(name = "CORE_PERMISSIONS")
@NamedQuery(name = "CorePermission.findAll", query = "SELECT c FROM CorePermission c")
public class CorePermission implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column(name = "PER_DESCRIPTION")
    private String perDescription;

    @Column(name = "PER_NAME")
    private String perName;

    //bi-directional many-to-many association to CoreRole
    @ManyToMany(mappedBy = "corePermissions")
    private Set<CoreRoles> coreRoles;

    @Id
    @Column(name = "PER_ID")
    private Long perId;

    public CorePermission() {
    }

    public String getPerDescription() {
        return this.perDescription;
    }

    public void setPerDescription(String perDescription) {
        this.perDescription = perDescription;
    }

    public String getPerName() {
        return this.perName;
    }

    public void setPerName(String perName) {
        this.perName = perName;
    }

    public Set<CoreRoles> getCoreRoles() {
        return this.coreRoles;
    }

    public void setCoreRoles(Set<CoreRoles> coreRoles) {
        this.coreRoles = coreRoles;
    }

    /**
     * @return the perId
     */
    public Long getPerId() {
        return perId;
    }

    /**
     * @param perId the perId to set
     */
    public void setPerId(Long perId) {
        this.perId = perId;
    }

}
