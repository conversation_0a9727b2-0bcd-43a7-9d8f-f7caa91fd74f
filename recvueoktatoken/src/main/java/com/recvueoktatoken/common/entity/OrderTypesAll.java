/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.common.entity;

/**
 *
 * <AUTHOR>
 */
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

/**
 * The persistent class for the ORDER_TYPES_ALL database table.
 *
 */
@Entity
@Table(name = "ORDER_TYPES_ALL")
@NamedQuery(name = "OrderTypesAll.findAll", query = "SELECT o FROM OrderTypesAll o ORDER BY o.orderTypeId ASC")
public class OrderTypesAll extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column(name = "LIST_CODE_1")
    private String listCode1;

    @Column(name = "LIST_CODE_10")
    private String listCode10;

    @Column(name = "LIST_CODE_2")
    private String listCode2;

    @Column(name = "LIST_CODE_3")
    private String listCode3;

    @Column(name = "LIST_CODE_4")
    private String listCode4;

    @Column(name = "LIST_CODE_5")
    private String listCode5;

    @Column(name = "LIST_CODE_6")
    private String listCode6;

    @Column(name = "LIST_CODE_7")
    private String listCode7;

    @Column(name = "LIST_CODE_8")
    private String listCode8;

    @Column(name = "LIST_CODE_9")
    private String listCode9;

    @Column(name = "LIST_VALUES_SET1")
    private String listValuesSet1;

    @Column(name = "LIST_VALUES_SET10")
    private String listValuesSet10;

    @Column(name = "LIST_VALUES_SET2")
    private String listValuesSet2;

    @Column(name = "LIST_VALUES_SET3")
    private String listValuesSet3;

    @Column(name = "LIST_VALUES_SET4")
    private String listValuesSet4;

    @Column(name = "LIST_VALUES_SET5")
    private String listValuesSet5;

    @Column(name = "LIST_VALUES_SET6")
    private String listValuesSet6;

    @Column(name = "LIST_VALUES_SET7")
    private String listValuesSet7;

    @Column(name = "LIST_VALUES_SET8")
    private String listValuesSet8;

    @Column(name = "LIST_VALUES_SET9")
    private String listValuesSet9;

    @Column(name = "MANDATORY_F")
    private String mandatoryF;

    @Column(name = "ORDER_TYPE")
    private String orderType;

    @Column(name = "END_DATE_V")
    private String endDateValidation;

    @Column(name = "DEL_TEMPLATE_COLS")
    private String delTemplateCols;

    @Column(name = "PRODUCT_CATALOG_ID")
    private Long productCatalogId;

    @Column(name = "MEANING")
    private String meaning;

    @Column(name = "ENABLED_FLAG")
    private String enabledFlag;
    @Column(name = "ATTRIBUTE_CATEGORY")
    private String attributeCategory;

    @Column(name = "ATTRIBUTE1")
    private String attribute1;

    @Column(name = "ATTRIBUTE2")
    private String attribute2;

    @Column(name = "ATTRIBUTE3")
    private String attribute3;

    @Column(name = "ATTRIBUTE4")
    private String attribute4;

    @Column(name = "ATTRIBUTE5")
    private String attribute5;

    @Column(name = "ATTRIBUTE6")
    private String attribute6;

    @Column(name = "ATTRIBUTE7")
    private String attribute7;

    @Column(name = "ATTRIBUTE8")
    private String attribute8;

    @Column(name = "ATTRIBUTE9")
    private String attribute9;

    @Column(name = "ATTRIBUTE10")
    private String attribute10;

    @Column(name = "ATTRIBUTE11")
    private String attribute11;

    @Column(name = "ATTRIBUTE12")
    private String attribute12;

    @Column(name = "ATTRIBUTE13")
    private String attribute13;

    @Column(name = "ATTRIBUTE14")
    private String attribute14;

    @Column(name = "ATTRIBUTE15")
    private String attribute15;

    @Column(name = "ORG_ID")
    private Long orgId;
    
    @Column(name = "TAX_CODE")
    private String taxCode;

    @Id
    @SequenceGenerator(name = "ORDER_TYPES_ID_GEN", sequenceName = "ORDER_TYPES_S", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ORDER_TYPES_ID_GEN")
    @Column(name = "ORDER_TYPE_ID")
    private BigDecimal orderTypeId;

    @Column(name = "WORKFLOW_ID")
    private Long workflowId;

    @Column(name = "ADJ_TRANSACTION_TYPE")
    private String adjTranType;

    @Column(name = "ADJ_CREDIT_TRANSACTION_TYPE")
    private String adjCreditTransactionType;
    
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "pk.orderTypeAll", cascade=CascadeType.ALL, orphanRemoval = true)
    @NotFound(action = NotFoundAction.IGNORE)
    @BatchSize(size = 10)
    private Set<OrderTypeBUMapping> orderTypeBUMapping = new HashSet<>(0);
    
    @Transient
    private boolean savedBeforeDisable = false;
    
    @Column(name = "APPLICATION_METHOD")
    private String applicationMethod;
    
    @Column(name = "TRUEUP_ACCOUNTING_RULE")
    private String trueUpAccountingRule;
    
    /**
     *
     */
    public OrderTypesAll() {
    }

    /**
     *
     * @return
     */
    public String getListCode1() {
        return this.listCode1;
    }

    /**
     *
     * @param listCode1
     */
    public void setListCode1(String listCode1) {
        this.listCode1 = listCode1;
    }

    /**
     *
     * @return
     */
    public String getListCode10() {
        return this.listCode10;
    }

    /**
     *
     * @param listCode10
     */
    public void setListCode10(String listCode10) {
        this.listCode10 = listCode10;
    }

    /**
     *
     * @return
     */
    public String getListCode2() {
        return this.listCode2;
    }

    /**
     *
     * @param listCode2
     */
    public void setListCode2(String listCode2) {
        this.listCode2 = listCode2;
    }

    /**
     *
     * @return
     */
    public String getListCode3() {
        return this.listCode3;
    }

    /**
     *
     * @param listCode3
     */
    public void setListCode3(String listCode3) {
        this.listCode3 = listCode3;
    }

    /**
     *
     * @return
     */
    public String getListCode4() {
        return this.listCode4;
    }

    /**
     *
     * @param listCode4
     */
    public void setListCode4(String listCode4) {
        this.listCode4 = listCode4;
    }

    /**
     *
     * @return
     */
    public String getListCode5() {
        return this.listCode5;
    }

    /**
     *
     * @param listCode5
     */
    public void setListCode5(String listCode5) {
        this.listCode5 = listCode5;
    }

    /**
     *
     * @return
     */
    public String getListCode6() {
        return this.listCode6;
    }

    /**
     *
     * @param listCode6
     */
    public void setListCode6(String listCode6) {
        this.listCode6 = listCode6;
    }

    /**
     *
     * @return
     */
    public String getListCode7() {
        return this.listCode7;
    }

    /**
     *
     * @param listCode7
     */
    public void setListCode7(String listCode7) {
        this.listCode7 = listCode7;
    }

    /**
     *
     * @return
     */
    public String getListCode8() {
        return this.listCode8;
    }

    /**
     *
     * @param listCode8
     */
    public void setListCode8(String listCode8) {
        this.listCode8 = listCode8;
    }

    /**
     *
     * @return
     */
    public String getListCode9() {
        return this.listCode9;
    }

    /**
     *
     * @param listCode9
     */
    public void setListCode9(String listCode9) {
        this.listCode9 = listCode9;
    }

    /**
     *
     * @return
     */
    public String getListValuesSet1() {
        return this.listValuesSet1;
    }

    /**
     *
     * @param listValuesSet1
     */
    public void setListValuesSet1(String listValuesSet1) {
        this.listValuesSet1 = listValuesSet1;
    }

    /**
     *
     * @return
     */
    public String getListValuesSet10() {
        return this.listValuesSet10;
    }

    /**
     *
     * @param listValuesSet10
     */
    public void setListValuesSet10(String listValuesSet10) {
        this.listValuesSet10 = listValuesSet10;
    }

    /**
     *
     * @return
     */
    public String getListValuesSet2() {
        return this.listValuesSet2;
    }

    /**
     *
     * @param listValuesSet2
     */
    public void setListValuesSet2(String listValuesSet2) {
        this.listValuesSet2 = listValuesSet2;
    }

    /**
     *
     * @return
     */
    public String getListValuesSet3() {
        return this.listValuesSet3;
    }

    /**
     *
     * @param listValuesSet3
     */
    public void setListValuesSet3(String listValuesSet3) {
        this.listValuesSet3 = listValuesSet3;
    }

    /**
     *
     * @return
     */
    public String getListValuesSet4() {
        return this.listValuesSet4;
    }

    /**
     *
     * @param listValuesSet4
     */
    public void setListValuesSet4(String listValuesSet4) {
        this.listValuesSet4 = listValuesSet4;
    }

    /**
     *
     * @return
     */
    public String getListValuesSet5() {
        return this.listValuesSet5;
    }

    /**
     *
     * @param listValuesSet5
     */
    public void setListValuesSet5(String listValuesSet5) {
        this.listValuesSet5 = listValuesSet5;
    }

    /**
     *
     * @return
     */
    public String getListValuesSet6() {
        return this.listValuesSet6;
    }

    /**
     *
     * @param listValuesSet6
     */
    public void setListValuesSet6(String listValuesSet6) {
        this.listValuesSet6 = listValuesSet6;
    }

    /**
     *
     * @return
     */
    public String getListValuesSet7() {
        return this.listValuesSet7;
    }

    /**
     *
     * @param listValuesSet7
     */
    public void setListValuesSet7(String listValuesSet7) {
        this.listValuesSet7 = listValuesSet7;
    }

    /**
     *
     * @return
     */
    public String getListValuesSet8() {
        return this.listValuesSet8;
    }

    /**
     *
     * @param listValuesSet8
     */
    public void setListValuesSet8(String listValuesSet8) {
        this.listValuesSet8 = listValuesSet8;
    }

    /**
     *
     * @return
     */
    public String getListValuesSet9() {
        return this.listValuesSet9;
    }

    /**
     *
     * @param listValuesSet9
     */
    public void setListValuesSet9(String listValuesSet9) {
        this.listValuesSet9 = listValuesSet9;
    }

    /**
     *
     * @return
     */
    public String getMandatoryF() {
        return this.mandatoryF;
    }

    /**
     *
     * @param mandatoryF
     */
    public void setMandatoryF(String mandatoryF) {
        this.mandatoryF = mandatoryF;
    }

    /**
     *
     * @return
     */
    public String getOrderType() {
        return this.orderType;
    }

    /**
     *
     * @param orderType
     */
    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    /**
     *
     * @return
     */
    public BigDecimal getOrderTypeId() {
        return this.orderTypeId;
    }

    /**
     *
     * @param orderTypeId
     */
    public void setOrderTypeId(BigDecimal orderTypeId) {
        this.orderTypeId = orderTypeId;
    }

    /**
     * @return the endDateValidation
     */
    public String getEndDateValidation() {
        return endDateValidation;
    }

    /**
     * @param endDateValidation the endDateValidation to set
     */
    public void setEndDateValidation(String endDateValidation) {
        this.endDateValidation = endDateValidation;
    }

    /**
     *
     * @return
     */
    public String getDelTemplateCols() {
        return delTemplateCols;
    }

    /**
     *
     * @param delTemplateCols
     */
    public void setDelTemplateCols(String delTemplateCols) {
        this.delTemplateCols = delTemplateCols;
    }

    /**
     *
     * @return
     */
    public Long getProductCatalogId() {
        return productCatalogId;
    }

    /**
     *
     * @param productCatalogId
     */
    public void setProductCatalogId(Long productCatalogId) {
        this.productCatalogId = productCatalogId;
    }

    /**
     *
     * @return
     */
    public String getMeaning() {
        return meaning;
    }

    /**
     *
     * @param meaning
     */
    public void setMeaning(String meaning) {
        this.meaning = meaning;
    }

    /**
     *
     * @return
     */
    public String getEnabledFlag() {
        return enabledFlag;
    }

    /**
     *
     * @param enabledFlag
     */
    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    /**
     *
     * @return
     */
    public String getAttributeCategory() {
        return attributeCategory;
    }

    /**
     *
     * @param attributeCategory
     */
    public void setAttributeCategory(String attributeCategory) {
        this.attributeCategory = attributeCategory;
    }

    /**
     *
     * @return
     */
    public String getAttribute1() {
        return attribute1;
    }

    /**
     *
     * @param attribute1
     */
    public void setAttribute1(String attribute1) {
        this.attribute1 = attribute1;
    }

    /**
     *
     * @return
     */
    public String getAttribute2() {
        return attribute2;
    }

    /**
     *
     * @param attribute2
     */
    public void setAttribute2(String attribute2) {
        this.attribute2 = attribute2;
    }

    /**
     *
     * @return
     */
    public String getAttribute3() {
        return attribute3;
    }

    /**
     *
     * @param attribute3
     */
    public void setAttribute3(String attribute3) {
        this.attribute3 = attribute3;
    }

    /**
     *
     * @return
     */
    public String getAttribute4() {
        return attribute4;
    }

    /**
     *
     * @param attribute4
     */
    public void setAttribute4(String attribute4) {
        this.attribute4 = attribute4;
    }

    /**
     *
     * @return
     */
    public String getAttribute5() {
        return attribute5;
    }

    /**
     *
     * @param attribute5
     */
    public void setAttribute5(String attribute5) {
        this.attribute5 = attribute5;
    }

    /**
     *
     * @return
     */
    public String getAttribute6() {
        return attribute6;
    }

    /**
     *
     * @param attribute6
     */
    public void setAttribute6(String attribute6) {
        this.attribute6 = attribute6;
    }

    /**
     *
     * @return
     */
    public String getAttribute7() {
        return attribute7;
    }

    /**
     *
     * @param attribute7
     */
    public void setAttribute7(String attribute7) {
        this.attribute7 = attribute7;
    }

    /**
     *
     * @return
     */
    public String getAttribute8() {
        return attribute8;
    }

    /**
     *
     * @param attribute8
     */
    public void setAttribute8(String attribute8) {
        this.attribute8 = attribute8;
    }

    /**
     *
     * @return
     */
    public String getAttribute9() {
        return attribute9;
    }

    /**
     *
     * @param attribute9
     */
    public void setAttribute9(String attribute9) {
        this.attribute9 = attribute9;
    }

    /**
     *
     * @return
     */
    public String getAttribute10() {
        return attribute10;
    }

    /**
     *
     * @param attribute10
     */
    public void setAttribute10(String attribute10) {
        this.attribute10 = attribute10;
    }

    /**
     *
     * @return
     */
    public String getAttribute11() {
        return attribute11;
    }

    /**
     *
     * @param attribute11
     */
    public void setAttribute11(String attribute11) {
        this.attribute11 = attribute11;
    }

    /**
     *
     * @return
     */
    public String getAttribute12() {
        return attribute12;
    }

    /**
     *
     * @param attribute12
     */
    public void setAttribute12(String attribute12) {
        this.attribute12 = attribute12;
    }

    /**
     *
     * @return
     */
    public String getAttribute13() {
        return attribute13;
    }

    /**
     *
     * @param attribute13
     */
    public void setAttribute13(String attribute13) {
        this.attribute13 = attribute13;
    }

    /**
     *
     * @return
     */
    public String getAttribute14() {
        return attribute14;
    }

    /**
     *
     * @param attribute14
     */
    public void setAttribute14(String attribute14) {
        this.attribute14 = attribute14;
    }

    /**
     *
     * @return
     */
    public String getAttribute15() {
        return attribute15;
    }

    /**
     *
     * @param attribute15
     */
    public void setAttribute15(String attribute15) {
        this.attribute15 = attribute15;
    }

    /**
     *
     * @return
     */
    public Long getOrgId() {
        return orgId;
    }

    /**
     *
     * @param orgId
     */
    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    /**
     *
     * @return
     */
    public Long getWorkflowId() {
        return workflowId;
    }

    /**
     *
     * @param workflowId
     */
    public void setWorkflowId(Long workflowId) {
        this.workflowId = workflowId;
    }

    /**
     *
     * @return
     */
    public String getAdjTranType() {
        return adjTranType;
    }

    /**
     *
     * @param adjTranType
     */
    public void setAdjTranType(String adjTranType) {
        this.adjTranType = adjTranType;
    }

    /**
     *
     * @return
     */
    public String getAdjCreditTransactionType() {
        return adjCreditTransactionType;
    }

    /**
     *
     * @param adjCreditTransactionType
     */
    public void setAdjCreditTransactionType(String adjCreditTransactionType) {
        this.adjCreditTransactionType = adjCreditTransactionType;
    }

    public Set<OrderTypeBUMapping> getOrderTypeBUMapping() {
        return orderTypeBUMapping;
    }

    public void setOrderTypeBUMapping(Set<OrderTypeBUMapping> orderTypeBUMapping) {
        this.orderTypeBUMapping = orderTypeBUMapping;
    }
    
    public void addOrderTypeBUMapping(OrderTypeBUMapping orderTypeBUMapping){
        this.getOrderTypeBUMapping().add(orderTypeBUMapping);
    }
    
    public void removeOrderTypeBUMapping(OrderTypeBUMapping orderTypeBUMapping){
        this.getOrderTypeBUMapping().remove(orderTypeBUMapping);
    }
    
    @Override
    public int hashCode() {
        int hash = 7;
        hash = 59 * hash + Objects.hashCode(this.orderTypeId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final OrderTypesAll other = (OrderTypesAll) obj;
        if (!Objects.equals(this.orderTypeId, other.orderTypeId)) {
            return false;
        }
        return true;
    }
    
    

    public boolean isSavedBeforeDisable() {
        return savedBeforeDisable;
    }

    public void setSavedBeforeDisable(boolean savedBeforeDisable) {
        this.savedBeforeDisable = savedBeforeDisable;
    }

    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public String getApplicationMethod() {
        return applicationMethod;
    }

    public void setApplicationMethod(String applicationMethod) {
        this.applicationMethod = applicationMethod;
    }

    public String getTrueUpAccountingRule() {
        return trueUpAccountingRule;
    }

    public void setTrueUpAccountingRule(String trueUpAccountingRule) {
        this.trueUpAccountingRule = trueUpAccountingRule;
    }
    
    
    
}
