/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.common.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Blob;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import org.slf4j.LoggerFactory;


@Entity
@Table(name = "CORE_USER")
@NamedQuery(name = "User.findAll", query = "SELECT a FROM User a")
public class User implements Serializable {

	private static final org.slf4j.Logger LOG = LoggerFactory.getLogger(User.class);

	@Id
	private Long id;

	@Column(name = "EMPLOYEE_ID", precision = 10)
	private BigDecimal employeeId;

	@Column(name = "TENANT_ID")
	private Long tenantId;

	@Column(name = "FIRST_NAME")
	private String firstName;

	@Column(name = "LAST_NAME")
	private String lastName;

	@Column(name = "USER_NAME")
	private String username;

	private String password;

	@Column(name = "EMAIL_ADDRESS")
	private String emailAddress;

	@Column(name = "FULL_NAME")
	private String fullName;

	@Column(name = "TITLE")
	private String title;

	@Column(name = "COMPANY_NAME")
	private String company;

	@Column(name = "CITY")
	private String city;

	@Column(name = "STATE")
	private String state;

	@Column(name = "COUNTRY")
	private String country;

	@Column(name = "START_DATE")
	@Temporal(TemporalType.TIMESTAMP)
	private Date startDate;

	@Column(name = "END_DATE")
	@Temporal(TemporalType.TIMESTAMP)
	private Date endDate;

	private boolean selected;

	@Column(name = "STATUS")
	private String status;

	private String userRoles;

	private Long roleId;

	@Column(name = "LANGUAGE_CODE")
	private String languageCode;

	@Column(name = "CURRENCY_CODE")
	private String currencyCode;

	@Lob
	@Column(name = "PICTURE")
	private Blob picture;

	@Column(name = "TIMEZONE")
	private String timezone;


    @Column(name = "USER_ID")
    private Long userId;
  
    @Column(name = "DEFAULT_HOME_LANDING_TAB")
    private String defaultHomeTab;
    
    @Column(name = "USER_BOARDS")
    private String userBoards;
    
    @Column(name="DEFAULT_UI_PREFERENCE")
    private String defaultUIPreference;
    
    @Column(name = "DATE_FORMAT")
    private String dateFormat;

    @Column(name = "NUMBER_FORMAT")
    private String numberFormat;
    
    @Column(name = "TIME_FORMAT")
	private String timeFormat;
    
    @Column(name ="READ_ONLY_USER")
	private String readOnlyUser;

    @Column(name="DEFAULT_DASHBOARD")
    private String defaultDashboard;
			
	@Transient
	private long currOrgId = 0;

	@Transient
	private HashSet<Long> rdOnlyBUs = new HashSet();

	@Transient
	private Set<CoreRoles> roles;

	public User() {

	}

	public Long getTenantId() {
		return tenantId;
	}

	public void setTenantId(Long tenantId) {
		this.tenantId = tenantId;
	}

	public String getFirstName() {
		return firstName;
	}

	public User(String userName, Long tenantId) {
		this.username = userName;
		this.tenantId = tenantId;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public boolean isAccountNonExpired() {
		return true;
	}

	public boolean isAccountNonLocked() {
		return true;
	}

	public boolean isCredentialsNonExpired() {
		return true;
	}

	public boolean isEnabled() {
		return true;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getEmailAddress() {
		return emailAddress;
	}

	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}

	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public Blob getPicture() {
		return picture;
	}

	public void setPicture(Blob picture) {
		this.picture = picture;
	}

	public String getTimezone() {
		return timezone;
	}

	public void setTimezone(String timezone) {
		this.timezone = timezone;
	}

	public long getCurrOrgId() {
		return currOrgId;
	}

	public void setCurrOrgId(long currOrgId) {
		this.currOrgId = currOrgId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public void setUserRoles(String userRoles) {
		this.userRoles = userRoles;
	}

	public boolean BUReadOnly() {
		return rdOnlyBUs.contains(currOrgId);
	}

	public boolean BUReadOnly(Long currOrgId) {
		return rdOnlyBUs.contains(currOrgId);
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public String getLanguageCode() {
		return languageCode;
	}

	public void setLanguageCode(String languageCode) {
		this.languageCode = languageCode;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public BigDecimal getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(BigDecimal employeeId) {
		this.employeeId = employeeId;
	}

	public HashSet<Long> getRdOnlyBUs() {
		return rdOnlyBUs;
	}

	public void setRdOnlyBUs(HashSet<Long> rdOnlyBUs) {
		this.rdOnlyBUs = rdOnlyBUs;
	}

	public Set<CoreRoles> getRoles() {
		return roles;
	}

	public void setRoles(Set<CoreRoles> roles) {
		this.roles = roles;
	}

	/**
	 * @return the defaultHomeTab
	 */
	public String getDefaultHomeTab() {
		return defaultHomeTab;
	}

	/**
	 * @param defaultHomeTab the defaultHomeTab to set
	 */
	public void setDefaultHomeTab(String defaultHomeTab) {
		this.defaultHomeTab = defaultHomeTab;
	}

	/**
	 * @return the userBoards
	 */
	public String getUserBoards() {
		return userBoards;
	}

	/**
	 * @param userBoards the userBoards to set
	 */
	public void setUserBoards(String userBoards) {
		this.userBoards = userBoards;
	}

	/**
	 * @return the defaultUIPreference
	 */
	public String getDefaultUIPreference() {
		return defaultUIPreference;
	}

	/**
	 * @param defaultUIPreference the defaultUIPreference to set
	 */
	public void setDefaultUIPreference(String defaultUIPreference) {
		this.defaultUIPreference = defaultUIPreference;
	}


	/**
	 * 
	 * @return the dateFormat
	 */
	public String getDateFormat() {
		return dateFormat;
	}

	/**
	 * 
	 * @param dateFormat the dateFormat to set
	 */
	public void setDateFormat(String dateFormat) {
		this.dateFormat = dateFormat;
	}

	/**
	 * 
	 * @return the numberFormat
	 */
	public String getNumberFormat() {
		return numberFormat;
	}
	
	/**
	 * 
	 * @param numberFormat the numberFormat to set
	 */
	public void setNumberFormat(String numberFormat) {
		this.numberFormat = numberFormat;
	}
    
	/**
	 * 
	 * @return the timeFormat
	 */
	public String getTimeFormat() {
		return timeFormat;
	}

	/**
	 * 
	 * @param timeFormat the timeFormat to set
	 */
	public void setTimeFormat(String timeFormat) {
		this.timeFormat = timeFormat;
	}

	/**
	 * 
	 * @return readOnlyUser
	 */
	public String getReadOnlyUser() {
		return readOnlyUser;
	}

	/**
	 * 
	 * @param readOnlyUser the readOnlyUser to set
	 */
	public void setReadOnlyUser(String readOnlyUser) {
		this.readOnlyUser = readOnlyUser;
	}
	
	public String getDefaultDashboard() {
		return defaultDashboard;
	}


	public void setDefaultDashboard(String defaultDashboard) {
		this.defaultDashboard = defaultDashboard;
	}

}
