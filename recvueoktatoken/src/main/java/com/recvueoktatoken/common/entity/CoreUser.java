package com.recvueoktatoken.common.entity;

import java.io.Serializable;
import java.math.BigInteger;
import java.sql.Blob;
import java.util.Date;
import java.util.List;
import java.util.Set;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.Lob;
import javax.persistence.ManyToMany;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;


@Entity
@Table(name = "CORE_USER")
@NamedQuery(name = "CoreUser.findAll", query = "SELECT c FROM CoreUser c")
public class CoreUser extends BaseEntity implements Serializable {

    private static final Long serialVersionUID = 1L;
    @ManyToMany(cascade = CascadeType.ALL)
    @JoinTable(
            name = "CORE_USER_ROLES", joinColumns = {
                @JoinColumn(name = "USER_ID", referencedColumnName = "USER_ID")
            }, inverseJoinColumns = {
                @JoinColumn(name = "ROLE_ID", referencedColumnName = "ROLE_ID")
            }
    )
    private Set<CoreRoles> coreRoles;	
    
    @OneToMany(mappedBy = "coreUser", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private List<CoreUserAttributes> coreUserAttributes;


    public List<CoreUserAttributes> getCoreUserAttributes() {
		return coreUserAttributes;
	}

	public void setCoreUserAttributes(List<CoreUserAttributes> coreUserAttributes) {
		this.coreUserAttributes = coreUserAttributes;
	}

	@Id
    @SequenceGenerator(name = "CORE_USER_USERID_GENERATOR", allocationSize = 1, sequenceName = "CORE_USER_SEQ")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CORE_USER_USERID_GENERATOR")
    @Column(name = "USER_ID", unique = true, nullable = false)
    private Long userId;

    @Basic(optional = false)
    @Column(name = "USER_NAME")
    private String userName;

    @Basic(optional = false)
    @Column(name = "ENCRYPTED_FOUNDATION_PASSWORD")
    private String encryptedFoundationPassword;

    @Basic(optional = false)
    @Column(name = "ENCRYPTED_USER_PASSWORD")
    private String encryptedUserPassword;

    @Basic(optional = false)
    @Column(name = "SESSION_NUMBER")
    private BigInteger sessionNumber;

    @Basic(optional = false)
    @Column(name = "START_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date startDate;

    @Column(name = "END_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date endDate;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "LAST_LOGON_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastLogonDate;

    @Column(name = "PASSWORD_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date passwordDate;

    @Column(name = "PASSWORD_ACCESSES_LEFT")
    private Long passwordAccessesLeft;

    @Column(name = "PASSWORD_LIFESPAN_ACCESSES")
    private Long passwordLifespanAccesses;

    @Column(name = "PASSWORD_LIFESPAN_DAYS")
    private Long passwordLifespanDays;

    @Column(name = "EMPLOYEE_ID")
    private Long employeeId;

    @Column(name = "EMAIL_ADDRESS")
    private String emailAddress;

    @Column(name = "FAX")
    private String fax;

    @Column(name = "CUSTOMER_ID")
    private Long customerId;

    @Column(name = "SUPPLIER_ID")
    private Long supplierId;

    @Column(name = "WEB_PASSWORD")
    private String webPassword;

    @Column(name = "SECURITY_GROUP_ID")
    private BigInteger securityGroupId;

    @Lob
    @Column(name = "USER_GUID")
    private byte[] userGuid;

    @Column(name = "GCN_CODE_COMBINATION_ID")
    private Long gcnCodeCombinationId;

    @Column(name = "PERSON_PARTY_ID")
    private BigInteger personPartyId;

    @Column(name = "FULL_NAME")
    private String fullName;

    @Column(name = "TITLE")
    private String title;

    @Column(name = "COMPANY_NAME")
    private String company;

    @Column(name = "CITY")
    private String city;

    @Column(name = "STATE")
    private String state;

    @Column(name = "COUNTRY")
    private String country;

    @Lob
    @Column(name = "PICTURE")
    private Blob picture;

    @Column(name = "TIMEZONE")
    private String timezone;

    @Column(name ="STATUS", updatable = false, nullable = false)
    private String status;

    @Column(name ="CURRENCY_CODE")
    private String currencyCode;
    
    @Column(name ="LANGUAGE_CODE")
    private String languageCode;
    
    @Column(name ="FIRST_NAME")
    private String firstName;
    
    @Column(name ="LAST_NAME")
    private String lastName;

    
    @Column(name = "DEFAULT_HOME_LANDING_TAB")
    private String defaultHomeTab;
    
    @Column(name = "USER_BOARDS")
    private String userBoards;
    
    @Column(name="DEFAULT_UI_PREFERENCE")
    private String defaultUIPreference;
    
    @Column(name = "DATE_FORMAT")
	private String dateFormat;

    @Column(name = "NUMBER_FORMAT")
	private String numberFormat;
    
    @Column(name = "TIME_FORMAT")
	private String timeFormat;
    
    @Column(name="READ_ONLY_USER")
	private String readOnlyUser;
    
    @Column(name="AUTHENTICATION_SOURCE")
    private String authenticationSource;  
    
    @Column(name="API_USER")
    private String apiUser;	
    
    @Column(name="DEFAULT_DASHBOARD")
    private String defaultDashboard;

	public CoreUser() {
    }

    public CoreUser(Long userId) {
        this.userId = userId;
    }

    public CoreUser(/*Long userId, */String userName,/* Date lastUpdateDate, Long lastUpdatedBy, Date creationDate, Long createdBy, String encryptedFoundationPassword, */ String encryptedUserPassword/*, BigInteger sessionNumber, Date startDate*/) {
//        this.userId = userId;
        this.userName = userName;
//        this.lastUpdateDate = lastUpdateDate;
//        this.lastUpdatedBy = lastUpdatedBy;
//        this.creationDate = creationDate;
//        this.createdBy = createdBy;
//        this.encryptedFoundationPassword = encryptedFoundationPassword;
        this.encryptedUserPassword = encryptedUserPassword;
//        this.sessionNumber = sessionNumber;
//        this.startDate = startDate;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEncryptedFoundationPassword() {
        return encryptedFoundationPassword;
    }

    public void setEncryptedFoundationPassword(String encryptedFoundationPassword) {
        this.encryptedFoundationPassword = encryptedFoundationPassword;
    }

    public String getEncryptedUserPassword() {
        return encryptedUserPassword;
    }

    public void setEncryptedUserPassword(String encryptedUserPassword) {
        this.encryptedUserPassword = encryptedUserPassword;
    }

    public BigInteger getSessionNumber() {
        return sessionNumber;
    }

    public void setSessionNumber(BigInteger sessionNumber) {
        this.sessionNumber = sessionNumber;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getLastLogonDate() {
        return lastLogonDate;
    }

    public void setLastLogonDate(Date lastLogonDate) {
        this.lastLogonDate = lastLogonDate;
    }

    public Date getPasswordDate() {
        return passwordDate;
    }

    public void setPasswordDate(Date passwordDate) {
        this.passwordDate = passwordDate;
    }

    public Long getPasswordAccessesLeft() {
        return passwordAccessesLeft;
    }

    public void setPasswordAccessesLeft(Long passwordAccessesLeft) {
        this.passwordAccessesLeft = passwordAccessesLeft;
    }

    public Long getPasswordLifespanAccesses() {
        return passwordLifespanAccesses;
    }

    public void setPasswordLifespanAccesses(Long passwordLifespanAccesses) {
        this.passwordLifespanAccesses = passwordLifespanAccesses;
    }

    public Long getPasswordLifespanDays() {
        return passwordLifespanDays;
    }

    public void setPasswordLifespanDays(Long passwordLifespanDays) {
        this.passwordLifespanDays = passwordLifespanDays;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getWebPassword() {
        return webPassword;
    }

    public void setWebPassword(String webPassword) {
        this.webPassword = webPassword;
    }

    public BigInteger getSecurityGroupId() {
        return securityGroupId;
    }

    public void setSecurityGroupId(BigInteger securityGroupId) {
        this.securityGroupId = securityGroupId;
    }

    public byte[] getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(byte[] userGuid) {
        this.userGuid = userGuid;
    }

    public Long getGcnCodeCombinationId() {
        return gcnCodeCombinationId;
    }

    public void setGcnCodeCombinationId(Long gcnCodeCombinationId) {
        this.gcnCodeCombinationId = gcnCodeCombinationId;
    }

    public BigInteger getPersonPartyId() {
        return personPartyId;
    }

    public void setPersonPartyId(BigInteger personPartyId) {
        this.personPartyId = personPartyId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Blob getPicture() {
        return picture;
    }

    public void setPicture(Blob picture) {
        this.picture = picture;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (userId != null ? userId.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof CoreUser)) {
            return false;
        }
        CoreUser other = (CoreUser) object;
        if ((this.userId == null && other.userId != null) || (this.userId != null && !this.userId.equals(other.userId))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.infovity.apps.adea.security.entity.CoreUser[ userId=" + userId + " ]";
    }

    /**
     * @return the coreRoles
     */
    public Set<CoreRoles> getCoreRoles() {
        return coreRoles;
    }

    /**
     * @param coreRoles the coreRoles to set
     */
    public void setCoreRoles(Set<CoreRoles> coreRoles) {
        this.coreRoles = coreRoles;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

	/**
	 * @return the defaultHomeTab
	 */
	public String getDefaultHomeTab() {
		return defaultHomeTab;
	}

	/**
	 * @param defaultHomeTab the defaultHomeTab to set
	 */
	public void setDefaultHomeTab(String defaultHomeTab) {
		this.defaultHomeTab = defaultHomeTab;
	}

	/**
	 * @return the userBoards
	 */
	public String getUserBoards() {
		return userBoards;
	}

	/**
	 * @param userBoards the userBoards to set
	 */
	public void setUserBoards(String userBoards) {
		this.userBoards = userBoards;
	}

	/**
	 * @return the defaultUIPreference
	 */
	public String getDefaultUIPreference() {
		return defaultUIPreference;
	}

	/**
	 * @param defaultUIPreference the defaultUIPreference to set
	 */
	public void setDefaultUIPreference(String defaultUIPreference) {
		this.defaultUIPreference = defaultUIPreference;
	}

    /**
     * 
     * @return the dateFormat
     */
	public String getDateFormat() {
		return dateFormat;
	}
	
	/**
	 * 
	 * @param dateFormat the dateFormat to set
	 */

	public void setDateFormat(String dateFormat) {
		this.dateFormat = dateFormat;
	}


	/**
	 * 
	 * @return the numberFormat
	 */

	public String getNumberFormat() {
		return numberFormat;
	}

	/**
	 * 
	 * @param numberFormat the numberFormat to set
	 */
	public void setNumberFormat(String numberFormat) {
		this.numberFormat = numberFormat;
	}	

	public String getTimeFormat() {
		return timeFormat;
	}

	public void setTimeFormat(String timeFormat) {
		this.timeFormat = timeFormat;
	}

	public String getReadOnlyUser() {
		return readOnlyUser;
	}

	public void setReadOnlyUser(String readOnlyUser) {
		this.readOnlyUser = readOnlyUser;
	}
	
	public String getAuthenticationSource() {
		return authenticationSource;
	}

	public void setAuthenticationSource(String authenticationSource) {
		this.authenticationSource = authenticationSource;
	}
	
	public String getApiUser() {
		return apiUser;
	}

	public void setApiUser(String apiUser) {
		this.apiUser = apiUser;
	}
	
	public String getDefaultDashboard() {
		return defaultDashboard;
	}

	public void setDefaultDashboard(String defaultDashboard) {
		this.defaultDashboard = defaultDashboard;
	}
}
