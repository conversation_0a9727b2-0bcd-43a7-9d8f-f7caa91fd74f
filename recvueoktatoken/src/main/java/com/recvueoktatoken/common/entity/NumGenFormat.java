/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.common.entity;


import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.NamedQueries;
import org.hibernate.annotations.NamedQuery;

import com.recvueoktatoken.common.constants.Constants;

@NamedQueries({
	@NamedQuery(name = "getNumGenFormatBySourceSetupId",
        query = "from NumGenFormat a where a.setupSource=:setupSource and a.setupSourceId=:setupSourceId and a.status=:active"),
        @NamedQuery(name = "NumGenFormat.findAll", query = "SELECT a FROM NumGenFormat a order by a.seqId"),
        @NamedQuery(name = "getNumGenFormatForOrder",
        query = "from NumGenFormat a where (a.setupSource=:setupBUSource and a.setupSourceId=:setupBUSourceId) or"
                    + " (a.setupSource=:setupOrdTypSource and a.setupSourceId=:setupOrdTypSourceId and a.assocOrgId=:setupBUSourceId)"
                    + " and a.status=:active "
                    + " order by a.setupSource DESC"),
        @NamedQuery(name = "updateInactiveNumGenFormats",
        query = "update NumGenFormat a set a.status =:inactive where a.seqId in (:seqIds)"),
        @NamedQuery(name = "findAutoPrefix",
        query = "from NumGenFormat a where a.seqPrefix=:seqPrefix and a.status=:active and a.setupType=:auto")
})
@Entity
@Table(name = "NUMBER_GEN_FORMAT")
public class NumGenFormat extends BaseEntity implements Serializable{
    
    private static final long serialVersionUID = 1L;
    
    @Id
    @SequenceGenerator(name = "NUMBER_GEN_FORMAT_GENERATOR", allocationSize = 1, sequenceName = "NUMBER_GEN_FORMAT_S")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "NUMBER_GEN_FORMAT_GENERATOR")
    @Column(name = "SEQ_ID")
    private Long seqId;
    
    @Column(name = "SETUP_TYPE")
    private String setupType;
    
    @Column(name = "SETUP_SOURCE")
    private String setupSource;
    
    @Column(name = "SETUP_SOURCE_ID")
    private Long setupSourceId;
    
    @Column(name = "ASSOC_ORG_ID")
    private Long assocOrgId;
    
    @Column(name = "FIELD")
    private String field;
    
    @Column(name = "SEQ_PREFIX")
    private String seqPrefix;
    
    @Column(name = "SEQ_SUFFIX")
    private String seqSuffix;
    
    @Column(name = "SEQ_START")
    private Long seqStart;
    
    @Column(name = "SEQ_END")
    private Long seqEnd;
    
    @Column(name = "INCREASE_BY")
    private Long increaseBy;
    
    @Column(name = "NUMFORMAT")
    private Long numFormat;
    
    @Column(name = "WARNSEQNO")
    private Long warnSeqNo;
    
    @Column(name = "LATEST_SEQ")
    private Long latestSeqNo;
    
    @Column(name = "STATUS")
    private String status;
    
    @Transient
    private boolean autoType;
    
    @Transient
    private boolean active;
    
    @Transient
    private boolean autoGenNumType;
    
    @Transient
    private boolean refreshLatestSeqNo=false;
    
    
    /**
     * No args default constructor as while loading hibernate initializes the entity classes using def constructor
     * If not present, it throws exception while loading the numgenformat page
     * Constructor to allow use as a JavaBean
     */
    public NumGenFormat() {
    }

    /**
     * Convenient constructor with default values set at BU level
     * @param setupSource
     * @param setupSourceId
     */
    public NumGenFormat(String setupSource, Long setupSourceId) {
        this.setupSource = setupSource;
        this.setupSourceId = setupSourceId;
        this.setupType=Constants.MANUAL.toUpperCase();
        this.field=Constants.ORDER_NUMBER;
        this.seqId=Long.valueOf(-11);
        this.increaseBy=Long.valueOf(1);
    }
    
    /**
     *
     * @return
     */
    public Long getSeqId() {
        return seqId;
    }

    /**
     *
     * @param seqId
     */
    public void setSeqId(Long seqId) {
        this.seqId = seqId;
    }

    /**
     *
     * @return
     */
    public String getSetupType() {
        return setupType;
    }

    /**
     *
     * @param setupType
     */
    public void setSetupType(String setupType) {
        this.setupType = setupType;
    }

    
    /**
     *
     * @return
     */
    public String getSetupSource() {
        return setupSource;
    }

    /**
     *
     * @param setupSource
     */
    public void setSetupSource(String setupSource) {
        this.setupSource = setupSource;
    }

    /**
     *
     * @return
     */
    public Long getSetupSourceId() {
        return setupSourceId;
    }

    /**
     *
     * @param setupSourceId
     */
    public void setSetupSourceId(Long setupSourceId) {
        this.setupSourceId = setupSourceId;
    }

    /**
     *
     * @return
     */
    public String getField() {
        return field;
    }

    /**
     *
     * @param field
     */
    public void setField(String field) {
        this.field = field;
    }

    /**
     *
     * @return
     */
    public String getSeqPrefix() {
        return seqPrefix;
    }

    /**
     *
     * @param seqPrefix
     */
    public void setSeqPrefix(String seqPrefix) {
        this.seqPrefix = seqPrefix;
    }

    /**
     *
     * @return
     */
    public String getSeqSuffix() {
        return seqSuffix;
    }

    /**
     *
     * @param seqSuffix
     */
    public void setSeqSuffix(String seqSuffix) {
        this.seqSuffix = seqSuffix;
    }

    

    /**
     *
     * @return
     */
    public Long getSeqStart() {
        return seqStart;
    }

    /**
     *
     * @param seqStart
     */
    public void setSeqStart(Long seqStart) {
        this.seqStart = seqStart;
    }

    /**
     *
     * @return
     */
    public Long getSeqEnd() {
        return seqEnd;
    }

    /**
     *
     * @param seqEnd
     */
    public void setSeqEnd(Long seqEnd) {
        this.seqEnd = seqEnd;
    }

    /**
     *
     * @return
     */
    public Long getIncreaseBy() {
        return increaseBy;
    }

    /**
     *
     * @param increaseBy
     */
    public void setIncreaseBy(Long increaseBy) {
        this.increaseBy = increaseBy;
    }

    

    /**
     *
     * @return
     */
    public Long getAssocOrgId() {
        return assocOrgId;
    }

    /**
     *
     * @param assocOrgId
     */
    public void setAssocOrgId(Long assocOrgId) {
        this.assocOrgId = assocOrgId;
    }

    /**
     *
     * @return
     */
    public Long getNumFormat() {
        return numFormat;
    }

    /**
     *
     * @param numFormat
     */
    public void setNumFormat(Long numFormat) {
        this.numFormat = numFormat;
    }

    /**
     *
     * @return
     */
    public Long getWarnSeqNo() {
        return warnSeqNo;
    }

    /**
     *
     * @param warnSeqNo
     */
    public void setWarnSeqNo(Long warnSeqNo) {
        this.warnSeqNo = warnSeqNo;
    }

    /**
     *
     * @return
     */
    public Long getLatestSeqNo() {
        return latestSeqNo;
    }

    /**
     *
     * @param latestSeqNo
     */
    public void setLatestSeqNo(Long latestSeqNo) {
        this.latestSeqNo = latestSeqNo;
    }

    /**
     * This checks if the num gen type is auto, used in xhtml pages to disable fields
     * @return
     */
    public boolean isAutoType() {
        if(setupType.equalsIgnoreCase(Constants.AUTO_NUM_GEN_TYPE)){
            return true;
        } 
        return false;
    }

    /**
     *
     * @param autoType
     */
    public void setAutoType(boolean autoType) {
        this.autoType = autoType;
    }

    @Override
    public String toString() {
        String seqSuffixStr = seqSuffix==null?"":seqSuffix;
        return "NumGenFormat{" + "setupType=" + setupType + ", setupSource=" + setupSource + ", setupSourceId=" + 
                setupSourceId + ",seqStart="+seqStart+",seqSuffix="+seqSuffixStr+",seqPrefix="+seqPrefix;
    }

    /**
     *
     * @return
     */
    public String getStatus() {
        return status;
    }

    /**
     *
     * @param status
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     *
     * @return
     */
    public boolean isActive() {
        return status!=null && status.equalsIgnoreCase(Constants.USER_ACTIVE_STATUS);
    }

    /**
     *
     * @param active
     */
    public void setActive(boolean active) {
        this.active = active;
    }

    /**
     * Returns true if the number should not be manually entered for this auto gen format
     * @return
     */
    public boolean isAutoGenNumType() {
        if(setupType.equalsIgnoreCase(Constants.AUTO_NUM_GEN_TYPE) ||
                setupType.equalsIgnoreCase(Constants.GLOBAL_NUM_GEN_TYPE)){
            return true;
        } 
        return false;
    }

    /**
     *
     * @param autoGenNumType
     */
    public void setAutoGenNumType(boolean autoGenNumType) {
        this.autoGenNumType = autoGenNumType;
    }

    public boolean isRefreshLatestSeqNo() {
        return refreshLatestSeqNo;
    }

    public void setRefreshLatestSeqNo(boolean refreshLatestSeqNo) {
        this.refreshLatestSeqNo = refreshLatestSeqNo;
    }
}
