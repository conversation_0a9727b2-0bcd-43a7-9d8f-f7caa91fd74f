/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.common.entity;


import java.util.Objects;

import javax.persistence.AssociationOverride;
import javax.persistence.AssociationOverrides;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.Table;

@Entity
@Table(name = "ORDER_TYPE_BU_MAPPING")
@AssociationOverrides({
		@AssociationOverride(name = "pk.orderTypeAll",
			joinColumns = @JoinColumn(name = "ORDER_TYPE_ID")),
		@AssociationOverride(name = "pk.businessUnit",
			joinColumns = @JoinColumn(name = "ORG_ID")) })
public class OrderTypeBUMapping extends BaseEntity implements java.io.Serializable{
    
    @EmbeddedId
    private OrderTypeBUMappingId pk;
    
    public OrderTypeBUMapping() {
        this.pk = new OrderTypeBUMappingId();
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 17 * hash + Objects.hashCode(this.pk);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final OrderTypeBUMapping other = (OrderTypeBUMapping) obj;
        if (!Objects.equals(this.pk, other.pk)) {
            return false;
        }
        return true;
    }

    public OrderTypeBUMappingId getPk() {
        return pk;
    }

    public void setPk(OrderTypeBUMappingId pk) {
        this.pk = pk;
    }

    public BusinessUnits getBusinessUnits() {
        return getPk().getBusinessUnit();
    }

    public void setBusinessUnits(BusinessUnits businessUnits) {
        getPk().setBusinessUnit(businessUnits);
    }

    public OrderTypesAll getOrderTypesAll() {
        return getPk().getOrderTypeAll();
    }

    public void setOrderTypesAll(OrderTypesAll orderTypesAll) {
        getPk().setOrderTypeAll(orderTypesAll);
    }
    
    
    
}
