/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.common.entity;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "CORE_ROLES")
@NamedQuery(name = "CoreRoles.findAll", query = "SELECT c FROM CoreRoles c")
public class CoreRoles implements Serializable {

    private static final Long serialVersionUID = 1L;

    @ManyToMany
    @JoinTable(
            name = "CORE_ROLE_PERMISSIONS", joinColumns = {
                @JoinColumn(name = "ROLE_ID", referencedColumnName = "ROLE_ID")
            }, inverseJoinColumns = {
                @JoinColumn(name = "PER_ID", referencedColumnName = "PER_ID")
            }
    )
    private List<CorePermission> corePermissions;

    //bi-directional many-to-many association to CoreUser
    @ManyToMany(mappedBy = "coreRoles")
    private List<CoreUser> coreUsers;
    
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name="ROLE_ID")
    private List<CoreRoleBusinessUnit> businessUnits;

    @Id
    @SequenceGenerator(name = "CORE_ROLES_ROLEID_GENERATOR", allocationSize = 1, sequenceName = "CORE_ROLES_SEQ")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CORE_ROLES_ROLEID_GENERATOR")
    @Column(name = "ROLE_ID", unique = true, nullable = false)
    private Long roleId;

    @Column(name = "EMPLOYEE_ID")
    private Long employeeId;

    @Basic(optional = false)
    @Column(name = "DATE_FROM")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateFrom;

    @Column(name = "DATE_TO")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateTo;

    @Column(name = "ROLE_NAME")
    private String roleName;

    @Column(name = "ATTRIBUTE1")
    private String attribute1;

    @Column(name = "ATTRIBUTE2")
    private String attribute2;

    @Column(name = "ATTRIBUTE3")
    private String attribute3;

    @Column(name = "ATTRIBUTE4")
    private String attribute4;

    @Column(name = "ATTRIBUTE5")
    private String attribute5;

    @Column(name = "ATTRIBUTE6")
    private String attribute6;

    @Column(name = "ATTRIBUTE7")
    private String attribute7;

    @Column(name = "ATTRIBUTE8")
    private String attribute8;

    @Column(name = "ATTRIBUTE9")
    private String attribute9;

    @Column(name = "ATTRIBUTE10")
    private String attribute10;

    @Column(name = "LAST_UPDATE_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastUpdateDate;

    @Column(name = "LAST_UPDATED_BY")
    private Long lastUpdatedBy;

    @Column(name = "CREATED_BY")
    private Long createdBy;

    @Column(name = "CREATION_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date creationDate;

    @Column(name = "ROLE_INFORMATION1")
    private String roleInformation1;

    @Column(name = "ROLE_INFORMATION2")
    private String roleInformation2;

    @Column(name = "ROLE3_INFORMATION3")
    private String role3Information3;

    @Column(name = "APPROVAL_AUTHORITY")
    private BigInteger approvalAuthority;

    @Column(name = "COMMENTS")
    private String comments;

    @Column(name = "TENANT_ID")
    private BigInteger tenantId;

    public CoreRoles() {
    }

    public CoreRoles(Long roleId) {
        this.roleId = roleId;
    }

    public CoreRoles(Long roleId, Date dateFrom) {
        this.roleId = roleId;
        this.dateFrom = dateFrom;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public Date getDateFrom() {
        return dateFrom;
    }

    public void setDateFrom(Date dateFrom) {
        this.dateFrom = dateFrom;
    }

    public Date getDateTo() {
        return dateTo;
    }

    public void setDateTo(Date dateTo) {
        this.dateTo = dateTo;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getAttribute1() {
        return attribute1;
    }

    public void setAttribute1(String attribute1) {
        this.attribute1 = attribute1;
    }

    public String getAttribute2() {
        return attribute2;
    }

    public void setAttribute2(String attribute2) {
        this.attribute2 = attribute2;
    }

    public String getAttribute3() {
        return attribute3;
    }

    public void setAttribute3(String attribute3) {
        this.attribute3 = attribute3;
    }

    public String getAttribute4() {
        return attribute4;
    }

    public void setAttribute4(String attribute4) {
        this.attribute4 = attribute4;
    }

    public String getAttribute5() {
        return attribute5;
    }

    public void setAttribute5(String attribute5) {
        this.attribute5 = attribute5;
    }

    public String getAttribute6() {
        return attribute6;
    }

    public void setAttribute6(String attribute6) {
        this.attribute6 = attribute6;
    }

    public String getAttribute7() {
        return attribute7;
    }

    public void setAttribute7(String attribute7) {
        this.attribute7 = attribute7;
    }

    public String getAttribute8() {
        return attribute8;
    }

    public void setAttribute8(String attribute8) {
        this.attribute8 = attribute8;
    }

    public String getAttribute9() {
        return attribute9;
    }

    public void setAttribute9(String attribute9) {
        this.attribute9 = attribute9;
    }

    public String getAttribute10() {
        return attribute10;
    }

    public void setAttribute10(String attribute10) {
        this.attribute10 = attribute10;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Long getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Long lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getRoleInformation1() {
        return roleInformation1;
    }

    public void setRoleInformation1(String roleInformation1) {
        this.roleInformation1 = roleInformation1;
    }

    public String getRoleInformation2() {
        return roleInformation2;
    }

    public void setRoleInformation2(String roleInformation2) {
        this.roleInformation2 = roleInformation2;
    }

    public String getRole3Information3() {
        return role3Information3;
    }

    public void setRole3Information3(String role3Information3) {
        this.role3Information3 = role3Information3;
    }

    public BigInteger getApprovalAuthority() {
        return approvalAuthority;
    }

    public void setApprovalAuthority(BigInteger approvalAuthority) {
        this.approvalAuthority = approvalAuthority;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public BigInteger getTenantId() {
        return tenantId;
    }

    public void setTenantId(BigInteger tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (roleId != null ? roleId.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof CoreRoles)) {
            return false;
        }
        CoreRoles other = (CoreRoles) object;
        if ((this.roleId == null && other.roleId != null) || (this.roleId != null && !this.roleId.equals(other.roleId))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.infovity.apps.adea.security.entity.CoreRoles[ roleId=" + roleId + " ]";
    }

    /**
     * @return the corePermissions
     */
    public List<CorePermission> getCorePermissions() {
        return corePermissions;
    }

    /**
     * @param corePermissions the corePermissions to set
     */
    public void setCorePermissions(List<CorePermission> corePermissions) {
        this.corePermissions = corePermissions;
    }

    /**
     * @return the coreUsers
     */
    public List<CoreUser> getCoreUsers() {
        return coreUsers;
    }

    /**
     * @param coreUsers the coreUsers to set
     */
    public void setCoreUsers(List<CoreUser> coreUsers) {
        this.coreUsers = coreUsers;
    }

    public List<CoreRoleBusinessUnit> getBusinessUnits() {
        return businessUnits;
    }

    public void setBusinessUnits(List<CoreRoleBusinessUnit> businessUnits) {
        this.businessUnits = businessUnits;
    }
    
    

}
