package com.recvueoktatoken.common.entity;


import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "CORE_USER_ROLES")
@NamedQuery(name = "CoreUserRoles.findAll", query = "SELECT c FROM CoreUserRoles c")

public class CoreUserRoles  extends BaseEntity implements Serializable {
    private static final Long serialVersionUID = 1L;
    @Id
    @SequenceGenerator(name = "CORE_USER_ROLES_USERROLEID_GENERATOR",allocationSize = 1,sequenceName = "CORE_USER_ROLES_SEQ")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CORE_USER_ROLES_USERROLEID_GENERATOR")
    @Column(name = "USER_ROLE_ID", unique = true, nullable = false)
    private Long userRoleId;
    
    @Basic(optional = false)
    @Column(name = "ROLE_ID")
    private Long roleId;
    
    @Column(name = "EMPLOYEE_ID")
    private Long employeeId;
    
    @Column(name = "USER_ROLE_NUMBER")
    private String userRoleNumber;
    
    @Basic(optional = false)
    @Column(name = "DATE_FROM")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateFrom;
    
    @Column(name = "DATE_TO")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateTo;
    
    @Column(name = "USER_ROLE")
    private String userRole;
    
    @Column(name = "FULL_NAME")
    private String fullName;
    
    @Column(name = "FIRST_NAME")
    private String firstName;
    
    @Column(name = "MIDDLE_NAME")
    private String middleName;
    
    @Column(name = "LAST_NAME")
    private String lastName;
    
    @Column(name = "ATTRIBUTE1")
    private String attribute1;
    
    @Column(name = "ATTRIBUTE2")
    private String attribute2;
    
    @Column(name = "ATTRIBUTE3")
    private String attribute3;
    
    @Column(name = "ATTRIBUTE4")
    private String attribute4;
    
    @Column(name = "ATTRIBUTE5")
    private String attribute5;
    
    @Column(name = "ATTRIBUTE6")
    private String attribute6;
    
    @Column(name = "ATTRIBUTE7")
    private String attribute7;
    
    @Column(name = "ATTRIBUTE8")
    private String attribute8;
    
    @Column(name = "ATTRIBUTE9")
    private String attribute9;
    
    @Column(name = "ATTRIBUTE10")
    private String attribute10;
    
    @Column(name = "LAST_UPDATE_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastUpdateDate;
    
    @Column(name = "LAST_UPDATED_BY")
    private Long lastUpdatedBy;
    
    @Column(name = "CREATED_BY")
    private Long createdBy;
    
    @Column(name = "CREATION_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date creationDate;
    
    @Column(name = "ROLE_INFORMATION1")
    private String roleInformation1;
    
    @Column(name = "ROLE_INFORMATION2")
    private String roleInformation2;
    
    @Column(name = "ROLE3_INFORMATION3")
    private String role3Information3;
    
    @Column(name = "APPROVAL_AUTHORITY")
    private BigInteger approvalAuthority;
    
    @Column(name = "COMMENTS")
    private String comments;
    
    
    //bi-directional many-to-one association to CoreUser
    @ManyToOne
    @JoinColumn(name = "USER_ID")
    private CoreUser coreUser;


    public CoreUserRoles() {
    }

    public CoreUserRoles(Long userRoleId) {
        this.userRoleId = userRoleId;
    }

    public CoreUserRoles(Long userRoleId, Long roleId, Date dateFrom) {
        this.userRoleId = userRoleId;
        this.roleId = roleId;
        this.dateFrom = dateFrom;
    }

    public Long getUserRoleId() {
        return userRoleId;
    }

    public void setUserRoleId(Long userRoleId) {
        this.userRoleId = userRoleId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getUserRoleNumber() {
        return userRoleNumber;
    }

    public void setUserRoleNumber(String userRoleNumber) {
        this.userRoleNumber = userRoleNumber;
    }

    public Date getDateFrom() {
        return dateFrom;
    }

    public void setDateFrom(Date dateFrom) {
        this.dateFrom = dateFrom;
    }

    public Date getDateTo() {
        return dateTo;
    }

    public void setDateTo(Date dateTo) {
        this.dateTo = dateTo;
    }

    public String getUserRole() {
        return userRole;
    }

    public void setUserRole(String userRole) {
        this.userRole = userRole;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getAttribute1() {
        return attribute1;
    }

    public void setAttribute1(String attribute1) {
        this.attribute1 = attribute1;
    }

    public String getAttribute2() {
        return attribute2;
    }

    public void setAttribute2(String attribute2) {
        this.attribute2 = attribute2;
    }

    public String getAttribute3() {
        return attribute3;
    }

    public void setAttribute3(String attribute3) {
        this.attribute3 = attribute3;
    }

    public String getAttribute4() {
        return attribute4;
    }

    public void setAttribute4(String attribute4) {
        this.attribute4 = attribute4;
    }

    public String getAttribute5() {
        return attribute5;
    }

    public void setAttribute5(String attribute5) {
        this.attribute5 = attribute5;
    }

    public String getAttribute6() {
        return attribute6;
    }

    public void setAttribute6(String attribute6) {
        this.attribute6 = attribute6;
    }

    public String getAttribute7() {
        return attribute7;
    }

    public void setAttribute7(String attribute7) {
        this.attribute7 = attribute7;
    }

    public String getAttribute8() {
        return attribute8;
    }

    public void setAttribute8(String attribute8) {
        this.attribute8 = attribute8;
    }

    public String getAttribute9() {
        return attribute9;
    }

    public void setAttribute9(String attribute9) {
        this.attribute9 = attribute9;
    }

    public String getAttribute10() {
        return attribute10;
    }

    public void setAttribute10(String attribute10) {
        this.attribute10 = attribute10;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Long getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Long lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getRoleInformation1() {
        return roleInformation1;
    }

    public void setRoleInformation1(String roleInformation1) {
        this.roleInformation1 = roleInformation1;
    }

    public String getRoleInformation2() {
        return roleInformation2;
    }

    public void setRoleInformation2(String roleInformation2) {
        this.roleInformation2 = roleInformation2;
    }

    public String getRole3Information3() {
        return role3Information3;
    }

    public void setRole3Information3(String role3Information3) {
        this.role3Information3 = role3Information3;
    }

    public BigInteger getApprovalAuthority() {
        return approvalAuthority;
    }

    public void setApprovalAuthority(BigInteger approvalAuthority) {
        this.approvalAuthority = approvalAuthority;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    
    public CoreUser getCoreUser() {
        return coreUser;
    }

    public void setCoreUser(CoreUser coreUser) {
        this.coreUser = coreUser;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (userRoleId != null ? userRoleId.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof CoreUserRoles)) {
            return false;
        }
        CoreUserRoles other = (CoreUserRoles) object;
        if ((this.userRoleId == null && other.userRoleId != null) || (this.userRoleId != null && !this.userRoleId.equals(other.userRoleId))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.infovity.apps.adea.security.entity.CoreUserRoles[ userRoleId=" + userRoleId + " ]";
    }
    
}
