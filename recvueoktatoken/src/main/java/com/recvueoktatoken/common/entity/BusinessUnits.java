package com.recvueoktatoken.common.entity;


import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.NamedQueries;
import org.hibernate.annotations.NamedQuery;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;


@Entity
@Table(name = "BUSINESS_UNITS")
@NamedQueries({
    @NamedQuery(name = "BusinessUnits.findAll",
            query = "SELECT b FROM BusinessUnits b order by b.name"),
    @NamedQuery(name = "BusinessUnits.findBUBasedOnRole",
            query = "select bu from BusinessUnits bu, CoreUserRoles userRoles, CoreRoleBusinessUnit roleBasedBu "
                    + "where bu.orgId=roleBasedBu.orgId and roleBasedBu.roleId=userRoles.roleId and userRoles.coreUser.userId=:userId"),
    @NamedQuery(name = "BusinessUnits.findListOfOrgIdsBasedOnUserId",
            query = "select bu.orgId from BusinessUnits bu, CoreUserRoles userRoles, CoreRoleBusinessUnit roleBasedBu "
                    + "where bu.orgId=roleBasedBu.orgId and roleBasedBu.roleId=userRoles.roleId and userRoles.coreUser.userId=:userId"),
    @NamedQuery(name = "BusinessUnits.checkBULegalEntityAssoc", 
            query = "SELECT bu.name FROM BusinessUnits bu where bu.primaryLegalEntityId=:primaryLegalEntityId"),
    @NamedQuery(name = "BusinessUnits.getPrimaryLEForBU", 
            query = "SELECT bu.primaryLegalEntityId FROM BusinessUnits bu where bu.orgId=:orgId")
})
@org.hibernate.annotations.FilterDef(name = "restrictToCurrentBu",
        parameters = {
            @org.hibernate.annotations.ParamDef(
                    name = "orgIds", type = "java.lang.Long"
            )
        }
)
@org.hibernate.annotations.Filter(
        name = "restrictToCurrentBu",
        condition = "ORG_ID in (:orgIds)"
)
public class BusinessUnits extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @Column(name = "ORG_ID", unique = true, nullable = false)
    private Long orgId;

    @Column(name = "NAME")
    private String name;

    @Column(name = "DATE_FROM")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateFrom;

    @Column(name = "DATE_TO")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateTo;

    @Column(name = "STATUS")
    private String status;

    @Column(name = "ATTRIBUTE_CATEGORY")
    private String attributeCategory;

    @Column(name = "ACCOUNTING_PERIOD_FREQUENCY")
    private String acPeriodFrequency;

    @Column(name = "ATTRIBUTE1")
    private String attribute1;

    @Column(name = "ATTRIBUTE2")
    private String attribute2;

    @Column(name = "ATTRIBUTE3")
    private String attribute3;

    @Column(name = "ATTRIBUTE4")
    private String attribute4;

    @Column(name = "ATTRIBUTE5")
    private String attribute5;

    @Column(name = "ATTRIBUTE6")
    private String attribute6;

    @Column(name = "ATTRIBUTE7")
    private String attribute7;

    @Column(name = "ATTRIBUTE8")
    private String attribute8;

    @Column(name = "ATTRIBUTE9")
    private String attribute9;

    @Column(name = "ATTRIBUTE10")
    private String attribute10;

    @Column(name = "ATTRIBUTE11")
    private String attribute11;

    @Column(name = "ATTRIBUTE12")
    private String attribute12;

    @Column(name = "ATTRIBUTE13")
    private String attribute13;

    @Column(name = "ATTRIBUTE14")
    private String attribute14;

    @Column(name = "ATTRIBUTE15")
    private String attribute15;

    @Column(name = "ATTRIBUTE16")
    private String attribute16;

    @Column(name = "ATTRIBUTE17")
    private String attribute17;

    @Column(name = "ATTRIBUTE18")
    private String attribute18;

    @Column(name = "ATTRIBUTE19")
    private String attribute19;

    @Column(name = "ATTRIBUTE20")
    private String attribute20;

    @Column(name = "ORIG_SYSTEM_REFERENCE")
    private String origSystemReference;

    @Column(name = "ORIG_SYSTEM_REFERENCE_VALUE")
    private String origSystemReferenceValue;

    @Column(name = "ACCT_ADDRESS_SET_ID")
    private Long accAddressSetId;

    @Column(name = "BU_CODE")
    private String buCode;

    @Column(name = "PARENT_CODE")
    private String parentCode;

    @Column(name = "PARENT_NAME")
    private String parentName;

    @Column(name = "BILL_SCH_STATUS")
    private String billSchStatus;

    @Column(name = "SET_OF_BOOKS_ID")
    private Long setOfBooksId;

    @Column(name = "CONVERSION_TYPE")
    private String conversionType;

    @Column(name = "PRODUCT_CATALOG_ID")
    private Long productCatalogId;

    @Column(name = "INVOICE_TRX_TYPE")
    private String invoiceTrxType;

    @Column(name = "CREDIT_MEMO_TRX_TYPE")
    private String creditMemoTrxType;

    @Column(name = "ADJ_TRX_TYPE")
    private String adjTranType;

    @Column(name = "ADJ_CREDIT_TRX_TYPE")
    private String adjCreditTransactionType;
    
    @Column(name = "LEDGER")
    private String ledger;
    
    @Column(name = "PRIMARY_LEGAL_ENTITY_ID")
    private Long primaryLegalEntityId;

    @Transient
    private NumGenFormat numGenFormat;

    @Transient
    private boolean readOnly = false;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "pk.businessUnit", cascade = CascadeType.ALL)
    @NotFound(action = NotFoundAction.IGNORE)
    @BatchSize(size = 10)
    private Set<OrderTypeBUMapping> orderTypeBUMapping = new HashSet(0);

    /**
     *
     * @return
     */
    public String getBillSchStatus() {
        return billSchStatus;
    }

    /**
     *
     * @param billSchStatus
     */
    public void setBillSchStatus(String billSchStatus) {
        this.billSchStatus = billSchStatus;
    }

    /**
     *
     * @return
     */
    public Long getSetOfBooksId() {
        return setOfBooksId;
    }

    /**
     *
     * @param setOfBooksId
     */
    public void setSetOfBooksId(Long setOfBooksId) {
        this.setOfBooksId = setOfBooksId;
    }

    /**
     *
     */
    public BusinessUnits() {
    }

    /**
     *
     * @param orgId
     */
    public BusinessUnits(Long orgId) {
        this.orgId = orgId;
    }

    /**
     *
     * @return
     */
    public Long getOrgId() {
        return orgId;
    }

    /**
     *
     * @param orgId
     */
    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    /**
     *
     * @return
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @param name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     *
     * @return
     */
    public Date getDateFrom() {
        return dateFrom;
    }

    /**
     *
     * @param dateFrom
     */
    public void setDateFrom(Date dateFrom) {
        this.dateFrom = dateFrom;
    }

    /**
     *
     * @return
     */
    public Date getDateTo() {
        return dateTo;
    }

    /**
     *
     * @param dateTo
     */
    public void setDateTo(Date dateTo) {
        this.dateTo = dateTo;
    }

    /**
     *
     * @return
     */
    public String getStatus() {
        return status;
    }

    /**
     *
     * @param status
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     *
     * @return
     */
    public String getAttributeCategory() {
        return attributeCategory;
    }

    /**
     *
     * @param attributeCategory
     */
    public void setAttributeCategory(String attributeCategory) {
        this.attributeCategory = attributeCategory;
    }

    /**
     *
     * @return
     */
    public String getAttribute1() {
        return attribute1;
    }

    /**
     *
     * @param attribute1
     */
    public void setAttribute1(String attribute1) {
        this.attribute1 = attribute1;
    }

    /**
     *
     * @return
     */
    public String getAttribute2() {
        return attribute2;
    }

    /**
     *
     * @param attribute2
     */
    public void setAttribute2(String attribute2) {
        this.attribute2 = attribute2;
    }

    /**
     *
     * @return
     */
    public String getAttribute3() {
        return attribute3;
    }

    /**
     *
     * @param attribute3
     */
    public void setAttribute3(String attribute3) {
        this.attribute3 = attribute3;
    }

    /**
     *
     * @return
     */
    public String getAttribute4() {
        return attribute4;
    }

    /**
     *
     * @param attribute4
     */
    public void setAttribute4(String attribute4) {
        this.attribute4 = attribute4;
    }

    /**
     *
     * @return
     */
    public String getAttribute5() {
        return attribute5;
    }

    /**
     *
     * @param attribute5
     */
    public void setAttribute5(String attribute5) {
        this.attribute5 = attribute5;
    }

    /**
     *
     * @return
     */
    public String getAttribute6() {
        return attribute6;
    }

    /**
     *
     * @param attribute6
     */
    public void setAttribute6(String attribute6) {
        this.attribute6 = attribute6;
    }

    /**
     *
     * @return
     */
    public String getAttribute7() {
        return attribute7;
    }

    /**
     *
     * @param attribute7
     */
    public void setAttribute7(String attribute7) {
        this.attribute7 = attribute7;
    }

    /**
     *
     * @return
     */
    public String getAttribute8() {
        return attribute8;
    }

    /**
     *
     * @param attribute8
     */
    public void setAttribute8(String attribute8) {
        this.attribute8 = attribute8;
    }

    /**
     *
     * @return
     */
    public String getAttribute9() {
        return attribute9;
    }

    /**
     *
     * @param attribute9
     */
    public void setAttribute9(String attribute9) {
        this.attribute9 = attribute9;
    }

    /**
     *
     * @return
     */
    public String getAttribute10() {
        return attribute10;
    }

    /**
     *
     * @param attribute10
     */
    public void setAttribute10(String attribute10) {
        this.attribute10 = attribute10;
    }

    /**
     *
     * @return
     */
    public String getAttribute11() {
        return attribute11;
    }

    /**
     *
     * @param attribute11
     */
    public void setAttribute11(String attribute11) {
        this.attribute11 = attribute11;
    }

    /**
     *
     * @return
     */
    public String getAttribute12() {
        return attribute12;
    }

    /**
     *
     * @param attribute12
     */
    public void setAttribute12(String attribute12) {
        this.attribute12 = attribute12;
    }

    /**
     *
     * @return
     */
    public String getAttribute13() {
        return attribute13;
    }

    /**
     *
     * @param attribute13
     */
    public void setAttribute13(String attribute13) {
        this.attribute13 = attribute13;
    }

    /**
     *
     * @return
     */
    public String getAttribute14() {
        return attribute14;
    }

    /**
     *
     * @param attribute14
     */
    public void setAttribute14(String attribute14) {
        this.attribute14 = attribute14;
    }

    /**
     *
     * @return
     */
    public String getAttribute15() {
        return attribute15;
    }

    /**
     *
     * @param attribute15
     */
    public void setAttribute15(String attribute15) {
        this.attribute15 = attribute15;
    }

    /**
     *
     * @return
     */
    public String getAttribute16() {
        return attribute16;
    }

    /**
     *
     * @param attribute16
     */
    public void setAttribute16(String attribute16) {
        this.attribute16 = attribute16;
    }

    /**
     *
     * @return
     */
    public String getAttribute17() {
        return attribute17;
    }

    /**
     *
     * @param attribute17
     */
    public void setAttribute17(String attribute17) {
        this.attribute17 = attribute17;
    }

    /**
     *
     * @return
     */
    public String getAttribute18() {
        return attribute18;
    }

    /**
     *
     * @param attribute18
     */
    public void setAttribute18(String attribute18) {
        this.attribute18 = attribute18;
    }

    /**
     *
     * @return
     */
    public String getAttribute19() {
        return attribute19;
    }

    /**
     *
     * @param attribute19
     */
    public void setAttribute19(String attribute19) {
        this.attribute19 = attribute19;
    }

    /**
     *
     * @return
     */
    public String getAttribute20() {
        return attribute20;
    }

    /**
     *
     * @param attribute20
     */
    public void setAttribute20(String attribute20) {
        this.attribute20 = attribute20;
    }

    /**
     *
     * @return
     */
    public String getOrigSystemReference() {
        return origSystemReference;
    }

    /**
     *
     * @param origSystemReference
     */
    public void setOrigSystemReference(String origSystemReference) {
        this.origSystemReference = origSystemReference;
    }

    /**
     *
     * @return
     */
    public String getOrigSystemReferenceValue() {
        return origSystemReferenceValue;
    }

    /**
     *
     * @param origSystemReferenceValue
     */
    public void setOrigSystemReferenceValue(String origSystemReferenceValue) {
        this.origSystemReferenceValue = origSystemReferenceValue;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (orgId != null ? orgId.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof BusinessUnits)) {
            return false;
        }
        BusinessUnits other = (BusinessUnits) object;
        if ((this.orgId == null && other.orgId != null) || (this.orgId != null && !this.orgId.equals(other.orgId))) {
            return false;
        }
        return true;
    }

    /**
     *
     * @return
     */
    public Long getAccAddressSetId() {
        return accAddressSetId;
    }

    /**
     *
     * @param accAddressSetId
     */
    public void setAccAddressSetId(Long accAddressSetId) {
        this.accAddressSetId = accAddressSetId;
    }

    /**
     *
     * @return
     */
    public String getBuCode() {
        return buCode;
    }

    /**
     *
     * @param buCode
     */
    public void setBuCode(String buCode) {
        this.buCode = buCode;
    }

    /**
     *
     * @return
     */
    public String getParentCode() {
        return parentCode;
    }

    /**
     *
     * @param parentCode
     */
    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    /**
     *
     * @return
     */
    public String getParentName() {
        return parentName;
    }

    /**
     *
     * @param parentName
     */
    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    /**
     *
     * @return
     */
    public String getConversionType() {
        return conversionType;
    }

    /**
     *
     * @param conversionType
     */
    public void setConversionType(String conversionType) {
        this.conversionType = conversionType;
    }

    /**
     *
     * @return
     */
    public String getAcPeriodFrequency() {
        return acPeriodFrequency;
    }

    /**
     *
     * @param acPeriodFrequency
     */
    public void setAcPeriodFrequency(String acPeriodFrequency) {
        this.acPeriodFrequency = acPeriodFrequency;
    }

    @Override
    public String toString() {
        return "com.infovity.apps.adea.model.order.entity.BusinessUnits[ orgId=" + orgId + " ]";
    }

    /**
     *
     * @return
     */
    public NumGenFormat getNumGenFormat() {
        return numGenFormat;
    }

    /**
     *
     * @param numGenFormat
     */
    public void setNumGenFormat(NumGenFormat numGenFormat) {
        this.numGenFormat = numGenFormat;
    }

    /**
     *
     * @return
     */
    public Set<OrderTypeBUMapping> getOrderTypeBUMapping() {
        return orderTypeBUMapping;
    }

    /**
     *
     * @param orderTypeBUMapping
     */
    public void setOrderTypeBUMapping(Set<OrderTypeBUMapping> orderTypeBUMapping) {
        this.orderTypeBUMapping = orderTypeBUMapping;
    }

    /**
     *
     * @return
     */
    public boolean isReadOnly() {
        return readOnly;
    }

    /**
     *
     * @param readOnly
     */
    public void setReadOnly(boolean readOnly) {
        this.readOnly = readOnly;
    }

    /**
     *
     * @return
     */
    public Long getProductCatalogId() {
        return productCatalogId;
    }

    /**
     *
     * @param productCatalogId
     */
    public void setProductCatalogId(Long productCatalogId) {
        this.productCatalogId = productCatalogId;
    }

    /**
     *
     * @return
     */
    public String getInvoiceTrxType() {
        return invoiceTrxType;
    }

    /**
     *
     * @param invoiceTrxType
     */
    public void setInvoiceTrxType(String invoiceTrxType) {
        this.invoiceTrxType = invoiceTrxType;
    }

    /**
     *
     * @return
     */
    public String getCreditMemoTrxType() {
        return creditMemoTrxType;
    }

    /**
     *
     * @param creditMemoTrxType
     */
    public void setCreditMemoTrxType(String creditMemoTrxType) {
        this.creditMemoTrxType = creditMemoTrxType;
    }

    /**
     *
     * @return
     */
    public String getAdjTranType() {
        return adjTranType;
    }

    /**
     *
     * @param adjTranType
     */
    public void setAdjTranType(String adjTranType) {
        this.adjTranType = adjTranType;
    }

    /**
     *
     * @return
     */
    public String getAdjCreditTransactionType() {
        return adjCreditTransactionType;
    }

    /**
     *
     * @param adjCreditTransactionType
     */
    public void setAdjCreditTransactionType(String adjCreditTransactionType) {
        this.adjCreditTransactionType = adjCreditTransactionType;
    }

    public String getLedger() {
        return ledger;
    }

    public void setLedger(String ledger) {
        this.ledger = ledger;
    }

    public Long getPrimaryLegalEntityId() {
        return primaryLegalEntityId;
    }

    public void setPrimaryLegalEntityId(Long primaryLegalEntityId) {
        this.primaryLegalEntityId = primaryLegalEntityId;
    }

}
