/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.common.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

/**
 * The persistent class for the CORE_LOOKUP_VALUES database table.
 *
 */

@Entity
@Table(name = "CORE_ROLE_BU")
@NamedQuery(name = "CoreRoleBusinessUnit.findAll", query = "SELECT c FROM CoreRoleBusinessUnit c")
@org.hibernate.annotations.FilterDef(name = "restrictToCurrentTenant",
        parameters = {
            @org.hibernate.annotations.ParamDef(
                    name = "tenant", type = "java.lang.Long"
            )
        }
)
@org.hibernate.annotations.Filter(
        name = "restrictToCurrentTenant",
        condition = "TENANT_ID = :tenant"
)
public class CoreRoleBusinessUnit implements Serializable {

    private static final Long serialVersionUID = 1L;
    
    @Id
    @Column(name = "ROLE_ID")
    private Long roleId;

    @Id
    @Column(name = "ORG_ID")
    private Long orgId;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "ORG_ID", insertable = false, updatable = false)
    private BusinessUnits businessUnits;

    @Id
    @Column(name = "TENANT_ID", nullable = false, updatable = false)
    private Long tenantId;
   
    @Column(name = "CREATION_DATE", nullable = true)
    @Temporal(TemporalType.TIMESTAMP)
    private Date creationDate;

    @Column(name = "CREATED_BY", nullable = true)
    private Long createdBy;

    @Column(name = "LAST_UPDATED_BY", nullable = true)
    private Long lastUpdatedBy;

    @Column(name = "LAST_UPDATE_DATE", nullable = true)
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastUpdateDate;
    
    @Column(name = "RDONLY_FLAG", length = 30, nullable = true)
    private String rdOnlyFlag;

    @Column(name = "DEFAULT_BU_FLAG", length = 1, nullable = true)
    private String defaultBuFlag;
    
    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Long getTenantId() {
         return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public Long getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Long lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }
    
    public BusinessUnits getBusinessUnits() {
        return businessUnits;
    }

    public void setBusinessUnits(BusinessUnits businessUnits) {
        this.businessUnits = businessUnits;
    }

    public String getRdOnlyFlag() {
        return rdOnlyFlag;
    }

    public void setRdOnlyFlag(String rdOnlyFlag) {
        this.rdOnlyFlag = rdOnlyFlag;
    }
    
    public CoreRoleBusinessUnit() {
    }

    public String getDefaultBuFlag() {
        return defaultBuFlag;
    }

    public void setDefaultBuFlag(String defaultBuFlag) {
        this.defaultBuFlag = defaultBuFlag;
    }

    @Override
    public String toString() {
        return "CoreRoleBusinessUnit{" + "roleId=" + roleId + ", orgId=" + orgId + ", businessUnits=" + businessUnits + ", tenantId=" + tenantId + ", creationDate=" + creationDate + ", createdBy=" + createdBy + ", lastUpdatedBy=" + lastUpdatedBy + ", lastUpdateDate=" + lastUpdateDate + ", rdOnlyFlag=" + rdOnlyFlag + '}';
    }

}
