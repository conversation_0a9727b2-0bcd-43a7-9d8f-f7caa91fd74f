/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.common.dao.impl;


import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.recvueoktatoken.common.multitenancy.TenantContext;
import org.hibernate.Cache;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Repository;

import com.recvueoktatoken.common.dao.GenericDAO;
import com.recvueoktatoken.common.entity.BaseEntity;
import com.recvueoktatoken.common.entity.CoreRoleBusinessUnit;
import com.recvueoktatoken.common.entity.CoreRoles;
import com.recvueoktatoken.common.entity.User;

@Repository
public class GenericDAOImpl<E, I extends Serializable> implements GenericDAO<E, I> {

    private Class<E> entityClass;

    @Autowired
    private SessionFactory sessionFactory;
    
    private User userObject;

    private Long tenant;

    private Long createdBy = -1l;

    private Long lastUpdatedBy = -1l;

    private Date creationDate = new Date();
    private Date lastUpdateDate = new Date();
    
    void init() {

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (null != authentication) {
            setUserObject((User) authentication.getPrincipal());
            if (null != getUserObject()) {

                setTenant(getUserObject().getTenantId());
                setCreatedBy(getUserObject().getUserId());
                setLastUpdatedBy(getUserObject().getUserId());

            }
            setCreationDate(new Date());
            setLastUpdateDate(new Date());
        }

    }

    @Override
    public Session getCurrentSession() {
        //init();
        Session session = getSessionFactory().getCurrentSession();
        /*if (null != getTenant()) {
            
            session.enableFilter("restrictToCurrentTenant").setParameter("tenant", getTenant());
        }*/
        
      /*  if (null != getBusinessUnits()) {
            List<Long> tempList = new ArrayList<>();
           if(getBusinessUnits().isEmpty()) {               
               tempList.add(new Long(999999999));               
           }else{
               tempList.addAll(getBusinessUnits());
           }               
            session.enableFilter("restrictToCurrentBu").setParameterList("orgIds", tempList);
        } */
//        if (null != getBuAccAddrSets()) { 
//            List<Long> tempList = new ArrayList<>();
//            tempList.add(-1l);
//            if(!getBuAccAddrSets().isEmpty()) {               
//                tempList.addAll(getBuAccAddrSets());
//            }             
//            session.enableFilter("restrictToCurrentBuSet").setParameterList("busetIds", tempList);
//        } 
        return session;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<E> findAll() throws DataAccessException {
        return getCurrentSession().createCriteria(getEntityClass()).list();
    }

    @Override
    @SuppressWarnings("unchecked")
    public E find(I id) {
        return (E) getCurrentSession().get(getEntityClass(), id);
    }

    @Override
    public void saveOrUpdate(E e) {

        getCurrentSession().saveOrUpdate(e);
    }

    @Override
    public void delete(E e) {
        getCurrentSession().delete(e);
    }

    @Override
    public void flush() {
        getCurrentSession().flush();
    }

    /**
     * @return the userObject
     */
    public User getUserObject() {
        return userObject;
    }

    /**
     * @param userObject the userObject to set
     */
    public void setUserObject(User userObject) {
        this.userObject = userObject;
    }

    @Override
    public E save(E e) {
        getCurrentSession().save(e);
        return e;
    }

    @Override
    public void update(E e) {
        getCurrentSession().update(e);
    }

    /**
     * @return the sessionFactory
     */
    public SessionFactory getSessionFactory() {
        return sessionFactory;
    }

    /**
     * @param sessionFactory the sessionFactory to set
     */
    public void setSessionFactory(SessionFactory sessionFactory) {
        this.sessionFactory = sessionFactory;
    }

    @Override
    public void checkWho(E e) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    /**
     * @return the entityClass
     */
    public Class<E> getEntityClass() {
        return entityClass;
    }

    /**
     * @param entityClass the entityClass to set
     */
    public void setEntityClass(Class<E> entityClass) {
        this.entityClass = entityClass;
    }

    /**
     * @return the tenant
     */
    public Long getTenant() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (null != authentication) {
            setUserObject((User) authentication.getPrincipal());
            if (null != getUserObject()) {
                tenant = getUserObject().getTenantId();
            }
        }

        return tenant;
    }
    
    
     public List<Long> getBusinessUnits() {         
        List<Long> businessUnits = new ArrayList<>();        
        List<CoreRoleBusinessUnit> tempBu = null;        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();        
        if (null != authentication) {            
            setUserObject((User) authentication.getPrincipal());
           if (null != getUserObject() && null != getUserObject().getRoles() 
                    && getUserObject().getRoles().size() > 0) {
                for(CoreRoles role:getUserObject().getRoles()){
                   tempBu = (List<CoreRoleBusinessUnit>) role.getBusinessUnits();
                   if(null != tempBu && tempBu.size() > 0){
                        for(CoreRoleBusinessUnit bu:tempBu){
                           businessUnits.add(bu.getOrgId());
                       }                       
                   }
                   
                }                
            }
        }
        return businessUnits;
    }

    public List<Long> getBuAccAddrSets() {
        List<Long> buAccAddrSets = new ArrayList<>();        
        List<CoreRoleBusinessUnit> tempBu = null;        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();        
        if (null != authentication) {            
            setUserObject((User) authentication.getPrincipal());
           if (null != getUserObject() && null != getUserObject().getRoles() 
                    && getUserObject().getRoles().size() > 0) {
                for(CoreRoles role:getUserObject().getRoles()){
                   tempBu = (List<CoreRoleBusinessUnit>) role.getBusinessUnits();
                   if(null != tempBu && tempBu.size() > 0){
                        for(CoreRoleBusinessUnit bu:tempBu){
                           if(bu.getBusinessUnits().getAccAddressSetId() != null){
                               buAccAddrSets.add(bu.getBusinessUnits().getAccAddressSetId()); 
                           }
                       }                       
                   }
                }                
            }
        }
        return buAccAddrSets;
    }


    /**
     * @param tenant the tenant to set
     */
    public void setTenant(Long tenant) {
        this.tenant = tenant;
    }

    /**
     * @return the createdBy
     */
    public Long getCreatedBy() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (null != authentication) {
            setUserObject((User) authentication.getPrincipal());
            if (null != getUserObject()) {
                createdBy = getUserObject().getUserId();
            }
        }
        return createdBy;
    }

    /**
     * @param createdBy the createdBy to set
     */
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * @return the lastUpdatedBy
     */
    public Long getLastUpdatedBy() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (null != authentication) {
            setUserObject((User) authentication.getPrincipal());
            if (null != getUserObject()) {
                lastUpdatedBy = getUserObject().getUserId();
            }
        }
        return lastUpdatedBy;
    }

    /**
     * @param lastUpdatedBy the lastUpdatedBy to set
     */
    public void setLastUpdatedBy(Long lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    /**
     * @return the creationDate
     */
    public Date getCreationDate() {
        creationDate = new Date();
        return creationDate;
    }

    /**
     * @param creationDate the creationDate to set
     */
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    /**
     * @return the lastUpdateDate
     */
    public Date getLastUpdateDate() {
        lastUpdateDate = new Date();
        return lastUpdateDate;
    }

    /**
     * @param lastUpdateDate the lastUpdateDate to set
     */
    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Date retDateFromStr(String dt, String srcFmt) {

        SimpleDateFormat formatter = new SimpleDateFormat(srcFmt);
        String dateInString = dt;
        Date date = new Date();
        try {
            date = formatter.parse(dateInString);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return date;
    }

    @Override
    public void clearCache(){
        Cache cache = sessionFactory.getCache();
        if (cache != null) {
            cache.evictEntityRegions();
            cache.evictQueryRegions();
            cache.evictDefaultQueryRegion();
        }
    }
    
   /* @Override
    public void clearLookUpCache(){
        Cache cache = sessionFactory.getCache();
        if (cache != null) {
            cache.evictEntityRegion(CoreLookupsV.class);
        }
    }
    */
    
    /*public void populateCRUDFlags(BaseEntity entity){
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if(user.getPermissionMap() != null && user.getPermissionMap().containsKey(ConfigUtil.entityRoleMap.get(entity.getClass().getName()))){
            List<String> permList = user.getPermissionMap().get(ConfigUtil.entityRoleMap.get(entity.getClass().getName()));
            if(permList != null && permList.size() >0){
                if(permList.contains("r") && !( permList.contains("c") ||
                        permList.contains("u"))){
                    entity.setReadonlyFlag(true);
                }
                if( permList.contains("c") || permList.contains("u")){
                    entity.setUpdateFlag(true);
                }
                if( permList.contains("d")){
                    entity.setDeleteFlag(true);
                }
                if( permList.contains("r")){
                    entity.setReadFlag(true);
                }
            }
        }
    }
    */
    
    @Override
    public void populateAuditCol(BaseEntity obj, boolean isCreate){
        if(isCreate){
           obj.setCreatedBy(getCreatedBy());
           obj.setCreationDate(getCreationDate());
        }
        obj.setTenantId(getTenant());
        obj.setLastUpdatedBy(getLastUpdatedBy());
        obj.setLastUpdateDate(getLastUpdateDate());
    }

    public List<Long> getRdOnlybusinessUnits() {
        List<Long> rdOnlybusinessUnits = new ArrayList<>();        
        List<CoreRoleBusinessUnit> tempBu = null;        
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();        
        if (null != authentication) {            
            setUserObject((User) authentication.getPrincipal());
           if (null != getUserObject() && null != getUserObject().getRoles() 
                    && getUserObject().getRoles().size() > 0) {
                for(CoreRoles role:getUserObject().getRoles()){
                   tempBu = (List<CoreRoleBusinessUnit>) role.getBusinessUnits();
                   if(null != tempBu && tempBu.size() > 0){
                        for(CoreRoleBusinessUnit bu:tempBu){
                            if(!String.valueOf(bu.getRdOnlyFlag()).equals("null") && bu.getRdOnlyFlag().equals("Y"))
                                rdOnlybusinessUnits.add(bu.getOrgId());
                       }                       
                   }
                   
                }                
            }
        }
        return rdOnlybusinessUnits;
    }
    
    @Override
    public Long getRecordCount(Class c) {
        return (Long) getCurrentSession().createCriteria(c)
                .addOrder(Order.asc("orderNumber").ignoreCase())
                .setProjection(Projections.rowCount()).uniqueResult();
    }
    
}
