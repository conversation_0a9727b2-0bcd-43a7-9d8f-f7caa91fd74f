package com.recvueoktatoken.common.dao.impl;

import java.math.BigDecimal;
import java.util.List;

import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.recvueoktatoken.common.dao.ApiAuthUserDAO;
import com.recvueoktatoken.common.entity.CoreUser;
import com.recvueoktatoken.common.entity.User;


@Repository
public class ApiAuthUserDAOImpl extends GenericDAOImpl implements ApiAuthUserDAO {
    
    @Autowired
    private SessionFactory sessionFactory;

    
    @Override
    public User getUserByName(String userName) {
        
        List<CoreUser> users = sessionFactory.getCurrentSession()
                .createQuery("from CoreUser cu left outer join fetch cu.coreRoles where cu.userName=?")
                .setParameter(0, userName)
                .list();
        
        if (users.size() > 0) {
            CoreUser coreUser = users.get(0);
            User user = new User();
            user.setFirstName(coreUser.getFirstName());
            user.setFirstName(coreUser.getLastName());
            user.setUsername(coreUser.getUserName());
            user.setTenantId(coreUser.getTenantId());
            user.setPassword(coreUser.getEncryptedUserPassword());
            user.setUserId(coreUser.getUserId());
            user.setEmailAddress(coreUser.getEmailAddress());
            user.setFullName(coreUser.getFullName());
            user.setTitle(coreUser.getTitle());
            user.setCompany(coreUser.getCompany());
            user.setCity(coreUser.getCity());
            user.setState(coreUser.getState());
            user.setCountry(coreUser.getCountry());
            user.setPicture(coreUser.getPicture());
            user.setTimezone(coreUser.getTimezone());
            user.setRoles(coreUser.getCoreRoles());
            user.setStartDate(coreUser.getStartDate());
            user.setEndDate(coreUser.getEndDate());
            user.setStatus(coreUser.getStatus());
            user.setCurrencyCode(coreUser.getCurrencyCode());
            user.setEmployeeId(new BigDecimal(coreUser.getEmployeeId() == null ? 0 : coreUser.getEmployeeId()));
            user.setDefaultUIPreference(coreUser.getDefaultUIPreference());
            user.setDefaultHomeTab(coreUser.getDefaultHomeTab());
            user.setUserBoards(coreUser.getUserBoards());
            user.setDateFormat(coreUser.getDateFormat());
            user.setNumberFormat(coreUser.getNumberFormat());
            user.setTimeFormat(coreUser.getTimeFormat());
            user.setReadOnlyUser(coreUser.getReadOnlyUser());
            user.setDefaultDashboard(coreUser.getDefaultDashboard());
            return user;
        } else {
            return null;
        }
    }
    
    @Override
    public User getUserById(Long userId) {
        
        List<CoreUser> users = sessionFactory.getCurrentSession()
                .createQuery("from CoreUser cu left outer join fetch cu.coreRoles  where cu.userId=?")
                .setParameter(0, userId)
                .list();
        
        if (users.size() > 0) {
            CoreUser coreUser = users.get(0);
            User user = new User();
            user.setFirstName(coreUser.getFirstName());
            user.setLastName(coreUser.getLastName());
            user.setUsername(coreUser.getUserName());
            user.setTenantId(coreUser.getTenantId());
            user.setUserId(coreUser.getUserId());
            user.setEmailAddress(coreUser.getEmailAddress());
            user.setFullName(coreUser.getFullName());
            user.setTitle(coreUser.getTitle());
            user.setCompany(coreUser.getCompany());
            user.setCity(coreUser.getCity());
            user.setState(coreUser.getState());
            user.setCountry(coreUser.getCountry());
            user.setPicture(coreUser.getPicture());
            user.setTimezone(coreUser.getTimezone());
            user.setRoles(coreUser.getCoreRoles());
            user.setStartDate(coreUser.getStartDate());
            user.setEndDate(coreUser.getEndDate());
            user.setStatus(coreUser.getStatus());
            user.setCurrencyCode(coreUser.getCurrencyCode());
            return user;
        } else {
            return null;
        }
    }
    
    @Override
    public String getPassword(String userName) {
        List<CoreUser> users = getCurrentSession()
                .createQuery("from CoreUser where userName=?")
                .setParameter(0, userName)
                .list();
        if (users.size() > 0) {
            return users.get(0).getEncryptedUserPassword();
        } else {
            return null;
        }
    }

    public SessionFactory getSessionFactory() {
        return sessionFactory;
    }

    public void setSessionFactory(SessionFactory sessionFactory) {
        this.sessionFactory = sessionFactory;
    }
}
