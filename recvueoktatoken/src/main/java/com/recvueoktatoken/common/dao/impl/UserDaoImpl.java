package com.recvueoktatoken.common.dao.impl;

import java.util.List;

import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.recvueoktatoken.common.dao.UserDao;
import com.recvueoktatoken.common.entity.CoreUser;
import com.recvueoktatoken.common.entity.User;
import com.recvueoktatoken.common.entity.UserAttributes;

@Repository
public class UserDaoImpl implements UserDao{

	@Autowired
	private SessionFactory sessionFactory;
	
	@Override
	public List<UserAttributes> getCoreUserAttributeByUserId(Long userId) {
	    return sessionFactory.getCurrentSession()
	        .createNamedQuery("UserAttributes.getUserAttributesByUserId", UserAttributes.class)
	        .setParameter("userId", userId)
	        .setHint("org.hibernate.readOnly", true)
	        .getResultList();
	}

	 @Override
	    public User getUserById(Long userId) {

	        List<CoreUser> users = sessionFactory.getCurrentSession()
	                .createQuery("from CoreUser cu left outer join fetch cu.coreRoles  where cu.userId=?")
	                .setParameter(0, userId)
	                .list();

	        if (users.size() > 0) {
	            CoreUser coreUser = users.get(0);
	            User user = new User();
	            user.setFirstName(coreUser.getFirstName());
	            user.setLastName(coreUser.getLastName());
	            user.setUsername(coreUser.getUserName());
	            user.setTenantId(coreUser.getTenantId());
	            user.setUserId(coreUser.getUserId());
	            user.setEmailAddress(coreUser.getEmailAddress());
	            user.setFullName(coreUser.getFullName());
	            user.setTitle(coreUser.getTitle());
	            user.setCompany(coreUser.getCompany());
	            user.setCity(coreUser.getCity());
	            user.setState(coreUser.getState());
	            user.setCountry(coreUser.getCountry());
	            user.setPicture(coreUser.getPicture());
	            user.setTimezone(coreUser.getTimezone());
	            user.setRoles(coreUser.getCoreRoles());
	            user.setStartDate(coreUser.getStartDate());
	            user.setEndDate(coreUser.getEndDate());
	            user.setStatus(coreUser.getStatus());
	            user.setCurrencyCode(coreUser.getCurrencyCode());
	            user.setDefaultUIPreference(coreUser.getDefaultUIPreference());
	            user.setDefaultHomeTab(coreUser.getDefaultHomeTab());
	            user.setUserBoards(coreUser.getUserBoards());
	            return user;
	        } else {
	            return null;
	        }
	    }

}
