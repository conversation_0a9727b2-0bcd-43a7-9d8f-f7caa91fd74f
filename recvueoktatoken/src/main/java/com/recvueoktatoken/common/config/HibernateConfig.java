package com.recvueoktatoken.common.config;

import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.hibernate.MultiTenancyStrategy;
import org.hibernate.cfg.Environment;
import org.hibernate.context.spi.CurrentTenantIdentifierResolver;
import org.hibernate.engine.jdbc.connections.spi.MultiTenantConnectionProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;

@Configuration
public class HibernateConfig {

	@Autowired
	private org.springframework.core.env.Environment env;

	@Bean
	@Primary
	public LocalContainerEntityManagerFactoryBean entityManagerFactory(DataSource dataSource,
			MultiTenantConnectionProvider multiTenantConnectionProviderImpl,
			CurrentTenantIdentifierResolver currentTenantIdentifierResolverImpl) {
		System.out.println("In HibernateConfig LocalContainerEntityManagerFactoryBean ");
    	
		
		Map<String, Object> properties = new HashMap<>();

		// properties.putAll(jpaProperties.getHibernateProperties(dataSource));
		properties.putAll(additionalProperties());
		properties.put(Environment.MULTI_TENANT, MultiTenancyStrategy.SCHEMA);
		properties.put(Environment.MULTI_TENANT_CONNECTION_PROVIDER, multiTenantConnectionProviderImpl);
		properties.put(Environment.MULTI_TENANT_IDENTIFIER_RESOLVER, currentTenantIdentifierResolverImpl);

		LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(dataSource);
		em.setPackagesToScan("com.recvueoktatoken");
		em.setJpaVendorAdapter(new HibernateJpaVendorAdapter());
		em.setJpaPropertyMap(properties);
		return em;
	}

	private Map<String, Object> additionalProperties() {
		Map<String, Object> additionalPropertiesMap = new HashMap<>();
		additionalPropertiesMap.put("hibernate.dialect", env.getProperty("spring.jpa.database-platform"));
		additionalPropertiesMap.put("hibernate.current_session_context_class",
				env.getProperty("spring.jpa.properties.hibernate.current_session_context_class"));
		return additionalPropertiesMap;
	}
	
	
	
}
