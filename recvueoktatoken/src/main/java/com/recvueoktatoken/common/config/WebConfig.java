package com.recvueoktatoken.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.MappedInterceptor;

import com.recvueoktatoken.common.multitenancy.TenantInterceptor;


@Configuration

public class WebConfig implements WebMvcConfigurer {

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(new TenantInterceptor());
	}
	
	
	@Override
	  public void addCorsMappings(CorsRegistry registry) {
	    registry.addMapping("/**")
	    		.allowedMethods("*")
	    		.allowedOrigins("*");
	  }
	
	@Bean
	public MappedInterceptor interceptor(){
	    return new MappedInterceptor(null, new TenantInterceptor());
	}
	
}