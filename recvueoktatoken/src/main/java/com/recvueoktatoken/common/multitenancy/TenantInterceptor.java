package com.recvueoktatoken.common.multitenancy;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;


@Component
public class TenantInterceptor extends HandlerInterceptorAdapter {

	private static final String TENANT_HEADER_NAME = "TENANTIDENTIFIER";

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		String tenantId = request.getHeader(TENANT_HEADER_NAME);
		TenantContext.setCurrentTenant(tenantId);
		return true;
	}

	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {
		TenantContext.clear();
	}
}
