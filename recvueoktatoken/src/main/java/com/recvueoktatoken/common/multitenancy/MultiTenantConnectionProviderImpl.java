package com.recvueoktatoken.common.multitenancy;
	

import static com.recvueoktatoken.common.multitenancy.MultiTenantConstants.DEFAULT_TENANT_ID;
import java.sql.Connection;
import java.sql.SQLException;
import javax.sql.DataSource;
import org.hibernate.HibernateException;
import org.hibernate.engine.jdbc.connections.spi.MultiTenantConnectionProvider;
import org.springframework.beans.factory.annotation.Autowired;
import  org.springframework.stereotype.Component;

@Component
public class MultiTenantConnectionProviderImpl implements MultiTenantConnectionProvider {

    @Autowired
    private DataSource dataSource;

    @Override
    public Connection getAnyConnection() throws SQLException {
        return dataSource.getConnection();
    }

    @Override
    public void releaseAnyConnection(Connection connection) throws SQLException {
        connection.close();
    }

    /**
     * 
     * @param tenantIdentifier
     * @return
     * @throws SQLException 
     */
    @Override
    public Connection getConnection(String tenantIdentifier) throws SQLException {
        final Connection connection = getAnyConnection();
        try {
            if (tenantIdentifier != null) {
            	connection.createStatement().execute("ALTER SESSION SET container = " + tenantIdentifier); 
            	connection.createStatement().execute("ALTER SESSION SET CURRENT_SCHEMA = ADORB ");
            } else {
                connection.createStatement().execute("ALTER SESSION SET container = " + DEFAULT_TENANT_ID);
                connection.createStatement().execute("ALTER SESSION SET CURRENT_SCHEMA = ADORB ");
            }
       } catch (SQLException e) {
            try {
                releaseAnyConnection(connection);
            } catch (Exception ex) {
                throw new HibernateException("Could not alter JDBC connection to specified ADORB schema/container [" + tenantIdentifier + "]", ex);
            }
            throw new HibernateException("Could not alter JDBC connection to specified ADORB schema/container [" + tenantIdentifier + "]" + e.fillInStackTrace(), e.fillInStackTrace());
        }
        return connection;
    }

    /**
     * 
     * @param tenantIdentifier
     * @param connection
     * @throws SQLException 
     */
    @Override
    public void releaseConnection(String tenantIdentifier, Connection connection) throws SQLException {
        try {
            connection.createStatement().execute("ALTER SESSION SET container = " + tenantIdentifier);
            connection.createStatement().execute("ALTER SESSION SET CURRENT_SCHEMA = ADORB ");
        } catch (SQLException e) {
            throw new HibernateException("Could not alter JDBC connection to specified schema [" + tenantIdentifier + "]",e);
        }
        connection.close();
    }

    @SuppressWarnings("rawtypes")
    @Override
    public boolean isUnwrappableAs(Class unwrapType) {
        return false;
    }

    @Override
    public <T> T unwrap(Class<T> unwrapType) {
        return null;
    }

    @Override
    public boolean supportsAggressiveRelease() {
        return true;
    }
}