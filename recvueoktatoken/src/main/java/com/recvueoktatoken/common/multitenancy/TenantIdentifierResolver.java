package com.recvueoktatoken.common.multitenancy;

import static com.recvueoktatoken.common.multitenancy.MultiTenantConstants.DEFAULT_TENANT_ID;

import org.hibernate.context.spi.CurrentTenantIdentifierResolver;
import org.springframework.stereotype.Component;

@Component
public class TenantIdentifierResolver implements CurrentTenantIdentifierResolver {

	@Override
	public String resolveCurrentTenantIdentifier() {
		String tenantId = TenantContext.getCurrentTenant();
		if (tenantId != null) {
			return tenantId;
		}
		return DEFAULT_TENANT_ID ;
	}

	@Override
	public boolean validateExistingCurrentSessions() {
		return true;
	}
}
