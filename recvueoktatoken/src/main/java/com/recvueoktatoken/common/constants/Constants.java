/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.common.constants;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 *
 * <AUTHOR>
 */
public final class Constants {

    public static final BigDecimal DEFAULT_ORDER_VERSION = new BigDecimal(BigInteger.ONE);
    public static final String ORDER_CREATED = "New Order Created";
    public static final String ORDER_CREATED_DETAIL = "A new Order has been Created Successfully";
    public static final String DEFAULT_CURRENCY = "USD";
    public static final String ORDER_UPDATE_SUCCESS = "Order has been Updated Successfully";
    public static final String ORDER_UPDATE_FAIL = "Unable to Update the Order now!";
    public static final String RESULT_FILE_NAME = "ResultFileName";
    public static final String SSL_TRUSTSTORE = "javax.net.ssl.trustStore";
    public static final String CA_CERTS = "/u01/jdk/jre/lib/security/cacerts";
    public static final String JAVA_HOME = "$JAVA_HOME";
    public static final String TRUST_STORE_PASSWORD_PARAM = "javax.net.ssl.trustStorePassword";
    public static final String TRUST_STORE_PASSWORD_VALUE = "Infovity";
    public static final String ETL_PROJECT_DIR = "-P:PROJECT_DIR=";
    public static final String ETL_PLUGIN_DIR = "java:comp/env";
    public static final String LOG4J_PROPERTIES_FILENAME = "etl.properties";
    public static final String HCMINTEGRATOR_PROJECT_DIR = "/Customers/Common/HCMIntegrator";
    public static final String ADEAINTEGRATOR_PROJECT_DIR = "IntegrationProcess/Customers/Common/AdeaIntegrator";
    public static final String PLUGINS = "IntegrationProcess/cloverETL/plugins"; //"/plugins";
    public static final String SCRATCH_DIR = "ADEA_ETL_HOME";
    //"C:/ADORB/ADEA_PMF/"; //java:comp/env/infovity/scratchdir";
    public static final String JAVA_ENV = "java:comp/env";
    public static final String LOG4J_SCRATCH_DIR = "infovity/scratchdirlog4j";
    public static final String LOG4J_PROPERTIES_FILE_PATH = "/IntegrationProcess/Customers/Common/AdeaIntegrator/etl.properties";
    //"C:/ADORB_SVN/trunk/Web Application/Infovity/apps/ADEA/Adea-model/src/main/resources/log4j.properties";
    public static final String TALEO_AUTH_TOKEN
            = "-V:TaleoAuthToken:org.jetel.graph.dictionary.StringDictionaryType:value=null";
    public static final String GET_SUBPROCESSES_BY_PROCESS = "GetSubprocessByProcessIdQuery";
    public static final String GET_SUBPROCESS_PARAMS = "GetSubProcessParamsVC";
    public static final String PROCESS_ID = "processId";
    public static final String SUB_PROCESS_ID = "SubProcessId";
    public static final String PROCESSID = "ProcessId";
    public static final String JOB_ID = "JobId";
    public static final String SUBPROCESS_PARAMS = "subProcessParams";
    public static final String RESULT_FILE_DETAIL = "reslutsFileDetail";
    public static final String RESULT_FILENAME = "ResultFileName";
    public static final String JOBID_SUBST_PARAM = "{jobId}";
    public static final String PREV_JOBID_SUBST_PARAM = "{previousJobId}";
    public static final String LOG_FILE_PATH = "/IntegrationProcess/Customers/Common/AdeaIntegrator/Logs/";
    public static final String INTG_ROLLINGFILE_PATH = "/IntegrationProcess/Customers/Common/AdeaIntegrator/Logs/INTG_Log.log";
    // /home/<USER>/InfovityIntegrator/IntegrationProcess/Logs/INTG_Log.log
    public static final String INTG_APPENDER_FILE_PATH = "/IntegrationProcess/Customers/Common/AdeaIntegrator/Logs/INTG_Logger.log";
    //  /home/<USER>/InfovityIntegrator/IntegrationProcess/Logs/INTG_Logger.log
    public static final String LOG_FILE_NAME = "LogFileName";
    public static final String RESULTS_FILENAME = "ResultsFileName";
    public static final String STATUS = "Status";
    public static final String SUB_PROCESSID = "subProcessId";
    public static final String INTG_PROCESS_VO = "IntgIntegrationProcessesVO1";
    public static final String INTG_JOBS_VO = "IntgJobsVO1";
    public static final String INTG_JOBS_VO2 = "IntgJobsVO2";
    public static final String INTG_PROC_JOBS_VL = "IntgIntegrationProcessVOToIntgJobsVOLink1";
    public static final String INTG_SUBPROCESS_VO1 = "SubProcessVO1";
    public static final String INTG_SUBPROCESS_PARAMS_VO1 = "SubProcessParamsVO1";
    public static final String MESSAGE = "MESSAGE";
    public static final String FILE_UPLOAD_PATH = "/IntegrationProcess/Customers/Common/AdeaIntegrator/data-in";
    public static final String FILE_UPLOAD_RESULT_PATH = "/IntegrationProcess/Customers/Common/AdeaIntegrator/data-out/";
    public static final String REST_ORDER_DELIVERIES = "/api/v1/deliveries";
    public static final String REST_PRODUCTS = "/api/v1/products";
    public static final String REST_CUSTOMERS = "/api/v1/customers";
    public static final String REST_CUSTOMER_ACCOUNTS = "/api/v1/customerAccounts";
    public static final String REST_EXCHANGE_RATES = "/api/v1/exchangeRates";
    public static final String REST_TRACK_RECORDS_COUNT = "/api/v1/trackRecordsCount";
    public static final String REST_AR_INTERFACE = "/api/v1/arInterface/";
    public static final String REST_FIN_BACKFEED_INTERFACE = "/api/v1/finTrxBackFeed/";
    public static final String REST_SYNC_ERROR = "/api/v1/updateSyncErrors/";
    public static final String REST_BATCH_EXCHANGE_RATES = "/api/v1/batchExchangeRates";
    public static final String REST_SET_RECORDS_COUNT = "/api/v1/setRecordCount";
    public static final String REST_UPDATE_CONC_REQUEST = "/api/v1/setRequestStatus";
    public static final String REST_FUSION_INVOICE_SYNC = "/api/v1/createInvoice";
    public static final String DEFAULT_SYNC_ERROR_ACTION = "Fix";
    public static final String DEFAULT_LOG = "ADEA";
    public static final String BUSINESS_UNIT = "BUSINESS UNIT";
    public static final String BILLING_FREQUENCY = "BILLING_FREQUENCY";
    public static final String ORDER_STATUS = "ORDER_STATUS";
    public static final String ORDER_TYPE = "ORDER_TYPE";
    public static final String ORDER_CATEGORY = "ORDER_CATEGORY";
    public static final String BILLING_CYCLE = "BILLING_CYCLE";
    public static final String DELIVERY_COUNT = "DELIVERY_COUNT";
    public static final String DELIVERY_STATUS = "DELIVERY_STATUS";
    public static final String CONVERSION_TYPE = "CONVERSION_TYPE";
    public static final String SCH_TYPE = "REV_SCHEDULE";
    public static final String RECORD_STATUS = "RECORD_STATUS";
    public static final String SOURCE = "RECORD_SOURCE";
    public static final String ACCOUNT_NUMBER = "ACCOUNT_NUMBER";
    public static final String SITE_NUMBER = "SITE_NUMBER";
    public static final String BILLING_PERIOD_FROM = "BILLING_PERIOD_FROM";
    public static final String BILLING_PERIOD_TO = "BILLING_PERIOD_TO";
    public static final String TRANSACTION_START_DATE = "TRANSACTION_START_DATE";
    public static final String TRANSACTION_END_DATE = "TRANSACTION_END_DATE";
    public static final String BUSINESS_UNIT_NULL_PARAM = "BUSINESS_UNIT_NULL_PARAM";
    public static final String FUSION_INVOICE_NUMBER = "FUSION_INVOICE_NUMBER";
    public static final String FUSION_INVOICE_DATE = "FUSION_INVOICE_DATE";
    public static final String COMPANY_IDENTIFIER = "COMPANY_ID";
    public static final String REPORT_TYPE = "REPORT_TYPE";
    public static final String AS_OF_DATE = "AS_OF_DATE";
    public static final String AS_OF_DATE_PARAM = ":pi_as_of_date";

    public static final String DELIVERY_CHANNEL = "DELIVERY_CHANNEL";
    public static final String PAYMENT_METHODS = "PAYMENT_METHODS";
    public static final String OVERAGE_FREQUENCY = "OVERAGE_FREQUENCY";
    public static final String BILL_TO = "BILL_TO";
    public static final String SHIP_TO = "SHIP_TO";
    public static final String SELL_TO = "SELL_TO";

    public static final String FULFILLMENT_CHANNEL = "FULFILLMENT_CHANNEL";
    public static final String BILLING_CHANNEL = "BILLING_CHANNEL";

    public static final String LINE_STATUS = "ORDER_LINE_STATUS";
    public static final String LINE_TYPE = "LINE_TYPE";
    public static final String MAKE_GOOD_REVENUE = "MAKE_GOOD_REVENUE";
    public static final String MAKE_GOOD_BILLING = "MAKE_GOOD_BILLING";
    public static final String MAKE_GOOD_RESOLUTION = "MAKE_GOOD_RESOLUTION";
    public static final String FREE_MONTHS = "FREE_MONTHS";
    public static final Long PRF_FREE_MNTHS_ID = 15L;
    public static final Long PRF_FREE_MNTHS_DEF = 100L;
    public static final String TIER_PRICING = "TIER_PRICING";
    public static final String TIER_PRICING_SOURCE_TYPE = "TIER_PRICING_SOURCE_TYPE";
    public static final String TIER_PRICING_TYPE = "TIER_PRICING_PRICE_TYPE";
    public static final String TRACKING_OPTIONS = "TRACKING_OPTIONS";
    public static final String BILLING_TRIGGER = "BILLING_TRIGGER";
    public static final String OVER_DELIVERY = "OVER_DELIVERY";
    public static final String UNDER_DELIVERY = "UNDER_DELIVERY";

    public static final String BILLING_STATUS = "BILLING_STATUS";
    public static final String ORDER_LINE_BILLING_STATUS = "LINE_BILLING_STATUS";
    public static final String BILLING_TYPE = "BILLING_TYPE";
    public static final String BILLING_LINE_TYPE = "BILLING_LINE_TYPE";
    public static final String DEFAULT_ORDER_STATUS = "DRAFT";
    public static final String DEFAULT_ORDER_STATUS_NEW_LINE = "REACTIVATE";
    public static final String DEFAULT_LINE_STATUS = "DRAFT";
    public static final String DEFAULT_ACCOUNTING_RULE = "Immediate";
    public static final String LOOKUP_EDIT_LEVEL = "LOOKUP_EDIT_LEVEL";

    public static final String STATUS_ACTIVE = "ACTIVE";
    public static final String STATUS_PENDING_APPROVAL = "PENDING_APPROVAL";
    public static final String STATUS_HOLD = "ON_HOLD";
    public static final String DEFAULT_PRICE_LIST = "111";
    public static final String STRING_NEW = "NEW";
    public static final String INVALID_ORDER = "This Order is not available";

    public static final String FIRST = "FIRST";
    public static final String MONTHLY = "MONTHLY";
    public static final String WEEKLY = "WEEKLY";
    public static final String YEARLY = "YEARLY";
    public static final String QUARTERLY = "QUARTERLY";
    public static final String NUMBER_4 = "4";
    public static final String ADVANCE = "ADVANCE";
    public static final String ARREARS = "ARREARS";
    public static final String ADVERTISING = "DIRECT";
    public static final String SUBSCRIPTION = "SUBSCRIPTION";
    public static final String USAGE = "USAGE";
    public static final String LICENSE = "LICENSE";
    public static final String CONSULTING = "CONSULTING";
    public static final Long PRICELISTID = 100L;

    public static final String FULFILLMENT_STATUS = "FULFILLMENT_STATUS";

    public static final String FORMAT_PACK_TARGET = "FORMAT_PACK_TARGET";
    public static final String GEO = "GEO";

    //Constants for change Requests
    public static final String CR_TYPE = "CR_TYPE";
    public static final String CR_STATUS = "CR_STATUS";
    public static String CR_REASONCODE = "CR_REASON_CODE";
    public static String CR_IMPACT = "CR_IMPACT";
    public static String AMENDMENT_CHANGE_STATUS = "AMENDMENT_CHANGE_STATUS";
    public static final String DEFAULT_CR_STATUS = "CR DRAFT";
    public static final String CHANGE_REQUEST_UPDATE_SUCCESS = "Change Request has been Updated Successfully";
    public static final String CHANGE_REQUEST_CANCEL_ORDER_SUCCESS = "Change request to cancel order updated.";
    public static final String CHANGE_REQUEST_UNDO_CANCEL_ORDER_SUCCESS = "Order Cancel Reverted";
    public static final String AMENDMENT_PROFILE = "AmendmentProfile";
    public static final String AMENDMENT_PROCESS = "Process Amendments";
    public static final String AMENDMENT_PROCESS_FROM_ORDER = "Process Amendment from Amendment ORDER";

    public static final String UOM = "UOM";
    public static final String TERRITORY = "TERRITORY";

    public static final String INVOICING_RULE = "INVOICING_RULE";
    public static final String MANDATORY_F = "MANDATORY_F";

    public static final String DELIVERY_CHANNEL_V_ID = "tbv:viwOrdFrm:viewOrder:ViewdeliveryChannel";
    public static final String DELIVERY_CHANNEL_C_ID = "mainForm:createOrder:deliveryChannel";

    public static final String BILL_CHANNEL_V_ID = "tbv:viwOrdFrm:viewOrder:ViewbillChannel";
    public static final String BILL_CHANNEL_C_ID = "mainForm:createOrder:billChannel";

    public static final String BILLING_CYCLE_V_ID = "tbv:viwOrdFrm:viewOrder:ViewbillingCycle";
    public static final String BILLING_CYCLE_C_ID = "mainForm:createOrder:billingCycle";

    public static final String BILLING_FREQUENCY_V_ID = "tbv:viwOrdFrm:viewOrder:ViewbillingFrequency";
    public static final String BILLING_FREQUENCY_C_ID = "mainForm:createOrder:billingFrequency";

    public static final String INVOICING_RULE_V_ID = "tbv:viwOrdFrm:viewOrder:ViewinvoicingRule";
    public static final String INVOICING_RULE_C_ID = "mainForm:createOrder:invoicingRule";

    public static final String LINE_TYPE_ID = "tb:updateLines:viewLineAccord:lineType";
    public static final String END_DATE_V_ID = "tbv:viwOrdFrm:viewOrder:VieweffectiveEndDate";
    public static final String END_DATE_C_ID = "mainForm:createOrder:effectiveEndDate";

    public static final String END_DATE_V = "END_DATE_V";

    public static final String LAST_DAY_OF_THE_MONTH = "LAST_DAY_OF_THE_MONTH";

    public static final String PRODUCT_LIST = "PRODUCT_LIST";

    public static final String CONTACT_ROLE_TYPE = "CONTACT";

    public static final String ORG_PARTNER_TYPE = "ORGANIZATION";

    public static final String PERSON_PARTNER_TYPE = "PERSON";

    public static final String PARTNERS_TABLE = "PARTNERS";

    public static final String BILLING_STATUS_NEW = "SCHEDULED";
    public static final String BILLING_STATUS_INTERFACED = "INTERFACED";
    public static final String BILLING_STATUS_INVOICED = "INVOICED";
    public static final String BILLING_STATUS_INT_ERROR = "INT_ERROR";
    public static final String BILLING_STATUS_READY_TO_INTERFACE = "READY_TO_INTERFACE";

    public static final String BILLING_CONTINGENCY = "BILLING_CONTINGENCY";

    public static final String BILLING_LINE_TYPE_STANDARD = "STANDARD";

    public static final String PO_STATUS = "PO_STATUS";

    public static final String MANUAL_ORDER_FREQUENCY = "MANUAL";

    public static final String SYNC_PREF_EXT_SYSTEM = "SYNC_PREF_EXT_SYSTEM";

    public static final String SYNC_PREF_TRX_DFF = "SYNC_PREF_TRX_DFF";

    public static final String SYNC_PREF_TRX_TYPE = "SYNC_PREF_TRX_TYPE";

    public static final String PO_OPEN_STATUS = "OPEN";

    public static final String RPT_DAILY_DELIVERY = "RPT_DAILY_DELIVERY_DETAILS_V";
    public static final String RPT_MONTHLY_USAGE = "RPT_MONTHLY_USAGE_SUMMARY_V";
    public static final String RPT_MONTHLY_SUBS = "RPT_MONTHLY_SUBS_SUMMARY_V";
    public static final String RPT_MONTHLY_LBILL = "RPT_MON_LAST_BILL_V";

    public static final String LINE_STATUS_HOLD = "HOLD";
    public static final String CR_STATUS_SUBMITTED = "SUBMITTED";
    public static final String CR_STATUS_APPROVED = "APPROVED";
    public static final String CR_STATUS_DRAFT = "DRAFT";
    public static final String CR_STATUS_PARTIALLY_PROCESSED = "PARTIALLY_PROCESSED";
    public static final String CR_STATUS_PROCESSED = "PROCESSED";
    public static final String LINE_STATUS_TERMINATED = "TERMINATED";
    public static final String NEW_LINE_ADD_STATUS = "ADD";

    public static final String USER_STATUS = "USER_STATUS";
    public static final String USER_ACTIVE_STATUS = "A";
    public static final String USER_PENDING_STATUS = "P";
    public static final String USER_INACTIVE_STATUS = "I";

    public static final String COMPONENT_TYPE = "COMPONENT_TYPE";

    public static final String BILLING_LINE_TYPE_CM = "CREDIT_MEMO";
    public static final String BILLING_LINE_TYPE_DM = "DEBIT_MEMO";
    public static final String BILLING_LINE_TYPE_CR = "CREDIT_REBILL";
    public static final String BILLING_LINE_TYPE_DR = "DEBIT_REBILL";
    public static final String CD_MEMO_STATUS_NEW = "NEW";
    public static final String ADJ_REASON_CODE = "ADJ_REASON_CODE";

    public static final String SALES_REP_V_ID = "tbv:viwOrdFrm:viewOrder:ViewsalesRepc";
    public static final String ORDER_TYPE_V_ID = "tbv:viwOrdFrm:viewOrder:VieworderType";
    public static final String SALES_REP_C_ID = "mainForm:createOrder:salesRepc";
    public static final String EMPLOYEE_V_ID = "tbv:viwOrdFrm:viewOrder:Viewemloyee";
    public static final String TERRITORY_V_ID = "tbv:viwOrdFrm:viewOrder:vterritory";
    public static final String TERRITORY_C_ID = "mainForm:createOrder:territory";

    public static final String PRICING_NAME = "PRICING_NAME";
    public static final String PRICE_LIST_DEFAULT = "PRICE_LIST_DEFAULT";

    public static final String REVENUE_TYPE = "REVENUE_TYPE";
    public static final String UNITPRICE_TO_USAGE_FEE = "UNITPRICE_TO_USAGE_FEE";
    public static final String CAP_LINE_TYPE = "CAP_LINE_TYPE";
    public static final String USAGE_LINE_TYPE = "USAGE_LINE_TYPE";

    public static final int EXCHANGE_RATES_BATCH_SIZE = 5000;
    public static final String PRF_OBJ_TYPE_GENERAL = "GENERAL";
    public static final String PRF_OBJ_TYPE_OBJECT = "OBJECT";
    public static final String PRF_TEMPLATE_STANDARD = "STANDARD";
    public static final String PRF_TEMPLATE_CUSTOM = "Custom1";
    public static final String PRF_TEMPLATE_ALLTABS = "AllTabs";
    public static final String ROLE_DEFAULT = "DEF";
    public static final String PRF_ROLE_DEFAULT = "DEF";
    public static final String PRF_ROLE_ADMIN = "DEF_Admin";
    public static final String PRF_ROLE_BILLING_SPECIALIST = "DEF_BillingSpecialist";
    public static final String PRF_ROLE_BILLING_MANAGER = "DEF_BillingManager";
    public static final String PRF_ROLE_READONLY = "DEF_ReadOnly";
    public static final String PRF_ROLE_APPROVER = "DEF_Approver";
    public static final String RULE_CONDITION = "RULE_CONDITION";
    public static final String RULE_OPERATOR = "RULE_OPERATOR";
    public static final String READ_ONLY_RULE = "ReadOnlyRule";
    public static final String REVENUE_EVENT = "REVENUE_EVENT";
    public static final String PRF_OBJ_TYPE_FIELD = "FIELD";
    public static final String PRF_OBJ_TYPE_TAB = "TAB";

    public static final String REV_ALLOC_STATUS = "REV_ALLOC_STATUS";
    public static final String REV_ALLOC_TYPE = "REV_ALLOC_TYPE";
    public static final String MANUAL_INVOICE = "MANUAL";
    public static final String LINE_BILLING_STATUS = "BILLED";

    public static final String BILL_SCH_STATUS = "BILL_SCH_STATUS";
    public static final String BILL_SCH_STATUS_NEW = "NEW";
    public static final String BILL_SCH_STATUS_REVIEW = "REVIEW";
    public static final String BILL_SCH_STATUS_APPROVED = "APPROVED";
    public static final String BILL_SCH_STATUS_SCHEDULED = "SCHEDULED";
    public static final String BILL_SCH_STATUS_SRCH = "BILLING_REVIEW_SEARCH";
    public static final String BILL_SCH_STATUS_BILLED = "BILLED";
    public static final String BILL_SCH_STATUS_SUPPRESSED = "SUPPRESSED";
    public static final String BILL_SCH_STATUS_CANCELLED = "CANCELLED";
    public static final String BILL_SCH_STATUS_STANDARD = "STANDARD";
    public static final String BILL_SCH_STATUS_PRO_RATE = "PRO_RATE";
    public static final String BILL_SCH_STATUS_STUB = "STUB";

    public static final String BILLING_SCHEDULE_PROCESSED = "Billing Schedule(s) Status Updated";
    public static final String BILLING_SCHEDULE_PROCESSED_DETAIL = "Billing Schedule(s) Status has been Updated Successfully";

    public static final String HIDE_REVIEW = "HIDEREVIEW";
    public static final String ROLE_BILLING_APPROVER = "ROLE_BILLING_APPROVER";
    public static final String ROLE_BILLING = "ROLE_BILLING";
    public static final String ROLE_SUPERVISOR = "ROLE_SUPERVISOR";
    public static final String ROLENAME_DEFAULT = "Default";
    public static final String ROLENAME_ADMIN = "Admin";
    public static final String ROLENAME_BILLING_SPECIALIST = "Billing Specialist";
    public static final String ROLENAME_BILLING_MANAGER = "Billing Manager";
    public static final String ROLENAME_READONLY = "Read Only";
    public static final String ROLENAME_APPROVER = "Approver";

    public static final String NO_CHANGE = "No change(s) has been detected";
    public static final String NO_CHANGE_HEADER = "Sorry! Cannot Update";

    public static final String BILL_QUANTITY = "billQuantity";
    public static final String BILL_UNIT_PRICE = "billUnitPrice";
    public static final String BILLING_REVIEW_LINE = "billingReviewLine";
    public static final String ADD_NOTES_BILLING = "AddNotesBilling";
    public static final String CLOSE_NOTES_BILLING = "closeNotesBilling";
    public static final String DESCRIPTION = "description";
    public static final String CONCAT_CHAR = "#-#";
    public static final String AMT_OVERRIDE = "amtOverride";

    public static final String PRODUCT_STATUS = "PRODUCT_STATUS";
    public static final String ATTRIBUTE2_LOV = "ATTRIBUTE2_LOV";
    public static final String ORDERPRODUCTDFF1 = "ORDERPRODUCTDFF1";

    public static final String BILLING_REVIEW_PERMISSION = "billingreview";
    public static final String BILLING_SPECIALIST = "s";
    public static final String BILLING_APPROVER = "a";
    public static final String BILLING_MANAGER = "m";

    public static final String FROM_GT_TO = "From Number is greater than To Number";
    public static final String INIT_FROM_ONE = "Initial tier should always start from 1";
    public static final String DT_RANGE_INVALID = "Invalid Date Range";
    public static final String ST_DT_GT_END_DT = "Start Date is beyond the End Date";
    public static final String END_DT_SAME_PRV_END_DT = "Invalid Date Range";
    public static final String FROM_NUM_LT_PRV_TO_NUM = "Invalid Tier Range";
    public static final String FROM_NUM_INCR_BY_1 = "From Number should be increment by 1 of previous tier's To Number of this Date Range";
    public static final String CANT_ADD_TIER = "Cannot add further tier";
    public static final String END_TO_NUM_EMPTY = "End tier's to num should be empty";
    public static final String LINE_BILL_SCH_REFRESH_STATUS = "REFRESH";
    public static final String BILLING_REVIEW = "BILLING_REVIEW";
    public static final String INTERNAL_CONTACT = "INTERNAL_CONTACT";

    public static final String LINE_TYPE_FIXED_FEE = "FIXED_FEE";
    public static final String LINE_TYPE_H_COMMIT_USAGE = "H_COMMIT_USAGE";
    public static final String LINE_TYPE_USAGE = "USAGE";
    public static final String LINE_TYPE_COMMIT = "P_COMMIT";
    public static final String LINE_TYPE_BUDGET = "T_COMMIT_USAGE";
    public static final String LINE_TYPE_MINIMUME = "MINIMUM";
    public static final String STATUS_QA_CHECK = "QA_CHECK";

    public static final String REGION_TYPE = "REGION_TYPE";
    public static final String ENTITLEMENT_COV_FEATURE_TYPE = "ENTITLEMENT_COV_FEATURE_TYPE";
    public static final String ENTITLEMENT_RESP_SEVERITY_TYPE = "ENTITLEMENT_RESP_SEVERITY_TYPE";
    public static final String ETL_LOG_SERVER_PATH = "servers/";
    public static final String ETL_LOG_LOG_PATH = "/logs/ADORB_Import_logs/";
    public static final String ETL_RESULT_FILE_PATH = "/u01/data/backup/adorb/import_results/";
    //"C:/ADORB_SVN/trunk/IntegrationProcess/Customers/Common/AdeaIntegrator/data-out/";//

    public static final String ADJ_STATUS = "ADJ_STATUS";
    public static final String ADJ_NEW_STATUS = "NEW";
    public static final String ADJ_SUBMITTED_STATUS = "SUBMITTED";
    public static final String ADJ_TYPE = "ADJ_TYPE";
    public static final String CREDIT_METHOD = "CREDIT_METHOD";
    public static final String ADJ_TYPE_CM = "CM";
    public static final String ADJ_TYPE_DM = "DM";
    public static final String ADJ_TYPE_CMDM = "CMDM";
    public static final String CREDIT_TYPE = "CREDIT_TYPE";
    public static final String CREDIT_MEMO_AGAINST_INVOICE_TYPE = "CREDIT_MEMO_AGAINST_INVOICE";
    public static final String ON_ACCOUNT_CREDIT_TYPE = "ON_ACCOUNT_CREDIT_MEMO";

    public static final String ORDERDFFV1 = "ORDERDFF1";

    public static final String REF_NUMBER_PARAM = ":pi_reference_number";
    public static final String DEAL_NUMBER_PARAM = ":pi_deal_number";
    public static final String PO_NUMBER_PARAM = ":pi_po_number";
    public static final String BILLING_BATCH_PARAM = ":pi_billing_batch";
    public static final String SELL_TO_CUST_ID_PARAM = ":pi_sell_to_customer_id";
    public static final String BILLING_CHAN_PARAM = ":pi_billing_channel";
    public static final String BILLING_FREQUENCY_PARAM = ":pi_billing_frequency";
    public static final String BILLING_ANALYST_PARAM = ":pi_billing_analyst_id";
    public static final String SUPPORT_TIER_PARAM = ":pi_cust_support_tier";
    public static final String ITEM_ID_PARAM = ":pi_item_id";
    public static final String PRODUCT_CATEGORY_PARAM = ":pi_product_category";
    public static final String TERRITORY_PARAM = ":pi_territory";
    public static final String LINE_TYPE_PARAM = ":pi_line_type";
    public static final String BILL_THROUGH_DATE_PARAM = ":pi_bill_through_date";
    public static final String CUSTOMER_ID_PARAM = ":pi_customer_id";
    public static final String ORG_ID_PARAM = ":pi_org_id";
    public static final String DATE_PARAM = ":pi_as_of_day";
    public static final String TENANT_ID_PARAM = ":pi_tenant_id";
    public static final String BILL_RUN_ID_PARAM = ":pi_bill_run_id";
    public static final String BILL_RUN_NUMBER_PARAM = ":pi_bill_run_number";
    public static final String BU_PARAM = ":pi_business_unit";
    public static final String ORDER_NUMBER_PARAM = ":pi_order_number";
    public static final String BTC_PARAM = ":pi_bill_to_customer";
    public static final String BILLING_PERIOD_FROM_PARAM = ":pi_billing_period_from";
    public static final String BILLING_PERIOD_TO_PARAM = ":pi_billing_period_to";
    public static final String CUST_ACCT_NUMBER_PARAM = ":pi_cust_acct_number";
    public static final String SITE_NUMBER_PARAM = ":pi_site_number";
    public static final String ORDER_TYPE_PARAM = ":pi_order_type";
    public static final String TRANSACTION_START_DT_PARAM = ":pi_transaction_start_date";
    public static final String TRANSACTION_END_DT_PARAM = ":pi_transaction_end_date";
    public static final String FUSION_INVOICE_NUMBER_PARAM = ":pi_fusion_inv_num";
    public static final String FUSION_INVOICE_DATE_PARAM = ":pi_fin_trx_date";
    public static final String COMPANY_IDENTIFIER_PARAM = ":pi_company_identifier";
    public static final String BACKLOG_DATE = "BACKLOG_DATE";
    public static final String CUSTOMER_NAME = "CUSTOMER_NAME";
    public static final String BUSINESS_UNIT_NAME = "BUSINESS_UNIT";
    public static final String ORDER_NUMBER = "ORDER_NUMBER";
    public static final String RPT_ORDER_BACKLOG_V_NAME = "RPT_ORDER_BACKLOG_V";
    public static final String REPORT_NAME = "REPORT_NAME";
    public static final String ORDER_CATEGORY_BKLOG = "ORDER_CATEGORY";
    public static final String ORDER_CATEGORY_PARAM = ":pi_order_category";
    public static final String BKLOG_ORD_CATEGORY_RPT_LOV = "BKLOG_ORD_CATEGORY_RPT_LOV";
    public static final String SAMPLE_TEMPLATE_FILE_PATH = "/IntegrationProcess/Customers/Common/AdeaIntegrator/templates/";
    public static final String BILL_PERIOD = "BILL_PERIOD";
    public static final String BILL_PERIOD_PARAM = ":period_name";

    public static final String MANUAL_BILL_DATE = "MANUAL";
    public static final String MANUAL_BILLING_FREQUENCY = "MANUAL";
    public static final String ONE_TIME_LINE_TYPE = "FIXED_FEE";
    public static final String RECURRING_LINE_TYPE = "P_COMMIT";
    public static final String PRE_PAYMENT_LINE_TYPE = "PRE_PAYMENT";
    public static final String MANUAL = "Manual";
    public static final String MANUAL_BILL_DATE_MEANING = "Manual Bill Date";
    public static final String STATUS_LABEL = "status";
    public static final String SQLLDR_COMMAND = "sqlldr";

    public static final String ACTIVE = "A";
    public static final String INACTIVE = "I";

    public static final String ORDER_NUMBER_VALID = "Order Number is invalid";
    public static final String ORDER_NUMBER_VALID_DETAIL = "Please enter the valid Order Number";
    public static final String PP_ORDER_NUMBER_VALID = "Pre Payment Order Number is invalid";
    public static final String PP_ORDER_NUMBER_VALID_DETAIL = "Please enter the valid Pre payment Order Number";
    public static final String ORDER_BILL_TO_CUSTOMER_VALID = "Order Bill-To Customer is invalid";
    public static final String ORDER_BILL_TO_CUSTOMER_VALID_DETAIL = "Please select the valid Bill-To Customer in Order Tab";
    public static final String BILL_TO_CUSTOMER_VALID = "Bill-To Customer is invalid";
    public static final String BILL_TO_CUSTOMER_VALID_DETAIL = "Please select the valid Bill-To Customer";
    public static final String CUSTOMER_VALID = "Customer is invalid";
    public static final String CUSTOMER_VALID_DETAIL = "Please select the valid Customer";
    public static final String FROMTO_VALID = "From and To dates both required";
    public static final String FROMTO_VALID_DETAIL = "Please enter botht the Dates";
    public static final String SELL_TO_CUSTOMER_VALID = "Sell-To Customer is invalid";
    public static final String SELL_TO_CUSTOMER_VALID_DETAIL = "Please select the valid Sell-To Customer";
    public static final String LINE_BILL_TO_CUSTOMER_VALID = "Line Bill-To Customer is invalid";
    public static final String LINE_BILL_TO_CUSTOMER_VALID_DETAIL = "Please select the valid Bill-To Customer in Lines Tab";
    public static final String LINE_START_DATE = "LINE_START_DATE";
    public static final String START_DATE = "Start Date";
    public static final String END_DATE = "End Date";
    public static final String AND = " and ";
    public static final String LINE_START_DATE_PHRASE = " is not a complete period for the Billing Frequency : ";
    public static final String ORDER_NUMBER_MAP = "orderNumber";
    public static final String ORDER_STATUS_MAP = "orderStatus";
    public static final String SERVICE_EXP_DATE = "serviceExpDate";
    public static final String LINE_NUMBER_MAP = "lineNumber";
    public static final String ORDER_NUMBER_FROM_MAP = "orderNumberFrom";
    public static final String ORDER_NUMBER_TO_MAP = "orderNumberTo";
    public static final String PP_ORDER_NUMBER_MAP = "ppOrderNumber";
    public static final String BILL_TO_CUSTOMER_MAP = "billToCustomer";
    public static final String LINE_PRODUCT_LIST_MAP = "lineProductList";
    public static final String LINE_PRODUCT_ID = "productId";
    public static final String LINE_PRODUCT_ITEM_NAME = "productItemName";
    public static final String LINE_PRODUCT_DESCRIPTION = "productDescription";
    public static final String BILL_TO_CUSTOMER_CP = "billToCustomerCp";
    public static final String SELL_TO_CUSTOMER_CP = "sellToCustomerCp";
    public static final String BILL_TO_CUSTOMER_MAP_ADJ = "billToCustomerAdj";
    public static final String LINE_BILL_TO_CUSTOMER_MAP = "lineBillToCustomer";
    public static final String SELL_TO_CUSTOMER_MAP = "sellToCustomer";
    public static final String SEARCH_MODE_MAP = "searchMode";
    public static final String ORDER_ID_MAP = "orderId";
    public static final String CUSTOMER_ID_MAP = "customerId";
    public static final String ORDER_TYPE_MAP = "orderType";
    public static final String STATUS_MAP = "status";
    public static final String ACCOUNT_NUMBER_MAP = "accountNumber";
    public static final String ACCOUNT_NAME_MAP = "accountName";
    public static final String ACCOUNT_ID_MAP = "accountId";
    public static final String ACCT_SITE_ID_MAP = "acctSiteId";
    public static final String CONTACT_ID_MAP = "contactId";
    public static final String LOCATION_MAP = "location";
    public static final String PARTNER_NAME_MAP = "partnerName";
    public static final String PURPOSE_MAP = "purpose";
    public static final String BILLED_DELIVERY_REPORT_ERR_MSG = "Period Month or Bill Period Start Date/Bill Period End Date is blank";
    public static final String BUSINESS_UNIT_NULL = "Please select the Business Unit";
    public static final String INITIAL_VECTOR = "********";
    public static final String UTC_TIMEZONE = "UTC";
    public static final String PRODUCT_CATALOG_ID = "PRODUCT_CATALOG_ID";
    public static final String TYPE_DATA = "typeData";
    public static final String PRICING_MANUAL_ADJUSTMENT_TYPE = "PRICING_MANUAL_ADJUSTMENT_TYPE";
    public static final String PRICING_ADJ_NAME = "PRICING_ADJ_NAME";
    public static final String PRICE_LIST_LINE_TYPES = "PRICE_LIST_LINE_TYPES";
    public static final String PRICE_LIST_TYPES = "PRICE_LIST_TYPES";
    public static final String TRANSACTION_FLAG_TYPES = "TRANSACTION_FLAG_TYPES";
    public static final String PRICE_LIST_STATUS = "PRICE_LIST_STATUS";
    public static final String ADJUSTMENT_DELIVERY_OVERRIDE_TYPE = "Override";
    public static final String ADJUSTMENT_DELIVERY_INCREMENTAL_TYPE = "Incremental";
    public static final String PRICING_ADJUSTMENT_TYPES = "PRICING_ADJUSTMENT_TYPES";
    public static final String PRICING_INDEX_CATEGORIES = "PRICING_INDEX_CATEGORIES";
    public static final String MANDATORY_FIELD = "MANDATORY_FIELD";
    public static final String LIST_CODES = "LIST_CODES";
    public static final String DELIVERY_TEMPLATE_COLS = "DELIVERY_TEMPLATE_COLS";
    public static final String PRICING_ADJ_CYCLE = "PRICING_ADJ_CYCLE";
    public static final String PRICING_ADJ_FREQUENCY = "PRICING_ADJ_FREQUENCY";
    public static final String ORDER_DFF_ATTR1 = "ORDER_DFF_ATTR1";
    public static final String ORDER_DFF_ATTR2 = "ORDER_DFF_ATTR2";
    public static final String ORDER_DFF_ATTR3 = "ORDER_DFF_ATTR3";
    public static final String ORDER_DFF_ATTR4 = "ORDER_DFF_ATTR4";
    public static final String ORDER_DFF_ATTR5 = "ORDER_DFF_ATTR5";
    public static final String LINE_DFF_ATTR1 = "LINE_DFF_ATTR1";
    public static final String LINE_DFF_ATTR2 = "LINE_DFF_ATTR2";
    public static final String LINE_DFF_ATTR3 = "LINE_DFF_ATTR3";
    public static final String LINE_DFF_ATTR4 = "LINE_DFF_ATTR4";
    public static final String LINE_DFF_ATTR5 = "LINE_DFF_ATTR5";
    public static final String NONE = "NONE";
    public static final String ANY = "ANY";
    public static final String YESNO = "YES_NO";
    public static final String SFTP_FILE_DOWNLOAD_LOCATION = "/u01/data/backup/adorb/sqlloader/";
    public static final String ACCOUNTING_RULES = "ACCOUNTING_RULES";
    public static final String ACCOUNTING_RULES_ORIG = "Original Line";
    public static final String INVOICING_RULE_SEND = "INVOICING_RULE_SEND";
    public static final String RULE_START_DATE = "RULE_START_DATE";
    public static final String RULE_END_DATE = "RULE_END_DATE";
    public static final String GL_DATE = "GL_DATE";
    public static final String INVOICE_TRANSACTION_DATE = "INVOICE_TRANSACTION_DATE";
    public static final String DEFAULT_INVOICE_STATUS = "POSTED";
    public static final String WORKLIST_ACCESSIBLE = "WorklistApplication";

    public static final String INVOICE_AR_PERIOD = "INVOICE_AR_PERIOD";
    public static final String ACCOUNTING_RULE_TYPE = "ACCOUNTING_RULE_TYPE";
    public static final String SOURCE_TYPES = "SOURCE_TYPES";
    public static final String IMP_TYPE_LKP = "IMPORT_TYPE";
    public static final String IFACE_STATUS_LKP = "IFACE_STATUS";
    public static final String SUPPORT_TIER = "SUPPORT_TIER";
    public static final String BILL_RUN_ID = "BILL_RUN_ID";
    public static final String BILL_RUN_NUMBER = "BILL_RUN_NUMBER";
    public static final String LONG = "Long";
    public static final String OPERATOR_EQUAL = "=";
    public static final String OPERATOR_QUESTION_MARK = "?";

    public static final String BILL_THROUGH_DATE_REQUIRED = "billThroughDateRequired";
    public static final String ORGID_REQUIRED = "orgIdRequired";
    public static final String IMP_TYPE_REQUIRED = "impTypeRequired";
    public static final String IFAC_STATUS_REQUIRED = "ifaceStatusRequired";
    public static final String DAYS_FORWARD_REQUIRED = "daysForwardRequired";
    public static final String START_DATE_REQUIRED = "startDateRequired";
    public static final String END_DATE_REQUIRED = "endDateRequired";
    public static final String THRESHOLD_REQUIRED = "thresholdRequired";
    public static final String BILLDATE_REQUIRED = "billDateRequired";
    public static final String FILETYPE_REQUIRED = "fileTypeRequired";
    public static final String FILE_TYPES = "FILE_TYPES";

    public static final String Bill_Run_Id = "billRunId";
    public static final String DRAFT = "Draft";
    public static final String ERROR = "Error";
    public static final String CURRENT = "current";
    public static final String DISABLED = "disabled";
    public static final String START = "start";
    public static final String RECENT = "recent";
    public static final String COMPLETED = "completed";
    public static final String EMPTY = "";
    public static final String IN_PROCESS = "In Process";
    public static final String PROCESS_ERROR = "Process Error";
    public static final String IN_REVIEW = "In Review";
    public static final String BILLING = "Billing";
    public static final String BILLED = "Billed";
    public static final String CANCELLED = "Cancelled";
    public static final String BILLING_ERROR = "Billing Error";
    public static final String INTERFACED = "Interfaced";
    public static final String BILLED_WARNING = "Billed Warning";
    public static final String BILLED_PARTIAL = "Billed Partial";
    public static final String BILLING_EXECPTION = "Billing Exception";
    public static final String LOWER_BILL_RUN_NUMBER = "lower(BILL_RUN_NUMBER)";
    public static final String BILL_RUN_MAIN_URL = "billRunMain.xhtml";
    public static final String BILL_RUN_URL = "billRun.xhtml";
    public static final String DEF = "def";
    public static final String ORG_ID = "orgId";
    public static final String STRING = "String";
    public static final String BILL_RUN_ALREADY_EXISTS = "Bill Run already exists, try different Bill Run";
    public static final String BILL_RUN_ALREADY_INPORCESS = "Another Bill Run or Billing Schedule is in process, try after sometime";
    public static final String PARAM_REQUEST_ID = "prequestid";
    public static final String PARAM_TENANT_ID = "ptenantid";
    public static final String PARAM_USER_ID = "puserid";
    public static final String PARAM_ORG_ID = "porgid";
    public static final String PARAM_ORDER_ID = "porderid";
    public static final String PARAM_BILL_RUN_ID = "pbillrunid";
    public static final String PARAM_BILL_THROUGH_DATE = "pbillthroughdate";
    public static final String PARAM_TAX_TRANSACTION_DATE = "ptaxtransactiondate";
    public static final String PARAM_BILL_ANALYST_ID = "pbillingAnalystId";
    public static final String PARAM_CUSTOMER_ID = "pcustomerid";
    public static final String PARAM_SUPPORT_TIER = "psupporttier";
    public static final String PARAM_TERRITORY = "pterritory";
    public static final String PARAM_ORDER_TYPE = "pordertype";
    public static final String PARAM_ORDER_CATEGORY = "pordercategory";
    public static final String PARAM_ITEM_ID = "pitemId";
    public static final String PARAM_LINE_TYPE = "plinetype";
    public static final String PARAM_PROD_CATEGORGY = "pproductCategory";
    public static final String PARAM_REF_NUM = "preferencenumber";
    public static final String PARAM_DEAL_NUM = "pdealnumber";
    public static final String PARAM_PO_NUM = "pponumber";
    public static final String PARAM_BILL_BATCH = "pbillingbatch";
    public static final String PARAM_SELL_TO_CUST = "pselltocustomer";
    public static final String PARAM_BILL_CHANNEL = "pbillingchannel";
    public static final String PARAM_BILL_FREQ = "pbillingfrequency";
    public static final String PARAM_IMPORT_LOAD_MODE = "pimportLoadMode";
    public static final String PARAM_IMP_TYPE = "pimpType";
    public static final String PARAM_BATCH_NAME = "pbatchName";
    public static final String PARAM_ORD_SRC = "pordersource";
    public static final String PARAM_IFACE_STATUS = "pifaceStatus";
    public static final String PARAM_INVOICE_DATE = "pinvoicedate";
    public static final String PARAM_BILL_SCH_LINE_TYPE = "pbillschlinetype";
    public static final String PARAM_FILE_TYPE= "pfileType";
    public static final String PARAM_STORAGE= "pstorage";
    public static final String PI_SOURCE = "pi_source";
    public static final String PI_BATCH_NAME = "pi_batch_name";
    public static final String PI_PROCESS_NEW = "pi_process_new";
    public static final String PI_LOG_LEVEL = "pi_log_level";
    public static final String PI_CUST_SOURCE = "pi_cust_source";
    public static final String PI_BILLING_LINES_PROCESS_FLAG = "pi_billing_lines_process_flag;	";
    public static final String ARG_SOURCE = "source";
    public static final String BATCH = "batch";
    public static final String LOG_LEVEL_1 = "1";
    public static final String PROGRAM_NAME = "programname";
    public static final String PROCESS_USAGE_ADJ = "PROCESS_USAGE_ADJ";
    public static final String CALL_PROCESS_USAGE_ADJ = "{call PROCESS_ADJUSTMENTS.process_adjustments(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    public static final String PARAM_CPI_TRUE_UP_LINES = "pocpitrueup";

    public static final String EXCEPTION_MESSAGE = "Unexpected application error occurred. Please contact the support group";

    //Number Generator Constant Values
    public static final String NUM_GEN_TYPE = "NUM_GEN_TYPE";
    public static final String NUM_GEN_FIELD = "NUM_GEN_FIELD";
    public static final String AUTO_NUM_GEN_TYPE = "AUTO";
    public static final String GLOBAL_NUM_GEN_TYPE = "GLOBAL";
    public static final String MANUAL_NUM_GEN_TYPE = "MANUAL";
    public static final String BU_SETUP_SOURCE = "BUSINESS_UNIT";
    public static final String ORDER_TYPE_SETUP_SOURCE = "ORDER_TYPE";

    //Data Types
    public static final String LONG_TYPE = "Long";
    public static final String DOUBLE_TYPE = "Double";
    public static final String BIGDECIMAL_TYPE = "BigDecimal";
    public static final String DATE_TYPE = "Date";
    public static final String DATE_FORMAT_FOR_ORDER_SEARCH = "dd-MMM-yy";

    //Customer DFF JSF id's
    public static final String BILL_CUST_CLASS = "billCustClass";
    public static final String SELL_CUST_CLASS = "sellCustClass";
    public static final String LINE_BILL_CUST_CLASS = "lineBillCustClass";
    public static final String LINE_SELL_CUST_CLASS = "lineSellCustClass";
    public static final String ORDERS_MAIN_JVNM = "Orders(Main):";
    public static final String PRODUCTS_JVNM = "Products:";
    public static final String BILL_ATTRIBUTE = "billAttribute";
    public static final String SELL_ATTRIBUTE = "sellAttribute";
    public static final String LINE_BILL_ATTRIBUTE = "lineBillAttribute";
    public static final String LINE_SELL_ATTRIBUTE = "lineSellAttribute";
    public static final String CREATE_ORDER_FORM_ID = "mainForm:createOrder:";
    public static final String VIEW_LINE_FORM_ID = "tbv:updateLines:viewLineAccord:";
    public static final String VIEW_ORDER_FORM_ID = "tbv:viwOrdFrm:viewOrder:";

    public static final String PROCESS_USAGE_ADJ_DESC = "Process Usage Adjustments";
    public static final String PHASE_CODE_RUNNING = "R";
    public static final String STATUS_CODE_RUNNING = "R";
    public static final String STATUS_CODE_WARNING = "W";
    public static final String STATUS_CODE_ERROR = "E";
    public static final String STATUS_CODE_FAILED = "F";
    public static final String STATUS_DESC_RUNNING = "Running";
    public static final String STATUS_CODE_COMPLETED = "C";
    public static final String STATUS_DESC_COMPLETED = "Completed";
    public static final String STATUS_DESC_ERROR = "Error";
    public static final String PHASE_CODE_COMPLETED = "C";
    public static final String INVOICE = "invoice";
    public static final String INTERFACE = "interface";
    public static final String UPPER_CHAR_Y = "Y";
    public static final String UPPER_CHAR_N = "N";
    public static final String EVERGREEN_CASE_1_AND_3 = "Order is marked as Evergreen but no products have been selected.";
    public static final String EVERGREEN_CASE_2 = "Product line cannot be saved as evergreen until the order header is marked as evergreen.";
    public static final String EVERGREEN_CASE_4 = "One of the products is selected as Evergreen, but the Order is not selected.";
    public static final String EVERGREEN_CASE_5 = "Evergreen One-Time should be in Advance.";
    public static final String BILL_FREQ_ONE_TIME = "ONE_TIME";
    public static final String ZERO = "0";
    public static final String ZERO_TWO_DECIMAL = "0.00";
    public static final String LANGUAGE_EN = "en";
    public static final String COUNTRY_US = "US";
    public static final String NUMBER_PATTERN = "###,###.00";

    public static final String LOCALHOST = "localhost";
    public static final String SYNC_UPDATE_SUCCESS = "Sync Preferences has been Updated Successfully";

    public static final String DASH_WITH_SPACES = " - ";
    public static final String BILL_RUN_SUCCESS_MSG = "Bill Run saved successfully";
    public static final String TENANT_IDENTIFIER = "tenantIdentifier";
    public static final String ADORB_CONCURRENCY = "adorbConcurrency";
    public static final String AR_AUTO_INVOICE_BILL_RUN = "AR_AUTO_INVOICE_BILL_RUN";
    public static final String DASH = "-";
    public static final String BILL = "bill";
    public static final String SELL = "sell";
    public static final String INTERFACE_LINES = "interfaceLines";
    public static final String SALES_CREDITS = "salescredits";
    public static final String PROGRAM = "program";
    public static final String FUSION_USER_NAME = "FusionUserName";
    public static final String FUSION_PASSWORD = "FusionPassword";
    public static final String FUSION_URL = "FusionURL";
    public static final String FUSION_VERSION = "fusionVersion";
    public static final String SYNC_PROG = "syncProg";
    public static final String UPPER_CHAR_S = "S";
    public static final String SALES_CREDITS_FLAG = "SalesCreditsFlag";
    public static final String TRUE = "true";
    public static final String FALSE = "false";
    public static final String PROGRAM_ID = "programId";
    public static final String HTTP_PREFIX = "http://";
    public static final String HTTP_POSTFIX = "://";
    public static final String SERVER_PORT = "serverPort";
    public static final String OPERATOR_COLON = ":";
    public static final String DEFAULT_DATE = "DefaultDate";
    public static final String BUSINESS_UNIT_LOWER = "BusinessUnit";
    public static final String AR_AYTO_INVOICE = "arAutoInvoice";
    public static final String SYNC_ERROR_REST_URL = "syncErrorRestURL";
    public static final String P_AR_INT_URL = "pArIntURL";
    public static final String AUTO_INVOICE_ARGUMENT3 = "autoInvoiceArgument3";
    public static final String AUTO_INVOICE_TRX_SOURCE = "autoInvoiceTrxSource";
    public static final String USER_ID = "userId";
    public static final String FIN_TRX_BACK_FEED = "FIN_TRX_BACK_FEED";
    public static final String FIN_TRX_BACK_FEED_LOWER = "finTrxBackFeed";
    public static final String EEE_MMM_DD_HH_MM_SS_Z_YYYY = "EEE MMM dd HH:mm:ss Z yyyy";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String P_END_DATE = "pEndDate";
    public static final String P_START_DATE = "pStartDate";
    public static final String BILL_RUN_GENERATE_BILLING_LINES = "BILL_RUN_GENERATE_BILLING_LINES";
    public static final String AR_INVOICE_IMPORT_BILL_RUN = "AR_INVOICE_IMPORT_BILL_RUN";
    public static final String AR_INTERFACE = "AR_INTERFACE";
    public static final String AR_INTERFACE_LOWER = "arInterface";
    public static final String BILL_RUN_GENERATE_BILLING_LINES_LOWER = "billRunGenerateBillingLines";
    public static final String AR_INVOICE_IMPORT_BILL_RUN_LOWER = "arInvoiceImportBillRun";
    public static final String P_SYNC_ERROR_URL = "pSyncErrorURL";
    public static final String BILL_RUN_FORM_ID = "billRun";
    public static final String BILL_SCH_STATUS_SCHEDULED_LOWER = "Scheduled";
    public static final String ORDER_STATUS_ACTIVE = "ACTIVE";
    public static final String ORDER_STATUS_TERMINATED = "TERMINATED";
    public static final String STATUS_CANCELED = "CANCELED";
    public static final String REVIEW_INVOICE_DESCRIPTION = "Review Invoice";
    public static final String ORDER_ID_COLUMN_NAME = "ORDER_ID";
    public static final String AUTO_INVOICE_ORACLE_CLOUD_DESCRIPTION = "Auto Invoice - Oracle Cloud";
    public static final String GEN_INVOICE_ORACLE_CLOUD_DESCRIPTION = "Generate Invoice - Oracle Cloud";
    public static final String GEN_INVOICE_BILL_RUN_DESCRIPTION = "Generate Bill Run Billing Lines";
    public static final String ORACLE_CLOUD_ADORB_STATUS_UPDATE_DESCRIPTION = "Oracle Cloud - Adorb Status update";
    public static final String SCOPE_VIEW = "view";
    public static final String PROGRAM_BILL_RUN_GENERATE_BILLING_SCHEDULE = "BILL_RUN_GENERATE_BILLING_SCHEDULE";
    public static final String PROGRAM_PROCESS_CAPS_BS = "PROCESS_CAPS_BS";
    public static final String COMMAND_SHOW_NO_BILL_DIALOG = "PF('noBillDialog').show();";
    public static final String COMMAND_SHOW_NEW_USAGES_DIALOG = "PF('billConfDialog').show();";
    public static final String COMMAND_SHOW_NEW_USAGES = "billErrorDialog();";
    public static final String COMMAND_REFRESH = "refresh();";
    public static final String COMMAND_AFTER_REMOVE = "afterRemove();";
    public static final String COMMAND_DELETE_ORDER_CONFIRM_DIALOG = "PF('delorderConfirmDialog').show();";
    public static final String COMMAND_PROCESS_BILL = "processBill();";
    public static final Long MS_120K_LONG = 120000l;
    public static final Long MS_30K_LONG = 30000l;
    public static final String EXTEND_EVERGREEN_END_DATE_DESC = "Extend Evergreen End Date";
    public static final String EXTEND_EVERGREEN_END_DATE = "EXTEND_EVERGREEN_END_DATE";
    public static final String BUSINESS_UNIT_LABEL = "Business Unit";
    public static final String ORDER_NUMBER_LABEL = "Order Number";
    public static final String ORDER_NUMBER_LOWER = "orderNumber";
    public static final String MANDATORY_FIELD_STYLECLASS = "mandatoryField";
    public static final String NO_STYLE = "noStyle";
    public static final String INPUT_ORDER_LOV = "inputOrderLov";
    public static final String SELECT = "select";

    public static final String LOAD_INDEXES = "LOAD_INDEXES";
    public static final String IMPORT_INDEXES = "IMPORT_INDEXES";

    public static final String BLANK = "";
    public static final String DOT = ".";
    public static final String SPACE = " ";
    public static final String UNDERSCORE = "_";
    public static final String SEMICOLON = ":";
    public static final String PERCENTAGE = "%";
    public static final String COMMA = ",";
    public static final Long DEF_LONG = new Long(-1);
    public static final String NULL = "null";
    public static final String YES = "Y";
    public static final String NO = "N";
    public static final String CSV_CONTENT_TYPE = "application/csv";
    public static final String ZIP_CONTENT_TYPE = "application/zip";
    public static final String TXT_CONTENT_TYPE = "application/txt";
    public static final String PDF_CONTENT_TYPE = "application/pdf";
    public static final String XLS_CONTENT_TYPE = "application/vnd.ms-excel";
    public static final String PDF_REPORT_TYPE = "pdf";
    public static final String XLS_REPORT_TYPE = "xls";
    public static final String CSV_REPORT_TYPE = "csv";
    public static final String CSV_EXTENSION = ".csv";
    public static final String ZIP_EXTENSION = ".zip";
    public static final String LOG_EXTENSION = ".log";
    public static final String AMENDMENT_PREFIX = "Amendment_";
    public static final String CUSTOMER_TYPE = "CUSTOMER_TYPE";
    public static final String BUSINESS_CLASS = "BUSINESS_CLASS";
    public static final String SERVICE_TYPE = "SERVICE_TYPE";
    public static final String JURISDICTION_TYPE = "JURISDICTION_TYPE";
    public static final String SALE_RESALE = "SALE_RESALE";
    public static final String TAX = "Tax";
    public static final String AR_INVOICE_DATE = "AR Invoice Date";
    public static final String AR_ACCOUNTING_DATE = "AR Accounting Date";
    public static final String ACCOUNTING_DATE_INVDT = "INVOICE DATE";
    public static final String INVOICE_DATE_MANUAL = "MANUAL";
    public static final String INVOICE_DATE_SYSDATE = "SYSDATE";
    public static final String GEN_BILL_RUN_BILLING_SCHEDULES_DESC = "Generate Bill Run Billing Schedules";
    public static final String GEN_BILL_RUN_BILLING_SCHEDULES = "GEN_BILL_RUN_BILLING_SCHEDULES";
    public static final String COMMAND_PLEASE_WAIT_SHOW = "PF('pleaseWt').show();";
    public static final String COMMAND_PLEASE_WAIT_HIDE = "PF('pleaseWt').hide();";
    public static final String GENERATE_TAX_LINES = "GENERATE_TAX_LINES";
    public static final String GENERATE_TAX_LINES_DESC = "Generate Tax Lines";
    public static final String AVALARA_TAX_INTEGRATOR = "AVALARA";
    public static final String MANUAL_SOURCE_TYPE = "MANUAL";
    public static final String TAX_DFF_ATTR1 = "TAX_DFF_ATTR1";
    public static final String TAX_DFF_ATTR2 = "TAX_DFF_ATTR2";
    public static final String TAX_DFF_ATTR3 = "TAX_DFF_ATTR3";
    public static final String TAX_DFF_ATTR4 = "TAX_DFF_ATTR4";
    public static final String TAX_DFF_ATTR5 = "TAX_DFF_ATTR5";
    public static final String TAX_GROUPS = "TAX_GROUPS";
    public static final String TAX_FIELD = "TAXAMOUNT";
    public static final String SYMBOL_BACKSLASH = "/";
    public static final String OPERATOR_GT = ">";
    public static final String OPERATOR_LT = "<";
    public static final String JOB_STATUS_BLOCKED = "BLOCKED";
    public static final String JOB_STATUS_EXPIRED = "EXPIRED";
    public static final String JOB_STATUS_SCHEDULE_ENDED = "SCHEDULE_ENDED";
    public static final String JOB_STATUS_CANCELLED = "CANCELLED";
    public static final String JOB_STATUS_FINISHED = "FINISHED";
    public static final String JOB_STATUS_SUCCEEDED = "SUCCEEDED";
    public static final String JOB_STATUS_PAUSED = "PAUSED";
    public static final String JOB_STATUS_VALIDATION_FAILED = "VALIDATION_FAILED";
    public static final String JOB_STATUS_ERROR = "ERROR";
    public static final String JOB_STATUS_PENDING_VALIDATION = "PENDING_VALIDATION";
    public static final String JOB_STATUS_WAIT = "WAIT";
    public static final String JOB_STATUS_CANCELLING = "CANCELLING";
    public static final String JOB_STATUS_HOLD = "HOLD";
    public static final String JOB_STATUS_UNKNOWN = "UNKNOWN";
    public static final String JOB_STATUS_COMPLETED = "COMPLETED";
    public static final String JOB_STATUS_ERROR_AUTO_RETRY = "ERROR AUTO RETRY";
    public static final String JOB_STATUS_READY = "READY";
    public static final String JOB_STATUS_WARNING = "WARNING";
    public static final String JOB_STATUS_ERROR_MANUAL_RECOVERY = "ERROR MANUAL RECOVERY";
    public static final String JOB_STATUS_RUNNING = "RUNNING";
    public static final Long BILL_RUN_TIMEOUT_MILLISECS = 30 * 60l * 1000;
    public static final int SLEEP_30_SECS = 30 * 1000;
    public static final String BILL_RUN_STOP = "STOP";
    public static final String BILL_RUN_CONTINUE = "CONTINUE";
    public static final String PROCESS_CAPS = "PROCESS_CAPS";
    public static final String PROCESS_CAPS_DESC = "Process Caps";
    public static final String GENERATE_BILLING_SCHEDULE = "GENERATE_BILLING_SCHEDULE";
    public static final String GENERATE_BILLING_SCHEDULE_DESC = "Generate Billing Schedule from Order";
    public static final String BILL_RUN_GROUP_BILL = "BILL";
    public static final String BILL_RUN_GROUP_PRE_BILL = "PRE_BILL";
    public static final String BILL_RUN_GROUP_TAX = "REPROCESS_TAX";
    public static final String BILL_RUN_ADD_ORDER = "PROCESS_ORDER";
    public static final String PRE_BILL_RUN_REVIEW_PROCESSOR = "PRE_BILL_RUN_REVIEW_PROCESSOR";
    public static final String PRE_BILL_RUN_REVIEW_PROCESSOR_DESC = "Pre Bill Run Review Processor";
    public static final String ACTIVATE_ORDER_AMENDMENT_REST = "Activate Order from Amendment REST";
    public static final String AMENDMENT_ORDER_ACTIVATE_SUCCESS = "Order activated successfully post amendment changes.";
    public static final String NO_JOB_RUNNING = "No Job Found.";
    public static final String ACTIVATE_ORDER_POST_CHANGES = "ACTIVATE_ORDER_POST_CHANGES";
    public static final String GEN_TAX_VAL_FOR_AR_INTERFACE = "GEN_TAX_VAL_FOR_AR_INTERFACE";
    public static final String GEN_TAX_VAL_FOR_AR_INTERFACE_DESC = "Generate Tax values for AR Interface";
    public static final String BILL_RUN_PRE_PAYMENT_PROCESS = "PRE_PAYMENT_PROCESS";
    public static final String BILL_RUN_PRE_PAYMENT_PROCESS_DESC = "Pre Payment Process";
    public static final int SLEEP_ONE_MIN = 60000;
    public static final String PARAM_BILL_DATE = "pbilldate";
    public static final String COMMAND_SHOW_INVOICE_DATE_DIALOG = "PF('invConfirmDialog').show();";
    public static final String COMMAND_SHOW_NO_TAX_DIALOG = "PF('noTaxDialog').show();";
    public static final String COMMAND_SHOW_NO_PRICE_DIALOG = "PF('noPriceDialog').show();";
    public static final String ORDER_TAX_STATUS = "ORDERTAXSTATUS";
    public static final String LINE_TAX_STATUS = "LINETAXSTATUS";
    public static final String FAILED = "FAILED";
    public static final String FAIL = "FAIL";
    public static final String BILL_RUN_EXCEPTIONS = "BILL_RUN_EXCEPTIONS";
    public static final String BILL_RUN_EXCEPTIONS_DESC = "Bill Run Exceptions";
    public static final String CM_DM_IN_BS = "Show CM/DM in Bill Schedule";
    public static final String ADJ_CM = "ADJ-CM";
    public static final String ADJ_DM = "ADJ-DM";
    public static final String ADJ_CMDM = "ADJ-CMDM";
    public static final String UPDATE_BILL_RUN_STATUS = "UPDATE_BILL_RUN_STATUS";
    public static final String UPDATE_BILL_RUN_STATUS_DESC = "Update Bill Run Status";
    public static final String APPLICATION_METHOD = "APPLICATION_METHOD";
    public static final String APPLICATION_METHOD_PROFILE_OPTION_NAME = "Allow Application Method Update";
    public static final String TENANT = "TENANT";
    public static final String PRC_CYCLE_SPECIFIC_DATE = "SPECIFIC_DATE";
    public static final String COMMAND_NO_PROFORMA_DIALOG = "PF('noProformaDialog').show();";
    public static final String AR_INTERFACE_EBS11i = "AR_INTERFACE_EBS11i";

    public static final String BILLIN_LINE_ERROR_STATUS = "ERROR";
    public static final String BILLIN_LINE_INTERFACED_STATUS = "INTERFACED";
    public static final String BILLIN_LINE_INT_ERROR_STATUS = "INT_ERROR";
    public static final String BILLIN_LINE_INVOICED_STATUS = "INVOICED";
    public static final String BILLIN_LINE_RT_INTERFACE_STATUS = "READY_TO_INTERFACE";
    public static final String BILLIN_LINE_PROCESSED_STATUS = "PROCESSED";
    public static final String PRC_CPI_INDEX = "CPI";
    public static final String PRC_RPI_INDEX = "RPI";
    public static final String COMMAND_PROCESS_SAVE = "processSave();";
    public static final String COMMAND_REPROCESS_SAVE = "reProcessSave();";
    public static final String COMMAND_PROCESS_ORDER = "processOrder();";
    public static final String PR_PRICING_METHOD_TYPE = "PR_PRICING_METHOD_TYPE";
    public static final String PR_RULE_TYPE = "PR_RULE_TYPE";
    public static final String PR_ADJUSTMENT_TYPE = "PR_ADJUSTMENT_TYPE";
    public static final String PR_PRICING_OPERATOR = "PR_PRICING_OPERATOR";
    public static final String PRICE_DELIVERIES = "PRICE_DELIVERIES";
    public static final String PRICE_DELIVERIES_DESCRIPTION = "Price Deliveries";
    public static final String ORD_IMP_PURGE_PROCESSED_ORD = "ORD_IMP_PURGE_PROCESSED_ORD";
    public static final String ORD_IMP_PURGE_PROCESSED_ORD_DESC = "Purge Import Staging Data";
    public static final String PR_LINK_TO_PRICING_ATTR = "PR_LINK_TO_PRICING_ATTR";
    public static final String PR_SOURCE_TYPES = "PR_SOURCE_TYPES";
    public static final String PR_SOURCE_FUNCTIONS = "PR_SOURCE_FUNCTIONS";
    public static final String PR_SOURCE_DATA_TYPES = "PR_SOURCE_DATA_TYPES";
    public static final String PR_PRECEDENCE = "PR_PRECEDENCE";
    public static final String PR_SOURCE_TABLE = "PR_SOURCE_TABLE";
    public static final String PRICE_DELIVERIES_SP = "PRICE_DELIVERIES_SP";
    public static final String CUSTOMER_SYNC_FILE_TYPE = "customersSync";
    public static final String CUSTOMERS_FILE_TYPE = "customers";
    public static final String PAYMNETS_SYNC_FILE_TYPE = "fusionPaymentsSync";
    public static final String FILE_TYPE = "fileType";
    public static final String P_CUST_ACCOUNT_ID = "pCustAccountId";
    public static final String IMPORT_CUSTOMER_ACCOUNTS = "Import Customer Accounts";
    public static final String MM_DD_YYYY = "MM-dd-yyyy";
    public static final String CUST_IMPORT_URL = "pCustImportUrl";
    public static final String URL = "url";
    public static final String TENANT_ID = "tenantId";
    public static final String P_JOB_ID = "jobId";
    public static final String BI_REPORT_DEFAULT_END_DATE_VALUE = "12-31-4712";
    public static final String BI_REPORT_DEFAULT_START_DATE_VALUE = "01-01-2015";
    public static final String GRAPH_STATUS_SUCCESS = "FINISHED_OK";
    public static final String GRAPH_STATUS_ERROR = "ERROR";
    public static final String CUST_SOURCE_ORACLE_FIN_CLOUD = "ORACLE_FIN_CLOUD";
    public static final String FUSION_PAYMENTS_SYNC = "FUSION_PAYMENTS_SYNC";
    public static final String FUSION_PAYMENTS_SYNC_DESC = "Sync Fusion Payments";
    public static final String FUSION_PAYMENT_URL = "fusionPaymentURL";
    public static final String REST_FUSION_PAYMENT_SYNC = "/api/v1/createPaymentDetails";
    public static final String QA_MESSAGE_TYPE = "QA_MESSAGE_TYPE";
    public static final String ADVANCED_ORDER_SEARCH_LABEL = "advancedOrderSearch";
    public static final String AUDIT_REPORT_PROGRAM = "AUDIT_REPORT";
    public static final String AUDIT_REPORT_PROGRAM_DESC = "Audit Report";
    public static final String PI_AUDIT_FROM_DATE = "pi_audit_from_date";
    public static final String PI_AUDIT_TO_DATE = "pi_audit_to_date";
    public static final String P_ORDER_ID = "pi_order_id";
    public static final String P_CUSTOMER_ID = "pi_bill_to_customer_id";
    public static final String P_CUSTOMER = "pi_bill_to_cust_num";
    public static final String P_BILL_TO_SITE_NUMBER = "pi_bill_to_site_num";
    public static final String P_SELL_TO_CUSTOMER = "pi_sell_to_customer_id";
    public static final String P_SELL_TO_CUSTOMER_NUMBER = "pi_sell_to_cust_num";
    public static final String P_SELL_TO_SITE_NUMBER = "pi_sell_to_site_num";
    public static final String PI_REQUEST_ID = "pi_request_id";
    public static final String PI_ORG_ID = "pi_org_id";
    public static final String ADVANCED_SEARCH_CUSTOMER_ID = "custAccountId";
    public static final String ADVANCED_SEARCH_CUSTOMER_NAME = "accountName";
    public static final String ADVANCED_SEARCH_CUSTOMER_LOCATION = "custAcctSiteId";
    public static final String ADVANCED_SEARCH_CUSTOMER_CONTACT = "contactId";
    public static final String ADVANCED_SEARCH_CRITERIA_TYPE = "criteriaSelect";
    public static final String OPEN_PARENTHESIS = "(";
    public static final String CLOSE_PARENTHESIS = ")";
    public static final String FROM_DATE_BEFORE_TO_DATE = "From Date should be before To Date";
    public static final String TO_DATE_AFTER_FROM_DATE = "To Date should be after From Date";
    public static final String ADVANCED_SEARCH_STARTS_WITH_CRITERIA = "STARTS WITH";
    public static final String ADVANCED_SEARCH_DATE_TRUNC = "trunc (%s)";
    public static final String MSG_RESOURCE_BUNDLE = "msg";
    public static final String BILL_TO_BILL_FROM = "Please provide the valid Billing Period To and Billing Period from date";
    public static final String BILL_PERIODS_MANDATORY = "Please provide value for one of the following: Billing Period From/To, Billing Period or Bill Thru Date";
    public static final String PARAM_EFFECTIVE_DATE = "peffectiveDate";
    public static final String SEARCH_INPUT_INVALID = "Please enter the valid Search Input";
    public static final String SEARCH_INPUT_INVALID_DETAIL = "Please enter the valid Search Input";
    public static final String BILL_TO_GREATER_BILL_FROM = "Billing Period To should be after Billing Period from date";
    public static final String UPPER = "UPPER";
    public static final String TENANT_CASE = "TENANT";
    public static final String FREE_MONTHS_MAX_VALUE = "Free Months Max Value";
    public static final String NEW_EVENT_ENABLE = "Event Feature";
    public static final String MASS_ORDER_ACTIVATION = "MASS_ORDER_ACTIVATION";
    public static final String MASS_ORDER_ACTIVATION_DESC = "Mass Order Activation";
    public static final int SERVER_PORT_NUMBER = 8001;
    public static final String ADVANCED_PRICING = "Advance Pricing";
    public static final String APPLY_LEVEL_TENANT = "TENANT";
    public static final String REPORT_CONC_REQ_TYPE = "Report";
    public static final String PARAM_REPORT_NAME = "preportname";
    public static final String PARAM_REPORT_TYPE = "preporttype";
    public static final int REPORT_CONC_REQ_TYPE_THRESHOLD = 5;
    public static final String MAX_ROWS_EXPORT = "Max Rows Export";
    public static final String MAX_ROWS_LIMIT_ERROR = "Max Rows Limit Error";
    public static final String MIN_ROWS_LIMIT_ERROR = "Min Rows Limit Error";
    public static final String MAX_ROWS_LIMIT_ERROR_MESSAGE = "Export for this Report would be available in Requests Tab with Request ID : ";
    public static final String MAX_ROWS_LIMIT_ERROR_PROC_MESSAGE = "Exception Occured while processing , kindly contact support Team";
    public static final String AUDIT_REPORT_EXPORT = "AUDIT_REPORT_EXPORT";
    public static final String MAX_ROWS_LIMIT_CONFIG_ERROR_MESSAGE = "Configure Max Rows Limit for Reports in Ab-Orb";
    public static final String MIN_ROWS_LIMIT_ERROR_MESSAGE = "No records to downloads";
    public static final String THREAD_TIME_OUT = "Thread Time Out";
    public static final int DEF_THREAD_TIME_OUT = 60;
    public static final String REPORT_THRESHOLD_LIMIT = "Report Threshold Limit";
    public static final String P_BATCH_NAME = "pBatchName";
    public static final String P_BATCH_ID = "pBatchId";
    public static final String INVOICE_BATCH_STR = "BATCH_";
    public static final String BACK_FEED_FILE_TYPE_SYNC = "finTrxBackFeedSync";
    public static final String BACK_FEED_FILE_TYPE = "finTrxBackFeed";
    public static final String EVERGREEN_DATE_VAL_FRMT1 = "4712-12-31";
    public static final String EVERGREEN_DATE_VAL_FRMT2 = "31-Dec-4712";
    public static final String AUDIT_REPORT_EXPORT_COMPLETE = "AUDIT_REPORT_EXPORT_COMPLETE";
    public static final String STANDARD_PRICE_LIST_TYPE = "STANDARD";
    public static final String ADVANCED_PRICE_LIST_TYPE = "PRL";
    public static final String PR_DELIVERY_SOURCE_TYPE = "PR_DELIVERY_SOURCE_TYPE";
    public static final String SITE_PURPOSE = "SITE_PURPOSE";
    public static final String AMENDMENT_STATUS_NO_CHANGE = "NO_CHANGE";
    public static final String AMENDMENT_STATUS_CHANGED = "CHANGED";
    public static final String CPI_CHECK_BILLED_LINES_ERR_MSG = "Index is used on at least one order line, having Billed True-up line.";
    public static final String DEFERRED_REVENUE_REPORT = "Deferred Revenue";
    public static final String PI_DEFERRED_FROM = ":pi_deferred_from";
    public static final String DEFERRED_FROM = "DEFERRED_FROM";
    public static final String ORDER_ID = "ORDER_ID";
    public static final String PI_ORDER_ID = ":pi_order_id";
    public static final Long IMPORT_MAXFILESIZE = 104857600L;
    public static final String RPT_ORDER_CHANGES = "Order Changes";
    public static final String RPT_ORDER_CHANGE_CONDITION = "where  CREATION_DATE >= :fromDateCpi  and CREATION_DATE <= :toDateCpi";
    public static final String RPT_ORD_CHNG_CNDTN_WITH_TERMNTN_DATE = "where ((CREATION_DATE BETWEEN :fromDateCpi AND :toDateCpi ) OR (LINE_TERMINATION_DATE BETWEEN :fromDateCpi AND :toDateCpi)) ";
    public static final String PLUS_SIGN = "+";
    public static final String ORDER_IMPORT_V2_REST_API_LOGS_PROCESS = "ORDER_IMPORT_V2_REST_API_LOGS_PROCESS";
    public static final String ORDER_IMPORT_REST_LOG_FILENAME = "orderAPIlogs";
    public static final String INVOICE_LINE_TYPE = "LINE";
    public static final String INVOICE_TAX_LINE_TYPE = "TAX";
    public static final String CSV_FILE_CLASS_NAME = "CoreLogMessages";
    public static final String PARAM_ORDER_NUMBER = "pOrderNumber";
    public static final String TAX_SUMMARY_ATT3_STARTS_WITH = "S_";
    public static final String TAX_LINE_ATT3_STARTS_WITH = "D_";

    // Event parameters
    public static final String P_EVENT_NAME = "pEventName";
    public static final String P_EVENT_POST_ACTION = "pEventPostAction";
    public static final String P_EVENT_RELEASE_ACTION = "pEventReleaseAction";
    public static final String P_EVENT_SOURCE = "pEventSource";
    public static final String P_EVENT_NOTES = "pEventNotes";
    public static final String P_EVENT_REASON = "pEventReason";
    public static final String P_EVENT_POST_ACTUAL_DATE = "pEventPostActualDate";
    public static final String P_EVENT_RELEASE_ACTUAL_DATE = "pReleasePostActualDate";
    public static final String P_EVENT_ORG_SET_ID = "pEventOrgSetId";
    public static final String P_EVENT_RET_STATUS = "pEventRetStaus";
    public static final String P_EVENT_RET_MESSAGE = "pEventRetMessage";
    public static final String P_EVENT_SOURCE_API = "API";
    public static final String P_EVENT_HDR_REFERENCE_NUMBER = "pEventHdrReferenceNumber";
    public static final String P_EVENT_LINE_REFERENCE_NUMBER = "pEventLineReferenceNumber";
    public static final String PR_CUSTOM_TYPES = "PR_CUSTOM_TYPES";
    public static final String PRE_BUILT = "PRE-BUILT";
    public static final String PD = "PD";

    public static final String ACCOUNT_NAME = "ACCOUNT_NAME";
    public static final String ACCT_NAME_PARAM = ":pi_cust_acct_name";
    public static final String PARTNER_NUMBER = "PARTNER_NUMBER";
    public static final String PARTNER_NUMBER_PARAM = ":pi_partner_number";
    public static final String PARTNER_NAME = "PARTNER_NAME";
    public static final String PARTNER_NAME_PARAM = ":pi_partner_name";
    public static final String LEGACY_ENTITY = "LEGACY_ENTITY";
    public static final String LEGACY_ENTITY_PARAM = ":pi_legacy_entity";
    public static final String START_DATE_PARAM = ":pi_start_date";
    public static final String END_DATE_PARAM = ":pi_end_date";
    public static final String STARTDATE = "pi_start_date";
    public static final String ENDDATE = "pi_end_date";

    public static final String INVOICE_FILE_TYPE = "invoices";
    public static final String IMPORT_INVOICES_DESC = "Import Invoices";

    public static final String LEDGER = "LEDGER";
    public static final String LEGAL_ENTITY_PROFILE_OPTION = "Enable Legal Entity";
    public static final String PARTIAL_BILLED_BILLING_STATUS = "PARTIAL_BILLED";
    public static final String FIXED = "FIXED";
    public static final String DELIVERY = "DELIVERY";
    public static final String LIST = "LIST";
    public static final String TIER = "TIER";
    public static final String TIER_UNIT = "TIER_UNIT";
    public static final String TIER_FIXED = "TIER_BLOCK";

    public static final String TENANT_ID_NAME = "TENANT_ID";
    public static final String BATCH_SEQ_NUM = "BATCH_SEQ_NUM";
    public static final String BATCH_SEQ_NUM_SQL_ENTRY = "DEL_IFACE_S.nextval";
    public static final String BILL_DATE_ERROR = "Please provide the valid Bill Date To and Bill Date from date";
    public static final String INDEX_REFERENCE_NUM = "INDEX_REFERENCE_NUM";
    public static final String LINE_ID_NAME = "LINE_ID";
    public static final String ORDER_ID_NAME = "ORDER_ID";
    public static final String PAY_DEL_STATUS = "PAID";

    // Supplier sync related messages
    public static final String INVALID_VENDOR_ID = "Invalid Vendor Id";
    public static final String INVALID_SUPP_NAME = "Invalid Supplier Name";
    public static final String SUPPLIER_STATUS = "A";
    public static final String SFTP = "SFTP";
    public static final String EXPORT_DELIVERY_TEMPLATE_DATA_REPORT = "Export Delivery Template Data";
    public static final String CSV = "csv";
    public static final String DATE_FROM = "DATE_FROM";
    public static final String PI_DATE_FROM = ":pi_date_from";
    public static final String DATE_TO = "DATE_TO";
    public static final String PI_DATE_TO = ":pi_date_to";
    public static final String REPORT_DATE = "REPORT_DATE";
    public static final String PI_REPORT_DATE = ":pi_report_date";
    public static final String PRODUCT = "PRODUCT";
    public static final String PI_PRODUCT = ":pi_item";
    public static final String PI_BILLING_CHANNEL = ":pi_billing_channel";
    public static final String BILL_TO_CUSTOMER = "BILL_TO_CUSTOMER";
    public static final String PI_BILL_TO_CUSTOMER = ":pi_account_number";
    public static final String BILL_TO_CUSTOMER_ORIG_SYS_REF = "BILL_TO_CUSTOMER_ORIG_SYS_REF";
    public static final String PI_BILL_TO_CUSTOMER_ORIG_SYS_REF = ":pi_bill_to_acct_orig_sys_ref";
    public static final String SOLD_TO_CUSTOMER = "SOLD_TO_CUSTOMER";
    public static final String PI_SOLD_TO_CUSTOMER = ":pi_sell_to_account_number";
    public static final String SOLD_TO_CUSTOMER_ORIG_SYS_REF = "SOLD_TO_CUSTOMER_ORIG_SYS_REF";
    public static final String PI_SOLD_TO_CUSTOMER_ORIG_SYS_REF = ":pi_sell_to_orig_sys_ref";

    public static final String DATE_FROM_PARAM = "dateFrom";
    public static final String DATE_TO_PARAM = "dateTo";
    public static final String REPORT_DATE_PARAM = "reportDate";
    public static final String PRODUCT_PARAM = "product";
    public static final String BILLING_CHANNEL_PARAM = "billingChannel";
    public static final String BILL_TO_CUSTOMER_PARAM = "billToCustomer";
    public static final String BILL_TO_CUSTOMER_ORIG_SYS_REF_PARAM = "billToCustomerOrigSysRef";
    public static final String SOLD_TO_CUSTOMER_PARAM = "soldToCustomer";
    public static final String SOLD_TO_CUSTOMER_ORIG_SYS_REF_PARAM = "soldToCustomerOrigSysRef";
    public static final String PI_BUSINESS_UNIT = ":pi_bu_name";
    public static final String RANGE = "RANGE";
    public static final String DATE_FORMAT_FOR_INCREMENTAL_REPORT = "MM/dd/yyyy HH:mm:ss";
    public static final String INCREMENTAL_START_DATE_PARAM = "startDate";
    public static final String INCREMENTAL_END_DATE_PARAM = "endDate";
    public static final String BULK_API_CONC_REQ_TYPE = "Bulk API Process";
    public static final String CALLBACK_SFTP_METHOD = "Files";
    public static final String API_ADORB_SERVICE_TYPE = "ADORB";
    public static final String CALLBACK_SOAP_METHOD = "SOAP";
    public static final String SERVICE_TYPE_SPARK = "SPARK";
    public static final String BULK_API_REPORT_CONC_REQ_TYPE = "Bulk Api Report";
    public static final String BILLING_SITE_REPORT_ERR_MSG = "Site Number or Invoice From Date/Invoice To Date is blank";
    public static final String IMPORT_SUPPLIERS = "Import Suppliers";
    public static final String SYNC_IMPORT_URL = "pImportUrl";
    public static final String SYNC_SUPPLIERS = "supplierSync";
    public static final String REST_SUPPLIERS = "/api/v1/suppliers";
    public static final String IFACE_PROCESSED_STATUS = "PROCESSED";
    public static final String IFACE_ERROR_STATUS = "ERROR";
    public static final String SUCCESS = "Success";

    public static final String BULK_API_LOGS_PATH = "/u01/data/backup/adorb/bulkAPI/";
    public static final String UPDATE_LAST_SYNC_DATE = "UPDATE_LAST_SYNC_DATE";

    public static final String PROPEL_AUDIT_REPORT_PROGRAM = "AUDIT_REPORT_FOR_OVERRIDE_AMOUNTS";
    public static final String PROPEL_AUDIT_REPORT_PROGRAM_DESC = "Audit Report for Override Amounts";
    public static final String PROPEL_PI_AUDIT_FROM_DATE = "pi_propel_audit_from_date";
    public static final String PROPEL_PI_AUDIT_TO_DATE = "pi_propel_audit_to_date";
    public static final String PROPEL_AUDIT_REPORT_EXPORT = "AUDIT_REPORT_FOR_OVERRIDE_AMOUNTS_EXPORT";
    public static final String PROPEL_AUDIT_REPORT_EXPORT_COMPLETE = "AUDIT_REPORT_FOR_OVERRIDE_AMOUNTS_EXPORT_COMPLETE";
    public static final String ORDERS_IMPORT_TYPE = "ORDERS";
    public static final String DELIVERIES_IMPORT_TYPE = "DELIVERIES";
    public static final String PRICING_RULE_IMPORT_TYPE = "PR_RULES";

    public static final String PRICING_RULE_IMPORT_PROGRAM = "IMPORT_PRICING_RULES";
    public static final String PRICING_RULE_IMPORT_PROGRAM_DESC = "Import Pricing Rules";
    public static final String DELIVERY_PRICE_AT_TIER = "DELIVERY_PRICE_AT_TIER";
    public static final String DELIVERY_PRICE_AT_TIER_DESC = "Delivery Price at Tier";
    public static final String PRICING_ATTR_VALUE_FROM_LOV_MAP = "pricingAttrValueFromLovMap";
    public static final String PRICING_ATTR_VALUE_TO_LOV_MAP = "pricingAttrValueToLovMap";

    public static final String IMPORT_PRODUCTS = "Import Products";
    public static final String SYNC_PRODUCTS = "syncProducts";
    public static final String FUSION_PRODUCT_SYNC_URL = "fusionProductUrl";
    public static final String PRODUCT_SYNC_ITEM_CLASS = "itemClass";

    public static final String INCREMENTAL_REPORTS_SCHEDULE_PROGRAM = "INCREMENTAL_REPORTS_SCHEDULE_PROGRAM";
    public static final String INCREMENTAL_REPORTS_SCHEDULE_PROGRAM_DESC = "Schedule Incremental Reports";

    public static final String GEN_MIN_SCHEDULE = "GEN_MIN_SCHEDULE";
    public static final String MIN_MAX_BILLING_PERIOD = "MIN_MAX_BILLING_PERIOD";
    public static final String MIN_MAX_PERIOD_TYPE = "MIN_MAX_PERIOD_TYPE";
    public static final String IMPORT_CUSTOMER_ACCOUNTS_DESC = "Sync Customer Accounts";
    public static final String TERM_FEATURE = "Terms Feature";
    public static final String AUDIT_FOR_PRICING_SCHEDULES = "AUDIT_FOR_PRICING_RULES";
    public static final String AUDIT_FOR_PRICING_SCHEDULES_DESC = "Audit Report for Pricing Rules";
    public static final String AUDIT_FOR_PRICING_SCHEDULES_EXPORT = "AUDIT_FOR_PRICING_RULES_EXPORT";

    public static final String DEFAULT_START_DATE_FOR_INCREMENTAL_REPORT = "01/01/1800 00:00:00";
    public static final String DEFAULT_END_DATE_FOR_INCREMENTAL_REPORT = "12/31/4712 00:00:00";

    public static final String EXPORT_DEL_TEMPLATE = "EXPORT_DEL_TEMPLATE";

    public static final String PRICING_RULES_IFACE_TBL = "PRICING_RULES_IFACE";
    public static final String DELIVERIES_IFACE_TBL = "DELIVERIES_IFACE";
    public static final String ORDER_LINES_IFACE_TBL = "ORDER_LINES_IFACE";
    public static final String IMPORT_PRODUCTS_DESC = "Sync Products";

    public static final String BULK_API_INBOUND_REQTYPE = "Inbound";
    public static final String BULK_API_OUTBOUND_REPORT_REQTYPE = "OutboundBoundReport";
    public static final String BULK_API_CALLBACK_REQTYPE = "AdorbCallBackRequest";

    public static final String BULK_API_FULL_DUMP_RPRT_CATEGORY = "F";
    public static final String BULK_API_CUSTOM_RPRT_CATEGORY = "C";

    public static final String BULK_API_REPORT_FULL_DUMP_END_DATE = "12-31-4712";
    public static final String BULK_API_REPORT_FULL_DUMP_START_DATE = "01-01-1800";

    public static final String DATA_YEAR = "dataYear";
    public static final String ARR_YEAR_PARAM = ":piArrYearParam";
    public static final String COMPONENT_ITEM_TYPE = "COMPONENT";
    public static final String GL_PERIOD_TYPE = "GL_PERIOD_TYPE";
    public static final String ALL = "ALL";
    public static final String LIMIT_FOR_GET_ORDER_API = "25";
    public static final String TENANT_ID_ORD_DEL_IMPORT = "TENANT_ID";
    public static final String CAL_PERIOD_SET = "CAL_PERIOD_SET";
    public static final String DEFAULT_CALENDAR = "Default Calendar";
    public static final String FISC_PERIOD_SET = "FISC_PERIOD_SET";
    public static final String CUSTOM_CALENDAR = "Custom Calendar";
    public static final String RADIUS_TRANSACTION_AND_CHARGES_REPORT = "RADIUS_TRANSACTION_AND_CHARGES_REPORT";
    public static final String RADIUS_TRANSACTION_AND_CHARGES_REPORT_DESC = "Radius Transaction and Charges Report";
    public static final String RADIUS_TRANSACTION_AND_CHARGES_REPORT_EXPORT = "RADIUS_TRANSACTION_AND_CHARGES_REPORT_EXPORT";
    public static final String PI_PROGRAM_DESC = "pi_program_desc";
    public static final String PI_STORAGE_CONTAINER = "pi_storage_container";
    public static final String PI_OUTPUT_FILENAME = "pi_output_filename";
    public static final String PI_SPARK_ARGUMENTS = "pi_spark_arguments";
    public static final String PI_HTTP_END_POINT = "pi_http_end_point";
    public static final String GENERATE_BILLING_SCHEDULE_REQ_DESC = "Generate Billing Schedules";
    public static final String TERM_TYPE = "TERM_TYPE";
    public static final String TERM_PERIOD = "TERM_PERIOD";
    public static final String TERM_ACTIONS = "TERM_ACTIONS";
    public static final String TERM_ACTION_PERIOD = "TERM_ACTION_PERIOD";
    public static final String PRODUCT_VALID = "Product is invalid";
    public static final String PRODUCT_VALID_DETAIL = "Please select the valid Product";
    public static final String BILLING_AT_DELIVERY = "Billing at Delivery";
    public static final String USAGE_TO_AR = "Interface Usage to AR";
    public static final String USAGE_TO_FS = "Interface Usage to Financial System";
    public static final String TAX_CODES = "LINE_TAX_CODE";
    public static final String INTERFACE_TO_CLOUD_STORAGE = "INTERFACE_TO_CLOUD_STORAGE";
    public static final String INTERFACE_TO_CLOUD_STORAGE_DESC = "Interface file to Cloud Storage";
    public static final String PI_REPRICE = "pi_reprice";
    public static final String DATE_TIME_TYPE = "DateTime";
    public static final String REPORT_GENERIC_V_CLASS_NAME = "RptGenericView";
    public static final String MAX_FRACTION_DIGITS = "Unit Price Precision";
    public static final String PRODUCT_LIST_MAP = "productList";
    public static final String SYSTEM_TIER = "SYSTEM";
    public static final String PRICERULE_TIER = "PRICERULE";
    public static final String LINE_PRICE_CHANGE_PROFILE_OPTION = "Generate CM/DM for Line Billing Amount Change";
    public static final String EQUALS = "Equals";

    public static final String BILL_EVENT_AUDIT_REPORT_PROGRAM = "BILL_EVENT_AUDIT_REPORT";
    public static final String BILL_EVENT_AUDIT_REPORT_PROGRAM_DESC = "Bill Event Audit Report";
    public static final String CHANGE_REQUEST = "CHANGE_REQUEST";
    public static final String PARAM_PARENT_REQUEST_ID = "parentrequestid";

    public static final String TAX_UPDATE_PROGRAM = "CUST_TAX_UPDATE";
    public static final String TAX_UPDATE_PROGRAM_DESC = "Update Tax Information";
    public static final String TIER_PRICING_SOURCE_TYPES = "TIER_SCOPE";
    public static final String VALUE_CHANGE = "VALUE_CHANGE";
    public static final String ATTRIBUTE9 = "ATTRIBUTE9";

    public static final String SYNC_ORDERS_TO_PPM="SYNC_ORDERS_TO_PPM";
    public static final String SYNC_ORDERS_TO_PPM_DESC="Sync Orders to PPM";
    public static final String HTTPS_PREFIX = "https://";

    public static final String IDM_TENANT_IDENTIFIER="idmTenantIdentifier";

    public static final String AUTO_INVOICE_PARAM_NO_OF_WORKERS="Auto Invoice param Number Of Workers value";
    public static final String AUTO_INVOICE_PARAM_NO_OF_WORKERS_VALUE="50";
    public static final String DRAFT_STATUS="DRAFT";
    public static final String PRICE_RULE_IMPORT_REPORT ="PRICE_RULE_IMPORT_REPORT";
    public static final String PRICE_RULE_IMPORT_REPORT_DESC ="Pricing Rules Report";

    public static final String LOAD_LOOKUP_END_POINT = "/loadLookupList";
    public static final String LOAD_PRICELIST_END_POINT = "/loadPriceList";
    
    public static final String NO_RECORDS_FOUND = "No records Found";
    
 // Validation Error Messages
 	public static final String USERNAME_VALIDATION_ERROR = "Username from token can't be found in DB";
 	public static final String INVALID_REQUEST_ERROR = "Request Boby can not be null";
 	public static final String BAD_REQUEST_ERROR = "Invalid input value is provided.";
 	public static final String UNAUTHORISED_STATUS_ERROR_MESSAGE = "Unauthorized to perform operation.";
 	public static final String INCORRECT_SORT_METHOD = "Incorrect Sort Parameter.";
 	public static final String NO_DATA_FOUND_FOR_DELETE = "No data found for delete.";

}
