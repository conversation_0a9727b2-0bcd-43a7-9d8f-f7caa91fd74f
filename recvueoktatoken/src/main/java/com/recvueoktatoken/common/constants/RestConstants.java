package com.recvueoktatoken.common.constants;

public class RestConstants {

    //sucess message
    public static final String SUCESS_MESSGE = "Success";

    //failure message whenever user authentication failed.
    public static final String AUTHENTICATION_FAILED = "User Authentication Failed.";

    //represents success status code.
    public static final int SUCCESS_STATUS_CODE = 200;

    //represents failure status code.
    public static final int FAILURE_STATUS_CODE = 2001;

    //default invoice status 
    public static final String DEFAULT_INVOICE_STATUS = "POSTED";

    //represents warning status Code
    public static final int WARNING_STATUS_CODE = 2002;
    
    //represents failure status code.
    public static final int REPORT_LIMIT_FAILURE_STATUS_CODE = 429;
    
    //Location to store scheduled reports, used for testing, should be removed
    public static String SCHEDULE_REPORT_PATH = "C:\\My Documents\\";
    
    //Default name format of scheduled report
    public static String REPORT_NAME_FORMAT = "%s_%s.csv";
    
    public static String REPORT_NAME_PDF_FORMAT = "%s_%s.pdf";
    
    public static String REPORT_NAME_XLS_FORMAT = "%s_%s.xls";
   
    public static final int CREATED_STATUS_CODE = 201;
    public static final int ACCEPTED_STATUS_CODE = 202;
    public static final int NO_CONTENT_SUCCESS_STATUS_CODE = 204;
    public static final int INVALID_STATUS_CODE = 400;
    public static final int UNAUTHORISED_STATUS_CODE = 401;
    public static final int FORBIDDEN_STATUS_CODE = 403;
    public static final int NOT_FOUND_STATUS_CODE = 404;
    public static final int REQUEST_TIMEOUT_STATUS_CODE = 408;
    public static final int DUPLICATE_DATA_STATUS_CODE = 409;
    public static final int PRE_CONDITION_STATUS_CODE = 412;
    public static final int BAD_REQUEST_STATUS_CODE = 400;
    public static final int INTERNAL_SERVER_ERROR_STATUS_CODE = 500;
    
  //represents failure messages
    public static final String UNAUTHORISED_STATUS_ERROR_MESSAGE = "Unauthorized to perform operation.";
    public static final String USERNAME_VALIDATION_ERROR_MESSAGE = "Username from token can't be found in DB";
    public static final String INVALID_REQUEST_ERROR_MESSAGE = "Request Boby can not be null";
    public static final String INVALID_REQUEST_BODY = "Invalid Request Boby";
    public static final String INVALID_FROM_DATE_ERROR_MESSAGE = "From date is null or invalid";
    public static final String DUPLICATE_PERMISSION_ERROR_MESSAGE = "Duplicate permissions found in the request";
    public static final String TECHNICAL_ERROR_MESSAGE = "Internal Error occurred while executing request. Please contact administrator.";
    public static final String INVALID_SYNC_PARAM = "Sync parameter in the request URL can not be null";
    public static final String INVALID_PROD_CAT_ERROR_MESSAGE = "Product catalog id can not be null or empty";
    public static final String INVALID_CUST_TRANSACTION_TYPE_ID_ERROR_MESSAGE = "Transaction type id can not be null or empty";
    public static final String RECORD_DOES_NOT_EXIST_MESSAGE = "Record does not exist for the given id";
    
    public static final String DUPLICATE_ROLE_ERROR_MESSAGE = "Duplicate roles found in the request";
    

}
