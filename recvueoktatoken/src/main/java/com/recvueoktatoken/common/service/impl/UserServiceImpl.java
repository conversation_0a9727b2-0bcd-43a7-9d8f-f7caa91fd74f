package com.recvueoktatoken.common.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.recvueoktatoken.common.dao.UserDao;
import com.recvueoktatoken.common.entity.User;
import com.recvueoktatoken.common.entity.UserAttributes;
import com.recvueoktatoken.common.service.UserService;

@Service
@Transactional(propagation = Propagation.REQUIRED, readOnly = true)
public class UserServiceImpl implements UserService {

	@Autowired
	private UserDao userDao;
	
	@Override
	public List<UserAttributes> getCoreUserAttributeByUserId(Long userId) {
	        return userDao.getCoreUserAttributeByUserId(userId);
	 }

	  @Override
	  public User getUserById(Long userId) {
	        return userDao.getUserById(userId);
	  }

}
