/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.common.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.recvueoktatoken.common.dao.ApiAuthUserDAO;
import com.recvueoktatoken.common.entity.User;
import com.recvueoktatoken.common.service.ApiAuthUserService;


@Service
@Transactional(propagation = Propagation.REQUIRED, readOnly = true)
public class ApiAuthUserServiceImpl implements ApiAuthUserService {
    
    @Autowired
    private ApiAuthUserDAO ApiAuthUserDAO;

    public ApiAuthUserDAO getApiAuthUserDAO() {
        return ApiAuthUserDAO;
    }

    public void setApiAuthUserDAO(ApiAuthUserDAO ApiAuthUserDAO) {
        this.ApiAuthUserDAO = ApiAuthUserDAO;
    }
    
    @Override
    public User getUserByName(String userName){
        return getApiAuthUserDAO().getUserByName(userName);
    }
    
     @Override
    public User getUserById(Long userId) {
        return getApiAuthUserDAO().getUserById(userId);
    }
    
    
}
