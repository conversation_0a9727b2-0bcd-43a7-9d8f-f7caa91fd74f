package com.recvueoktatoken.authorizer.model;

import java.io.Serializable;
import java.util.Objects;

/**
 * Business unit information containing ID and name.
 * This class represents business unit access information for users.
 */
public class BusinessUnitInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long businessUnitId;
    private String businessUnitName;

    public BusinessUnitInfo() {
    }

    public BusinessUnitInfo(Long businessUnitId, String businessUnitName) {
        this.businessUnitId = businessUnitId;
        this.businessUnitName = businessUnitName;
    }

    /**
     * Gets the business unit ID
     * @return Business unit ID
     */
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    /**
     * Sets the business unit ID
     * @param businessUnitId Business unit ID
     */
    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    /**
     * Gets the business unit name
     * @return Business unit name
     */
    public String getBusinessUnitName() {
        return businessUnitName;
    }

    /**
     * Sets the business unit name
     * @param businessUnitName Business unit name
     */
    public void setBusinessUnitName(String businessUnitName) {
        this.businessUnitName = businessUnitName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BusinessUnitInfo that = (BusinessUnitInfo) o;
        return Objects.equals(businessUnitId, that.businessUnitId) &&
                Objects.equals(businessUnitName, that.businessUnitName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(businessUnitId, businessUnitName);
    }

    @Override
    public String toString() {
        return "BusinessUnitInfo{" +
                "businessUnitId=" + businessUnitId +
                ", businessUnitName='" + businessUnitName + '\'' +
                '}';
    }
}