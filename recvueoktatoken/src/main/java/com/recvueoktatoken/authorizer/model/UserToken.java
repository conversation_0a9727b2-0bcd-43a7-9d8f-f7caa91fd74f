package com.recvueoktatoken.authorizer.model;

import java.io.Serializable;
import java.util.List;
import java.util.ArrayList;

public class UserToken implements Serializable {

	private static final long serialVersionUID = 1L;

	private String accessToken;
	
	private String jwtToken;
	
	private String login;
	
	private String firstName;
	
	private String lastName;
	
	private String email;
	
	private String profileNames;
	
	private Long expiryTime;
	
	private String serverName;
	
	// Authorization fields
	private List<String> roles;
	
	private List<String> permissions;
	
	private List<BusinessUnitInfo> businessUnits;
	
	private boolean readOnlyAccess;

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public String getLogin() {
		return login;
	}

	public void setLogin(String login) {
		this.login = login;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getProfileNames() {
		return profileNames;
	}

	public void setProfileNames(String profileNames) {
		this.profileNames = profileNames;
	}

	public Long getExpiryTime() {
		return expiryTime;
	}

	public void setExpiryTime(Long expiryTime) {
		this.expiryTime = expiryTime;
	}

	public String getServerName() {
		return serverName;
	}

	public void setServerName(String serverName) {
		this.serverName = serverName;
	}

	public String getJwtToken() {
		return jwtToken;
	}

	public void setJwtToken(String jwtToken) {
		this.jwtToken = jwtToken;
	}

	public List<String> getRoles() {
		return roles;
	}

	public void setRoles(List<String> roles) {
		this.roles = roles != null ? new ArrayList<>(roles) : new ArrayList<>();
	}

	public List<String> getPermissions() {
		return permissions;
	}

	public void setPermissions(List<String> permissions) {
		this.permissions = permissions != null ? new ArrayList<>(permissions) : new ArrayList<>();
	}

	public List<BusinessUnitInfo> getBusinessUnits() {
		return businessUnits;
	}

	public void setBusinessUnits(List<BusinessUnitInfo> businessUnits) {
		this.businessUnits = businessUnits != null ? new ArrayList<>(businessUnits) : new ArrayList<>();
	}

	public boolean isReadOnlyAccess() {
		return readOnlyAccess;
	}

	public void setReadOnlyAccess(boolean readOnlyAccess) {
		this.readOnlyAccess = readOnlyAccess;
	}

	/**
	 * Creates a UserToken from an AuthorizationContext and user information
	 * @param context The authorization context
	 * @param login User login
	 * @param firstName User first name
	 * @param lastName User last name
	 * @param email User email
	 * @param jwtToken JWT token
	 * @param expiryTime Token expiry time
	 * @param serverName Server name
	 * @return UserToken with authorization information
	 */
	public static UserToken fromAuthorizationContext(AuthorizationContext context, String login, 
			String firstName, String lastName, String email, String jwtToken, 
			Long expiryTime, String serverName) {
		UserToken userToken = new UserToken();
		userToken.setLogin(login);
		userToken.setFirstName(firstName);
		userToken.setLastName(lastName);
		userToken.setEmail(email);
		userToken.setJwtToken(jwtToken);
		userToken.setExpiryTime(expiryTime);
		userToken.setServerName(serverName);
		
		if (context != null) {
			userToken.setRoles(context.getRoleNames());
			userToken.setPermissions(context.getPermissionNames());
			userToken.setBusinessUnits(context.getBusinessUnits());
			userToken.setReadOnlyAccess(context.isReadOnlyAccess());
		}
		
		return userToken;
	}

	/**
	 * Converts this UserToken to an AuthorizationContext
	 * @return AuthorizationContext containing authorization information
	 */
	public AuthorizationContext toAuthorizationContext() {
		return new AuthorizationContext(roles, permissions, businessUnits, readOnlyAccess);
	}
	
}
