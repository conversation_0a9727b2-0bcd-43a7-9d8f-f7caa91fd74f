package com.recvueoktatoken.authorizer.model;

import java.io.Serializable;
import java.util.List;
import java.util.ArrayList;

/**
 * Authorization context containing user's roles, permissions, and business unit access information.
 * This class is used to encapsulate all authorization-related data for a user.
 */
public class AuthorizationContext implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<String> roleNames;
    private List<String> permissionNames;
    private List<BusinessUnitInfo> businessUnits;
    private boolean readOnlyAccess;
    private long cacheTimestamp;

    public AuthorizationContext() {
        this.roleNames = new ArrayList<>();
        this.permissionNames = new ArrayList<>();
        this.businessUnits = new ArrayList<>();
        this.readOnlyAccess = false;
        this.cacheTimestamp = System.currentTimeMillis();
    }

    public AuthorizationContext(List<String> roleNames, List<String> permissionNames, 
                              List<BusinessUnitInfo> businessUnits, boolean readOnlyAccess) {
        this.roleNames = roleNames != null ? new ArrayList<>(roleNames) : new ArrayList<>();
        this.permissionNames = permissionNames != null ? new ArrayList<>(permissionNames) : new ArrayList<>();
        this.businessUnits = businessUnits != null ? new ArrayList<>(businessUnits) : new ArrayList<>();
        this.readOnlyAccess = readOnlyAccess;
        this.cacheTimestamp = System.currentTimeMillis();
    }

    /**
     * Gets the list of role names assigned to the user
     * @return List of role names, never null
     */
    public List<String> getRoleNames() {
        return roleNames;
    }

    /**
     * Sets the list of role names assigned to the user
     * @param roleNames List of role names
     */
    public void setRoleNames(List<String> roleNames) {
        this.roleNames = roleNames != null ? new ArrayList<>(roleNames) : new ArrayList<>();
    }

    /**
     * Gets the aggregated list of permission names from all user roles
     * @return List of permission names, never null
     */
    public List<String> getPermissionNames() {
        return permissionNames;
    }

    /**
     * Sets the aggregated list of permission names from all user roles
     * @param permissionNames List of permission names
     */
    public void setPermissionNames(List<String> permissionNames) {
        this.permissionNames = permissionNames != null ? new ArrayList<>(permissionNames) : new ArrayList<>();
    }

    /**
     * Gets the list of business units the user has access to
     * @return List of business unit information, never null
     */
    public List<BusinessUnitInfo> getBusinessUnits() {
        return businessUnits;
    }

    /**
     * Sets the list of business units the user has access to
     * @param businessUnits List of business unit information
     */
    public void setBusinessUnits(List<BusinessUnitInfo> businessUnits) {
        this.businessUnits = businessUnits != null ? new ArrayList<>(businessUnits) : new ArrayList<>();
    }

    /**
     * Checks if the user has read-only access
     * @return true if user has read-only access, false otherwise
     */
    public boolean isReadOnlyAccess() {
        return readOnlyAccess;
    }

    /**
     * Sets the read-only access flag for the user
     * @param readOnlyAccess true if user has read-only access
     */
    public void setReadOnlyAccess(boolean readOnlyAccess) {
        this.readOnlyAccess = readOnlyAccess;
    }

    /**
     * Gets the timestamp when this authorization context was cached
     * @return Cache timestamp in milliseconds
     */
    public long getCacheTimestamp() {
        return cacheTimestamp;
    }

    /**
     * Sets the cache timestamp for this authorization context
     * @param cacheTimestamp Cache timestamp in milliseconds
     */
    public void setCacheTimestamp(long cacheTimestamp) {
        this.cacheTimestamp = cacheTimestamp;
    }

    /**
     * Checks if the user has a specific role
     * @param roleName The role name to check
     * @return true if user has the role, false otherwise
     */
    public boolean hasRole(String roleName) {
        return roleName != null && roleNames.contains(roleName);
    }

    /**
     * Checks if the user has a specific permission
     * @param permissionName The permission name to check
     * @return true if user has the permission, false otherwise
     */
    public boolean hasPermission(String permissionName) {
        return permissionName != null && permissionNames.contains(permissionName);
    }

    /**
     * Checks if the user has access to a specific business unit
     * @param businessUnitId The business unit ID to check
     * @return true if user has access to the business unit, false otherwise
     */
    public boolean hasBusinessUnitAccess(Long businessUnitId) {
        if (businessUnitId == null) {
            return false;
        }
        return businessUnits.stream()
                .anyMatch(bu -> businessUnitId.equals(bu.getBusinessUnitId()));
    }

    /**
     * Checks if the authorization context is empty (no roles, permissions, or business units)
     * @return true if context is empty, false otherwise
     */
    public boolean isEmpty() {
        return roleNames.isEmpty() && permissionNames.isEmpty() && businessUnits.isEmpty();
    }

    @Override
    public String toString() {
        return "AuthorizationContext{" +
                "roleNames=" + roleNames +
                ", permissionNames=" + permissionNames +
                ", businessUnits=" + businessUnits +
                ", readOnlyAccess=" + readOnlyAccess +
                ", cacheTimestamp=" + cacheTimestamp +
                '}';
    }
}