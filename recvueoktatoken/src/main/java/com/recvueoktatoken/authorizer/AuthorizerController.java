/**
 * 
 */
package com.recvueoktatoken.authorizer;

import java.util.Collections;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.recvueoktatoken.authorizer.request.Constants;
import com.recvueoktatoken.authorizer.request.ForbiddenException;
import com.recvueoktatoken.authorizer.request.GenerateJWT;
import com.recvueoktatoken.authorizer.request.TokenNames;

@RestController
public class AuthorizerController {

	@Autowired
	GenerateJWT generateJwt;

//	@Autowired
//	RateLimiter rateLimiter;

	@Value("${auth.ratelimit}")
	private boolean isRateLimit = false;

	private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

	@GetMapping(value = "/api/v2.0/authorize", produces = "application/json")
	@ResponseBody
	public ResponseEntity<Object> getRecvueOktaToken(@RequestHeader Map<String, String> reqHeaders) {
		try {

//			if (isRateLimit) {
//				if (reqHeaders.get(TokenNames.userClient) != null
//						&& !reqHeaders.get(TokenNames.userClient).equals("UI")) {
//					Bucket bucket = rateLimiter.resolveBucket(reqHeaders.get(TokenNames.hostName));
//					if (!bucket.tryConsume(1)) {
//						return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).body("Rate limit exceeded.");
//					}
//				}
//				/*}else {
//					return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Collections.singletonMap("error", "Invalid Request"));
//				}*/
//				LOGGER.debug("Rate limit successfully");
//			}
			
			// Generate JWT token
			Map<String, String> map = generateJwt.generateJwtToken(reqHeaders);

			// Get token and tenant Identifier
			String token = map.get(Constants.jwtTokenMap);
			String tenantIdentifier = map.get(Constants.tenantIdentiferMap);
			String hostName = map.get(Constants.hostIdentifier);

			LOGGER.debug("Creating customer headers");
			HttpHeaders respHeaders = new HttpHeaders();
			respHeaders.add(TokenNames.tenantIdentifier, tenantIdentifier);
			respHeaders.add(TokenNames.xForwardedUser, token);
			respHeaders.add(TokenNames.hostName, hostName);

			return ResponseEntity.status(HttpStatus.OK).headers(respHeaders).body("");

		} catch (ForbiddenException e) {
			LOGGER.error(e.getMessage().toString());
			return ResponseEntity.status(HttpStatus.FORBIDDEN).body(Collections.singletonMap("message", e.getMessage()));
		} catch (Exception e) {
			LOGGER.error(e.getMessage().toString());
			return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
					.body(Collections.singletonMap("message", e.getMessage()));
		}
	}
}
