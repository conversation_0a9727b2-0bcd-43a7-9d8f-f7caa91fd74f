package com.recvueoktatoken.authorizer.monitoring;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Metrics collection and monitoring for authorization operations.
 * This component tracks performance, cache hit rates, and error statistics.
 */
@Component
public class AuthorizationMetrics {

    private static final Logger logger = LoggerFactory.getLogger(AuthorizationMetrics.class);

    // Cache metrics
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    private final AtomicLong cacheErrors = new AtomicLong(0);

    // Database metrics
    private final AtomicLong databaseQueries = new AtomicLong(0);
    private final AtomicLong databaseErrors = new AtomicLong(0);
    private final AtomicLong databaseFallbacks = new AtomicLong(0);

    // Performance metrics
    private final AtomicReference<Long> averageResponseTime = new AtomicReference<>(0L);
    private final AtomicLong totalResponseTime = new AtomicLong(0);
    private final AtomicLong totalRequests = new AtomicLong(0);

    // Authorization data metrics
    private final AtomicLong emptyAuthorizationContexts = new AtomicLong(0);
    private final AtomicLong readOnlyUsers = new AtomicLong(0);

    // JWT generation metrics
    private final AtomicLong jwtGenerationCount = new AtomicLong(0);
    private final AtomicLong jwtGenerationErrors = new AtomicLong(0);
    private final AtomicReference<Long> averageJwtSize = new AtomicReference<>(0L);

    /**
     * Records a cache hit event
     */
    public void recordCacheHit() {
        cacheHits.incrementAndGet();
        logger.debug("Authorization cache hit recorded. Total hits: {}", cacheHits.get());
    }

    /**
     * Records a cache miss event
     */
    public void recordCacheMiss() {
        cacheMisses.incrementAndGet();
        logger.debug("Authorization cache miss recorded. Total misses: {}", cacheMisses.get());
    }

    /**
     * Records a cache error event
     */
    public void recordCacheError() {
        cacheErrors.incrementAndGet();
        logger.warn("Authorization cache error recorded. Total cache errors: {}", cacheErrors.get());
    }

    /**
     * Records a database query event
     */
    public void recordDatabaseQuery() {
        databaseQueries.incrementAndGet();
        logger.debug("Authorization database query recorded. Total queries: {}", databaseQueries.get());
    }

    /**
     * Records a database error event
     */
    public void recordDatabaseError() {
        databaseErrors.incrementAndGet();
        logger.error("Authorization database error recorded. Total database errors: {}", databaseErrors.get());
    }

    /**
     * Records a database fallback event (when cache is used due to database issues)
     */
    public void recordDatabaseFallback() {
        databaseFallbacks.incrementAndGet();
        logger.warn("Authorization database fallback recorded. Total fallbacks: {}", databaseFallbacks.get());
    }

    /**
     * Records response time for authorization operations
     * 
     * @param responseTimeMs Response time in milliseconds
     */
    public void recordResponseTime(long responseTimeMs) {
        totalRequests.incrementAndGet();
        totalResponseTime.addAndGet(responseTimeMs);
        
        long totalReqs = totalRequests.get();
        long avgTime = totalResponseTime.get() / totalReqs;
        averageResponseTime.set(avgTime);
        
        if (responseTimeMs > 1000) { // Log slow operations
            logger.warn("Slow authorization operation detected: {}ms", responseTimeMs);
        }
        
        logger.debug("Authorization response time recorded: {}ms. Average: {}ms", responseTimeMs, avgTime);
    }

    /**
     * Records an empty authorization context event
     */
    public void recordEmptyAuthorizationContext() {
        emptyAuthorizationContexts.incrementAndGet();
        logger.debug("Empty authorization context recorded. Total: {}", emptyAuthorizationContexts.get());
    }

    /**
     * Records a read-only user event
     */
    public void recordReadOnlyUser() {
        readOnlyUsers.incrementAndGet();
        logger.debug("Read-only user recorded. Total: {}", readOnlyUsers.get());
    }

    /**
     * Records JWT generation event
     * 
     * @param jwtSize Size of the generated JWT token
     */
    public void recordJwtGeneration(int jwtSize) {
        jwtGenerationCount.incrementAndGet();
        
        // Update average JWT size
        long totalJwts = jwtGenerationCount.get();
        long currentAvg = averageJwtSize.get();
        long newAvg = ((currentAvg * (totalJwts - 1)) + jwtSize) / totalJwts;
        averageJwtSize.set(newAvg);
        
        if (jwtSize > 8192) { // Log large JWT tokens
            logger.warn("Large JWT token generated: {} bytes", jwtSize);
        }
        
        logger.debug("JWT generation recorded. Size: {} bytes. Average size: {} bytes", jwtSize, newAvg);
    }

    /**
     * Records JWT generation error
     */
    public void recordJwtGenerationError() {
        jwtGenerationErrors.incrementAndGet();
        logger.error("JWT generation error recorded. Total errors: {}", jwtGenerationErrors.get());
    }

    /**
     * Gets the cache hit ratio as a percentage
     * 
     * @return Cache hit ratio (0.0 to 100.0)
     */
    public double getCacheHitRatio() {
        long hits = cacheHits.get();
        long misses = cacheMisses.get();
        long total = hits + misses;
        
        if (total == 0) {
            return 0.0;
        }
        
        return (double) hits / total * 100.0;
    }

    /**
     * Gets the database error ratio as a percentage
     * 
     * @return Database error ratio (0.0 to 100.0)
     */
    public double getDatabaseErrorRatio() {
        long errors = databaseErrors.get();
        long queries = databaseQueries.get();
        
        if (queries == 0) {
            return 0.0;
        }
        
        return (double) errors / queries * 100.0;
    }

    /**
     * Logs current metrics summary
     */
    public void logMetricsSummary() {
        logger.info("=== Authorization Metrics Summary ===");
        logger.info("Cache - Hits: {}, Misses: {}, Errors: {}, Hit Ratio: {:.2f}%", 
                   cacheHits.get(), cacheMisses.get(), cacheErrors.get(), getCacheHitRatio());
        logger.info("Database - Queries: {}, Errors: {}, Fallbacks: {}, Error Ratio: {:.2f}%", 
                   databaseQueries.get(), databaseErrors.get(), databaseFallbacks.get(), getDatabaseErrorRatio());
        logger.info("Performance - Total Requests: {}, Average Response Time: {}ms", 
                   totalRequests.get(), averageResponseTime.get());
        logger.info("Authorization Data - Empty Contexts: {}, Read-only Users: {}", 
                   emptyAuthorizationContexts.get(), readOnlyUsers.get());
        logger.info("JWT - Generated: {}, Errors: {}, Average Size: {} bytes", 
                   jwtGenerationCount.get(), jwtGenerationErrors.get(), averageJwtSize.get());
        logger.info("=====================================");
    }

    /**
     * Resets all metrics (useful for testing)
     */
    public void resetMetrics() {
        cacheHits.set(0);
        cacheMisses.set(0);
        cacheErrors.set(0);
        databaseQueries.set(0);
        databaseErrors.set(0);
        databaseFallbacks.set(0);
        totalRequests.set(0);
        totalResponseTime.set(0);
        averageResponseTime.set(0L);
        emptyAuthorizationContexts.set(0);
        readOnlyUsers.set(0);
        jwtGenerationCount.set(0);
        jwtGenerationErrors.set(0);
        averageJwtSize.set(0L);
        
        logger.info("Authorization metrics reset");
    }

    // Getters for metrics (useful for monitoring endpoints)
    
    public long getCacheHits() { return cacheHits.get(); }
    public long getCacheMisses() { return cacheMisses.get(); }
    public long getCacheErrors() { return cacheErrors.get(); }
    public long getDatabaseQueries() { return databaseQueries.get(); }
    public long getDatabaseErrors() { return databaseErrors.get(); }
    public long getDatabaseFallbacks() { return databaseFallbacks.get(); }
    public long getTotalRequests() { return totalRequests.get(); }
    public long getAverageResponseTime() { return averageResponseTime.get(); }
    public long getEmptyAuthorizationContexts() { return emptyAuthorizationContexts.get(); }
    public long getReadOnlyUsers() { return readOnlyUsers.get(); }
    public long getJwtGenerationCount() { return jwtGenerationCount.get(); }
    public long getJwtGenerationErrors() { return jwtGenerationErrors.get(); }
    public long getAverageJwtSize() { return averageJwtSize.get(); }
}