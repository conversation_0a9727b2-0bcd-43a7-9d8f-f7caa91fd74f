/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.authorizer.request;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.nimbusds.oauth2.sdk.ParseException;
import com.okta.jwt.JoseException;

@Service
public class GenerateJWT {

	private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

	@Autowired
	IdmTokenValidations tokenValidation;
	

	/**
	 * Parse request header Token values and generates JWT token
	 *
	 * @param request
	 * @return
	 * @throws ParseException
	 * @throws JoseException
	 * @throws Exception
	 */
	public Map<String, String> generateJwtToken(Map<String, String> request)
			throws ParseException, JoseException, Exception {
		LOGGER.debug("generateJwtToken start");
		// Read TokenNames from requestHeaders
		Map<String, String> headersMap = getHostNameAndAccessTokenHeader(request);

		// Get User claims object from Request Headers
		// Check in Redis for claims if exists then use those details otherwise get the
		// details from okta
		Map<String, Object> tokenMap = tokenValidation.getTokenUserClaims(headersMap);
		LOGGER.debug("generateJwtToken-tokenMap created.");

		// Build Claims Object to generate JWT token
		Map<String, Object> claims = new HashMap<String, Object>();
		claims.put(Constants.userMap, tokenMap.get(Constants.userMap));
		LOGGER.debug("generateJwtToken-claims map created ");

//		// Generate JWT token
		String jwtToken = (String) tokenMap.get(Constants.jwtTokenMap);
//		JWTUtils util = new JWTUtils();
//		String jwtToken = util.generateJwt(claims);
//		LOGGER.debug("generateJwtToken-jwtToken - " + jwtToken);

		// Create ResultMap to return
		Map<String, String> resultMap = new HashMap<String, String>();
		resultMap.put(Constants.jwtTokenMap, jwtToken);
		resultMap.put(Constants.tenantIdentiferMap, tokenMap.get(Constants.tenantIdentiferMap).toString());
		resultMap.put(Constants.hostIdentifier, headersMap.get(Constants.hostIdentifier));

		//LOGGER.debug("generateJwtToken : resultMap -" + resultMap.toString());
		LOGGER.debug("generateJwtToken end ");
		return resultMap;
	}

	/**
	 * PrintHeaders and return required tokenNames in hashMap
	 * 
	 * @param request
	 * @return
	 */

	public Map<String, String> getHostNameAndAccessTokenHeader(Map<String, String> request) {
		Map<String, String> map = new HashMap<String, String>();

		if (request.isEmpty()) {
			LOGGER.debug("header is null");
			return map;
		}

		for (Map.Entry<String, String> entry : request.entrySet()) {
			if(entry.getKey().equals("access_token")) {
				LOGGER.debug("Header Key:" + entry.getKey() + ",Value:****");
			}else {
				LOGGER.debug("Header Key:" + entry.getKey() + ",Value:" + entry.getValue());
			}

			if (entry.getKey().equalsIgnoreCase(TokenNames.hostName)) {
				LOGGER.debug(" getHostNameAndAccessTokenHeader -  equal to server name : " + entry.getKey());

				String hostName = entry.getValue();
				LOGGER.debug(" getHostNameAndAccessTokenHeader - HostName : " + hostName);

				String splitPartsToDot[] = hostName.split("(\\.)|(\\:)|(\\.)");
				String tenantName = splitPartsToDot[0].toLowerCase();
				LOGGER.debug(" getHostNameAndAccessTokenHeader - tenantName : " + tenantName);

				map.put(TokenNames.hostName, tenantName); // host_name
				map.put(Constants.hostIdentifier, hostName); // hostIdentifier

			} else if (entry.getKey().equalsIgnoreCase(TokenNames.accessToken)) {
				LOGGER.debug(" getHostNameAndAccessTokenHeader -  accessToken : " + entry.getKey());
				map.put(TokenNames.accessToken, entry.getValue()); // access_token
			} else if (entry.getKey().equalsIgnoreCase(TokenNames.sessionToken)) {
				LOGGER.debug(" getHostNameAndAccessTokenHeader -  sessionToken : " + entry.getKey());
				map.put(TokenNames.sessionToken, entry.getValue());// session_token
			}

		}
		return map;
	}

}
