/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.authorizer.request;

import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.security.Key;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Security;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Date;
import java.util.Map;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeCo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import com.recvueoktatoken.authorizer.monitoring.AuthorizationMetrics;

@Component
public class JWTUtils {

	private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
	private static PrivateKey priv = null;

	@Autowired(required = false)
	private AuthorizationMetrics authorizationMetrics;

	/**
	 *
	 * @param id
	 * @param issuer
	 * @param subject
	 * @param ttlMillis
	 * @return
	 */
	public String createJWT(String id, String issuer, String subject, long ttlMillis) {

		// The JWT signature algorithm we will be using to sign the token
		SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;

		long nowMillis = System.currentTimeMillis();
		Date now = new Date(nowMillis);

		// We will sign our JWT with our ApiKey secret
		// byte[] apiKeySecretBytes =
		// DatatypeConverter.parseBase64Binary(apiKey.getSecret());
		byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary("secret");
		Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());

		// Let's set the JWT Claims
		JwtBuilder builder = Jwts.builder().setId(id).setIssuedAt(now).setSubject(subject).setIssuer(issuer)
				.signWith(signatureAlgorithm, signingKey);

		// if it has been specified, let's add the expiration
		if (ttlMillis >= 0) {
			long expMillis = nowMillis + ttlMillis;
			Date exp = new Date(expMillis);
			builder.setExpiration(exp);
		}

		// Builds the JWT and serializes it to a compact, URL-safe string
		return builder.compact();
	}

	/**
	 * generates JWT token with given HashMap parameter value
	 * 
	 * @param claims
	 * @return
	 * @throws java.security.NoSuchAlgorithmException
	 * @throws java.security.spec.InvalidKeySpecException
	 * @throws java.io.IOException
	 */
	public String generateJwt(Map<String, Object> claims)
			throws NoSuchAlgorithmException, InvalidKeySpecException, IOException {
		
		// Log authorization claims for debugging
		logAuthorizationClaims(claims);
		
		// Validate authorization claims structure
		validateAuthorizationClaims(claims);
		
		if (priv == null) {
			Security.addProvider(new BouncyCastleProvider());
			KeyFactory factory = KeyFactory.getInstance("RSA");
			// PrivateKey
			priv = generatePrivateKey(factory, Constants.KEY_FILES_PATH + Constants.privateKeyFileName);
			LOGGER.debug("generateJwt -PrivateKey created");
		}
		
		try {
			String jws = Jwts.builder().setClaims(claims).signWith(SignatureAlgorithm.RS256, priv).compact();

			LOGGER.debug("generateJwt - JWT token generated successfully with {} claims", claims.size());
			if (LOGGER.isTraceEnabled()) {
				LOGGER.trace("generateJwt - jws : " + jws);
			}

			// Record JWT generation metrics
			if (authorizationMetrics != null) {
				authorizationMetrics.recordJwtGeneration(jws.length());
			}

			return jws;
			
		} catch (Exception e) {
			if (authorizationMetrics != null) {
				authorizationMetrics.recordJwtGenerationError();
			}
			throw e;
		}
	}

	/**
	 * Reads privatekey File and generates keyFactor
	 * 
	 * @param factory
	 * @param filename
	 * @return
	 * @throws InvalidKeySpecException
	 * @throws FileNotFoundException
	 * @throws IOException
	 */
	private static PrivateKey generatePrivateKey(KeyFactory factory, String filename)
			throws InvalidKeySpecException, FileNotFoundException, IOException {
		PemFile pemFile = new PemFile(filename);
		byte[] content = pemFile.getPemObject().getContent();
		PKCS8EncodedKeySpec privKeySpec = new PKCS8EncodedKeySpec(content);
		return factory.generatePrivate(privKeySpec);
	}

	/**
	 * Logs authorization claims for debugging purposes
	 * 
	 * @param claims The claims map to log
	 */
	private void logAuthorizationClaims(Map<String, Object> claims) {
		if (claims == null || !LOGGER.isDebugEnabled()) {
			return;
		}

		try {
			// Log authorization-specific claims
			if (claims.containsKey(Constants.rolesMap)) {
				Object roles = claims.get(Constants.rolesMap);
				if (roles instanceof java.util.List) {
					@SuppressWarnings("unchecked")
					java.util.List<String> rolesList = (java.util.List<String>) roles;
					LOGGER.debug("JWT generation - Roles claim: {} roles", rolesList.size());
					if (LOGGER.isTraceEnabled()) {
						LOGGER.trace("JWT generation - Roles: {}", rolesList);
					}
				}
			}

			if (claims.containsKey(Constants.permissionsMap)) {
				Object permissions = claims.get(Constants.permissionsMap);
				if (permissions instanceof java.util.List) {
					@SuppressWarnings("unchecked")
					java.util.List<String> permissionsList = (java.util.List<String>) permissions;
					LOGGER.debug("JWT generation - Permissions claim: {} permissions", permissionsList.size());
					if (LOGGER.isTraceEnabled()) {
						LOGGER.trace("JWT generation - Permissions: {}", permissionsList);
					}
				}
			}

			if (claims.containsKey(Constants.businessUnitsMap)) {
				Object businessUnits = claims.get(Constants.businessUnitsMap);
				if (businessUnits instanceof java.util.List) {
					@SuppressWarnings("unchecked")
					java.util.List<?> businessUnitsList = (java.util.List<?>) businessUnits;
					LOGGER.debug("JWT generation - Business Units claim: {} business units", businessUnitsList.size());
					if (LOGGER.isTraceEnabled()) {
						LOGGER.trace("JWT generation - Business Units: {}", businessUnitsList);
					}
				}
			}

			if (claims.containsKey(Constants.readOnlyAccessMap)) {
				Object readOnlyAccess = claims.get(Constants.readOnlyAccessMap);
				LOGGER.debug("JWT generation - Read-only Access claim: {}", readOnlyAccess);
			}

			// Log standard claims
			if (claims.containsKey(Constants.userMap)) {
				LOGGER.debug("JWT generation - User claim present");
			}

			if (claims.containsKey(Constants.tenantIdentiferMap)) {
				LOGGER.debug("JWT generation - Tenant identifier claim: {}", claims.get(Constants.tenantIdentiferMap));
			}

		} catch (Exception e) {
			LOGGER.warn("Error logging authorization claims: {}", e.getMessage());
		}
	}

	/**
	 * Validates authorization claims structure to ensure JWT compatibility
	 * 
	 * @param claims The claims map to validate
	 */
	private void validateAuthorizationClaims(Map<String, Object> claims) {
		if (claims == null) {
			return;
		}

		try {
			// Validate roles claim
			if (claims.containsKey(Constants.rolesMap)) {
				Object roles = claims.get(Constants.rolesMap);
				if (roles != null && !(roles instanceof java.util.List)) {
					LOGGER.warn("JWT generation - Roles claim is not a List: {}", roles.getClass().getSimpleName());
				} else if (roles instanceof java.util.List) {
					@SuppressWarnings("unchecked")
					java.util.List<String> rolesList = (java.util.List<String>) roles;
					if (rolesList.size() > 50) { // Reasonable limit
						LOGGER.warn("JWT generation - Large number of roles ({}), may cause token size issues", rolesList.size());
					}
				}
			}

			// Validate permissions claim
			if (claims.containsKey(Constants.permissionsMap)) {
				Object permissions = claims.get(Constants.permissionsMap);
				if (permissions != null && !(permissions instanceof java.util.List)) {
					LOGGER.warn("JWT generation - Permissions claim is not a List: {}", permissions.getClass().getSimpleName());
				} else if (permissions instanceof java.util.List) {
					@SuppressWarnings("unchecked")
					java.util.List<String> permissionsList = (java.util.List<String>) permissions;
					if (permissionsList.size() > 100) { // Reasonable limit
						LOGGER.warn("JWT generation - Large number of permissions ({}), may cause token size issues", permissionsList.size());
					}
				}
			}

			// Validate business units claim
			if (claims.containsKey(Constants.businessUnitsMap)) {
				Object businessUnits = claims.get(Constants.businessUnitsMap);
				if (businessUnits != null && !(businessUnits instanceof java.util.List)) {
					LOGGER.warn("JWT generation - Business Units claim is not a List: {}", businessUnits.getClass().getSimpleName());
				} else if (businessUnits instanceof java.util.List) {
					@SuppressWarnings("unchecked")
					java.util.List<?> businessUnitsList = (java.util.List<?>) businessUnits;
					if (businessUnitsList.size() > 20) { // Reasonable limit
						LOGGER.warn("JWT generation - Large number of business units ({}), may cause token size issues", businessUnitsList.size());
					}
				}
			}

			// Validate read-only access claim
			if (claims.containsKey(Constants.readOnlyAccessMap)) {
				Object readOnlyAccess = claims.get(Constants.readOnlyAccessMap);
				if (readOnlyAccess != null && !(readOnlyAccess instanceof Boolean)) {
					LOGGER.warn("JWT generation - Read-only Access claim is not a Boolean: {}", readOnlyAccess.getClass().getSimpleName());
				}
			}

			// Check overall token size estimate
			int estimatedClaimsCount = claims.size();
			if (estimatedClaimsCount > 20) {
				LOGGER.warn("JWT generation - Large number of claims ({}), may cause token size issues", estimatedClaimsCount);
			}

		} catch (Exception e) {
			LOGGER.warn("Error validating authorization claims: {}", e.getMessage());
		}
	}
}
