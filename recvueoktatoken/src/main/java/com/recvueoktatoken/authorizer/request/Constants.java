/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.authorizer.request;


public class Constants {

    public static String jwtTokenMap="jwtToken";
    public static String userMap = "user";
    public static String tenantIdentiferMap = "tenantIdentifier";
    public static String GROUP_SEPARATOR = "@";
    public static String SETTINGS_FILES_PATH = "";
    //Local test path - "src/main/resources/"
    //server path - ""
    public static String idmSettingsFileName = "IdmSettings.json";
    public static String tenantIdentifierFileName = "TenantIdentifier.json";
    public static String privateKeyFileName = "rsatest.priv.pem";
    public static String KEY_FILES_PATH = "rsa-sample/";
    //Local test path - "src/main/resources/rsa-sample/"
    //server path - "rsa-sample/"
    public static String hostIdentifier ="hostIdentifier";
    public static String tenantId = "tenantId";
    public static String tps = "tps";
    
    // Authorization claim names
    public static String rolesMap = "roles";
    public static String permissionsMap = "permissions";
    public static String businessUnitsMap = "businessUnits";
    public static String readOnlyAccessMap = "readOnlyAccess";
}
