/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.recvueoktatoken.authorizer.request;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.MalformedURLException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.simple.parser.JSONParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.nimbusds.oauth2.sdk.ParseException;
import com.okta.jwt.JoseException;
import com.okta.jwt.Jwt;
import com.okta.jwt.JwtHelper;
import com.okta.jwt.JwtVerifier;
import com.recvueoktatoken.authorizer.model.AuthorizationContext;
import com.recvueoktatoken.authorizer.model.UserToken;
import com.recvueoktatoken.authorizer.service.AuthorizationService;

@Component
public class IdmTokenValidations {

	private static String orgUrl;
	private static String clientId;
	private final String contentType = "content-type";
	private final String applicationJson = "application/json";
	private static String issuerUrl;
	private static String idmApiToken;
	public static org.json.simple.JSONObject tokensMap;

	private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

	@Autowired
	// @Qualifier("redisTokenTemplate")
	RedisTemplate<String, UserToken> redisTokenTemplate;

	@Autowired
	private AuthorizationService authorizationService;

	static ObjectMapper objMapper = new ObjectMapper();
	private StringBuilder profileNames = new StringBuilder("|");
	static {
		if (tokensMap == null) {
			try {
				JSONParser parser = new JSONParser();

				Resource resource = new ClassPathResource(
						Constants.SETTINGS_FILES_PATH + Constants.tenantIdentifierFileName);
				byte[] binaryData = FileCopyUtils.copyToByteArray(resource.getInputStream());
				String strJson = new String(binaryData, StandardCharsets.UTF_8);

				org.json.simple.JSONObject a;

				a = (org.json.simple.JSONObject) parser.parse(strJson);
				tokensMap = a;
			} catch (org.json.simple.parser.ParseException ex) {
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				ex.printStackTrace(pw);
				// LOGGER.error("getTokenUserClaims GetCache:" + sw.toString());
			} catch (IOException ex) {
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				ex.printStackTrace(pw);
				// LOGGER.error("getTokenUserClaims GetCache:" + sw.toString());
			}

		}
	}

	/**
	 * Returns Users claims Object Map by parsing Provided Request header access
	 * token value
	 *
	 * @param headersMap
	 * @return
	 * @throws IOException
	 * @throws ParseException
	 * @throws JoseException
	 * @throws Exception
	 */
	public Map<String, Object> getTokenUserClaims(Map<String, String> headersMap) throws IOException, Exception {
		// Read IDM settings
		idmUrlsReadFromFile();

		// get user details from access token
		if (headersMap.containsKey(TokenNames.accessToken)) {

			Map<String, Object> calims = new HashMap<String, Object>();
			String accessToken = headerMapContainsKey(headersMap, TokenNames.accessToken);
			String serverName = headerMapContainsKey(headersMap, TokenNames.hostName);

			String payload = accessToken.split("\\.")[1];
			String payloadValue = new String(Base64.decodeBase64(payload));
			com.fasterxml.jackson.databind.JsonNode actualObj = objMapper.readTree(payloadValue);
			LOGGER.debug("EXP: " + actualObj.get("exp").asLong());

			try {
				long currentts = (new Date()).getTime() / 1000;
				if ( currentts > actualObj.get("exp").asLong()){
					throw new ForbiddenException("User is not authorized to access this resource with an explicit deny");
				}
				// Check for cached access token
				UserToken user = redisTokenTemplate.opsForValue().get("jwttoken:"+accessToken);

				if (user != null && (currentts < user.getExpiryTime())) {
					LOGGER.info("User from cache");
					if (!user.getProfileNames().contains("|" + serverName + "|")) {
						throw new Exception("User has no access to this instance. Access Denied.");
					}

					ClaimsObject claimsObj = new ClaimsObject();
					claimsObj.setUsername(user.getLogin());
					claimsObj.setFirstName(user.getFirstName());
					claimsObj.setLastName(user.getLastName());
					claimsObj.setEmail(user.getEmail());

					calims.put(Constants.userMap, claimsObj);
					calims.put(Constants.tenantIdentiferMap, getTenantIdentifier(serverName));
					calims.put(Constants.jwtTokenMap, user.getJwtToken());
					
					// Add authorization data from cached user token
					addAuthorizationDataToClaims(calims, user);
					
					return calims;
				}
			}catch (ForbiddenException ex) {
				throw new ForbiddenException(ex.getMessage());
			} catch (RedisConnectionFailureException ex) {
				LOGGER.error("getTokenUserClaims GetCache:" + ex.getMostSpecificCause().toString());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				ex.printStackTrace(pw);
				LOGGER.error("getTokenUserClaims GetCache:" + sw.toString());
				throw new InternalServerException("Internal server error");
			} catch (Exception | Error ex) {
				LOGGER.error("getTokenUserClaims GetCache:" + ex.getMessage());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				ex.printStackTrace(pw);
				LOGGER.error("getTokenUserClaims GetCache:" + sw.toString());
				throw new Exception("Unauthorized access");
			}
//			calims = getAccessTokenUser(headerMapContainsKey(headersMap, TokenNames.accessToken),
//					headerMapContainsKey(headersMap, TokenNames.hostName));
			calims = getAccessTokenUser(accessToken, serverName);
			ClaimsObject claimsObj = (ClaimsObject) calims.get(Constants.userMap);
			
			// Retrieve authorization context for the user
			AuthorizationContext authContext = getAuthorizationContextForUser(claimsObj.getUsername());
			
			// Add authorization data to claims for JWT generation
			addAuthorizationDataToClaims(calims, authContext);
			
			try {
				// Generate JWT token with authorization data
				JWTUtils util = new JWTUtils();
				String jwtToken = util.generateJwt(calims);
				LOGGER.debug("generateJwtToken-jwtToken - " + jwtToken);
				calims.put(Constants.jwtTokenMap, jwtToken);
				
				// Create UserToken with authorization data
				UserToken user = UserToken.fromAuthorizationContext(authContext, 
						claimsObj.getUsername(), claimsObj.getFirstName(), claimsObj.getLastName(), 
						claimsObj.getEmail(), jwtToken, actualObj.get("exp").asLong(), serverName);
				
				// Set additional fields for backward compatibility
				user.setProfileNames(profileNames.toString());

				LOGGER.info("User caching with authorization data");
				redisTokenTemplate.opsForValue().set("jwttoken:"+accessToken, user, 58, TimeUnit.MINUTES);
				LOGGER.debug("User cached with authorization context");

			} catch (RedisConnectionFailureException ex) {
				LOGGER.error("getTokenUserClaims SetCache:" + ex.getMostSpecificCause().toString());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				ex.printStackTrace(pw);
				LOGGER.error("getTokenUserClaims SetCache:" + sw.toString());
				throw new InternalServerException("Internal server error");
			} catch (Exception | Error ex) {
				LOGGER.error("getTokenUserClaims SetCache:" + ex.getMessage());
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				ex.printStackTrace(pw);
				LOGGER.error("getTokenUserClaims SetCache:" + sw.toString());
				throw new Exception("Unauthorized access");
			}
			return calims;
		} else if (headersMap.containsKey(TokenNames.sessionToken)) {
			// get user details from session token
			return getSessionTokenUser(headerMapContainsKey(headersMap, TokenNames.sessionToken),
					headerMapContainsKey(headersMap, TokenNames.hostName));
		} else {
			// No valid token
			throw new Exception(" No valid token found in request header to generate JWT token.");
		}

	}

	/**
	 * Read IDM settings from file
	 *
	 * @throws MalformedURLException
	 * @throws IOException
	 * @throws org.json.simple.parser.ParseException
	 */
	private void idmUrlsReadFromFile() throws Exception {
		LOGGER.debug("idmUrlsReadFromFile - Start");
		try {
			if (null == clientId || clientId.isEmpty()) {
				JSONParser parser = new JSONParser();

				Resource resource = new ClassPathResource(Constants.idmSettingsFileName);
				byte[] binaryData = FileCopyUtils.copyToByteArray(resource.getInputStream());
				String strJson = new String(binaryData, StandardCharsets.UTF_8);

				org.json.simple.JSONObject idmSettings;
				idmSettings = (org.json.simple.JSONObject) parser.parse(strJson);// new FileReader(strJson));

				clientId = (String) idmSettings.get("clientId");
				idmApiToken = (String) idmSettings.get("apiToken");
				issuerUrl = (String) idmSettings.get("issuerUrl");
				orgUrl = (String) idmSettings.get("orgUrl");
			} else {
				LOGGER.debug("IdmSettings already initialized");
			}
			LOGGER.debug("idmUrlsReadFromFile - End");
		} catch (IOException ex) {
			LOGGER.error("idmUrlsReadFromFile -IOException :  " + ex);
			throw new Exception(" Exception while reading IDM setting  file - " + ex);
		} catch (org.json.simple.parser.ParseException ex) {
			LOGGER.error("idmUrlsReadFromFile -ParseException :  " + ex);
			throw new Exception(" Exception while reading IDM setting  file - " + ex);
		}

	}

	/**
	 * This method checks for accessToken and HostName header keys
	 *
	 * @param request
	 * @param tokenName
	 * @return
	 */
	private String headerMapContainsKey(Map<String, String> request, String tokenName) throws Exception {
		if (request.containsKey(tokenName)) {
			LOGGER.debug("requestHeader contains :" + tokenName);
			return request.get(tokenName);
		} else {
			LOGGER.error("requestHeader does not contains :" + tokenName);
			throw new Exception(tokenName + "Request header not found.");
		}

	}

	/**
	 * Get user details from accessToken and Returns MAP object which has user
	 * details
	 *
	 * @param accessToken
	 * @param serverName
	 * @return
	 * @throws Exception
	 */
	public Map<String, Object> getAccessTokenUser(String accessToken, String serverName) throws Exception {
		try {

			LOGGER.debug(" getAccessTokenUser - accessToken : " + accessToken);
			String accessTokenUrl = orgUrl + "/oauth2/v1/userinfo";
			LOGGER.debug(" getAccessTokenUser - url : " + accessTokenUrl);

			HttpResponse<JsonNode> response = Unirest.get(accessTokenUrl)
					.header("authorization", "Bearer " + accessToken).asJson();
			int status = response.getStatus();
			LOGGER.debug("getAccessTokenUser - response Status %s and Body %s: ", status,
					response.getBody().toString());
			if (status == 200) {
				JSONArray array = response.getBody().getArray();
				JSONObject jsonObject = array.getJSONObject(0);
				String idmUserId = jsonObject.getString("sub");
				return buildClaimsMap(idmUserId, serverName);
			} else {
				throw new Exception(" Access token failed with status code :" + status);
			}

		} catch (Exception e) {
			LOGGER.error("getAccessTokenUser - Exception : " + e.toString());
			throw new Exception(" Error while getting user details from Access token - " + e);
		}
	}

	/**
	 *
	 * @param body
	 * @param serverName
	 * @return
	 * @throws Exception
	 */
	private Map<String, Object> buildClaimsMap(String idmUserId, String serverName) throws Exception {
		ClaimsObject obj = buildCailmsObject(idmUserId, serverName);
		Map<String, Object> calims = new HashMap<String, Object>();
		calims.put(Constants.userMap, obj);
		calims.put(Constants.tenantIdentiferMap, getTenantIdentifier(serverName));
		return calims;

	}

	/**
	 *
	 * @param body
	 * @param serverName
	 * @return
	 * @throws UnirestException
	 * @throws Exception
	 */
	private ClaimsObject buildCailmsObject(String idmUserId, String serverName) throws UnirestException, Exception {
		ClaimsObject claims = new ClaimsObject();
		LOGGER.debug("buildCailmsObject - For user Id : " + idmUserId);
		if (isTenantValidated(idmUserId, serverName)) {
			claims = getOktaUserDtlsByUserId(idmUserId);
			// String tenantIdentifier = getTenantIdentifier(serverName);
			// claims.setTenantIdentifier(tenantIdentifier);
			return claims;
		} else {
			throw new Exception("User has no access to this instance.Access Denied.");
		}
	}

	/**
	 * Method to validate the User has access to provided tenantName
	 *
	 * @param userId
	 * @param serverName
	 * @return
	 * @throws UnirestException
	 * @throws Exception
	 */
	private boolean isTenantValidated(String userId, String serverName) throws UnirestException, Exception {
		LOGGER.debug("isTenantValidated - serverName : " + serverName);
		HttpResponse<JsonNode> userGroupResponse = null;
		boolean userGroupValidated = false;
		try {
			userGroupResponse = Unirest.get(orgUrl + "/api/v1/users/" + userId + "/groups").header("user-agent", null)
					.header("authorization", "SSWS " + idmApiToken).header("connection", "close")
					.header("accept", "application/json").asJson();
			LOGGER.debug("isTenantValidated - userGroupResponse - Body" + userGroupResponse.getBody().toString());
			int status = userGroupResponse.getStatus();
			LOGGER.debug("isTenantValidated - userGroupResponse - Status : " + status);
			if (status == 200) {
				JSONArray array = userGroupResponse.getBody().getArray();
				for (int i = 0; i < array.length(); i++) {
					JSONObject jsonObject = array.getJSONObject(i);
					JSONObject profileObject = jsonObject.getJSONObject("profile");
					String groupName = profileObject.getString("name");
					String[] result = groupName.split(Constants.GROUP_SEPARATOR);
					LOGGER.debug(
							" isTenantValidated - groupName :" + groupName + " - result.length - " + result.length);
					if (result.length != 2) {
						continue;
					}
					if (result[0].equals(serverName)) {
						LOGGER.debug("isTenantValidated - serverName equlas to group name : " + serverName);
						userGroupValidated = true;
						break;
					}
				}

				for (int i = 0; i < array.length(); i++) {
					JSONObject jsonObject = array.getJSONObject(i);
					JSONObject profileObject = jsonObject.getJSONObject("profile");
					String groupName = profileObject.getString("name");
					String[] result = groupName.split(Constants.GROUP_SEPARATOR);
					LOGGER.debug(
							" isTenantValidated - groupName :" + groupName + " - result.length - " + result.length);
					if (result.length != 2) {
						continue;
					}
					profileNames.append(result[0]).append("|");
				}

			} else {
				userGroupValidated = false;
			}
			return userGroupValidated;
		} catch (UnirestException e) {
			LOGGER.error("isTenantValidated Exception :" + e);
			return userGroupValidated;
		}

	}

	/**
	 * OKTA user details by userId to build ClaimsObject
	 *
	 * @param userId
	 * @return
	 * @throws UnirestException
	 * @throws Exception
	 */
	private ClaimsObject getOktaUserDtlsByUserId(String userId) throws UnirestException, Exception {
		LOGGER.debug(" getOktaUserDtlsByUserId - userId : " + userId);
		HttpResponse<JsonNode> userResponse = null;
		ClaimsObject claims = new ClaimsObject();

		try {
			userResponse = Unirest.get(orgUrl + "/api/v1/users/" + userId).header("user-agent", null)
					.header("authorization", "SSWS " + idmApiToken).header("connection", "close")
					.header("accept", "application/json").asJson();

			int status = userResponse.getStatus();
			LOGGER.debug("getOktaUserDtlsByUserId - userResponse - Status %s and Body %s", status,
					userResponse.getBody().toString());
			if (status == 200) {
				JSONArray array = userResponse.getBody().getArray();
				for (int i = 0; i < array.length(); i++) {
					JSONObject jsonObject = array.getJSONObject(i);
					JSONObject profileObject = jsonObject.getJSONObject("profile");
					LOGGER.debug("getOktaUserDtlsByUserId - Profile :" + profileObject);
					LOGGER.debug("getOktaUserDtlsByUserId -user Name : " + profileObject.getString("login"));
					claims.setUsername(profileObject.getString("login"));
					claims.setFirstName(profileObject.getString("firstName"));
					claims.setLastName(profileObject.getString("lastName"));
					claims.setEmail(profileObject.getString("email"));
				}
			} else {
				throw new Exception("User Details API errored out with status :" + status);
			}

		} catch (UnirestException e) {
			LOGGER.error("isTenantValidated Exception :" + e);
			throw new Exception(" User Details API errored out with status :" + e);
		}
		return claims;
	}

	/**
	 * Read TenantIdentifier File and get the DB names of the provided tenantName
	 *
	 * @param serverName
	 * @return
	 * @throws IOException
	 * @throws org.json.simple.parser.ParseException
	 */
	private String getTenantIdentifier(String serverName) throws Exception {
		try {
			LOGGER.debug("getTenantIdentifier - file read - server name : " + serverName);
			String tenantIdentifier;
			if (tokensMap == null) {
				JSONParser parser = new JSONParser();

				Resource resource = new ClassPathResource(
						Constants.SETTINGS_FILES_PATH + Constants.tenantIdentifierFileName);
				byte[] binaryData = FileCopyUtils.copyToByteArray(resource.getInputStream());
				String strJson = new String(binaryData, StandardCharsets.UTF_8);

				org.json.simple.JSONObject a = (org.json.simple.JSONObject) parser.parse(strJson);// new
																									// FileReader(Constants.SETTINGS_FILES_PATH
																									// +
																									// Constants.tenantIdentifierFileName));
				// tenantIdentifier = (String) a.get(serverName);
				tenantIdentifier = (String) ((HashMap<String, Object>) a.get(serverName)).get(Constants.tenantId);
				tokensMap = a;
			} else {
				// tenantIdentifier = (String) tokensMap.get(serverName);
				tenantIdentifier = (String) ((HashMap<String, Object>) tokensMap.get(serverName))
						.get(Constants.tenantId);
			}
			LOGGER.debug("getTenantIdentifier - tenantIdentifier : " + tenantIdentifier);
			return tenantIdentifier;
		} catch (org.json.simple.parser.ParseException ex) {
			LOGGER.error("getTenantIdentifier - Exception : " + ex);
			throw new Exception("TenantIdentier not found for tenantName - " + serverName + " : " + ex);
		}

	}

	/**
	 *
	 * @param sessionToken
	 * @return
	 * @throws Exception
	 */
	public Map<String, Object> getSessionTokenUser(String sessionToken, String serverName) throws Exception {

		try {
			String body = "{\"sessionToken\": \"" + sessionToken + "\"}";
			LOGGER.debug(" getSessionTokenUser.sessionToken : " + body);
			String sessionUrl = orgUrl + "/api/v1/sessions";
			LOGGER.debug(" getSessionTokenUser.sessionUrl -" + sessionUrl);
			HttpResponse<JsonNode> response = Unirest.post(sessionUrl).header(contentType, applicationJson).body(body)
					.asJson();
			LOGGER.debug("getSessionTokenUser.response : - Body -" + response.getBody().toString());
			int status = response.getStatus();
			LOGGER.debug("getSessionTokenUser.response : - Status : " + status);
			if (status == 200) {
				JSONArray array = response.getBody().getArray();
				JSONObject jsonObject = array.getJSONObject(0);
				String idmUserId = jsonObject.getString("userId");
				return buildClaimsMap(idmUserId, serverName);
			} else {
				throw new Exception(" Session token failed with status code :" + status);
			}
		} catch (Exception e) {
			LOGGER.error("getSessionTokenUser.Exception : " + e.toString());
			throw e;
		}

	}

	/**
	 * ************* Below mehtods might be useful *******************
	 */
	/**
	 *
	 * @param idToken
	 * @param cookieNonce
	 * @throws IOException
	 * @throws ParseException
	 * @throws JoseException
	 */
	public void getIdTokenUser(String idToken, String cookieNonce) throws IOException, ParseException, JoseException {
		LOGGER.debug("getIdTokenUser.idToken -" + idToken + ":cookieNonce :" + cookieNonce);
		JwtVerifier jwtVerifier = new JwtHelper().setIssuerUrl(issuerUrl).setAudience(issuerUrl) // defaults to
																									// 'api://default'
				.setClientId(clientId) // optional
				.build();
		LOGGER.debug("getIdTokenUser.jwtVerifier After : ");
		Jwt jwtIdToken = jwtVerifier.decodeIdToken(idToken, cookieNonce);
		Map<?, ?> claims = jwtIdToken.getClaims();
		LOGGER.debug("getIdTokenUser.claims : " + claims);

	}

	/**
	 *
	 * @param accessToken
	 */
	public void getAccessTokenUserUsingIdmLib(String accessToken) {
		try {

			JwtVerifier jwtVerifier = new JwtHelper().setIssuerUrl("https://recvue.okta.com")
					.setAudience("https://recvue.okta.com/").build();

			Jwt jwt = jwtVerifier.decodeAccessToken(accessToken);

			LOGGER.debug(jwt.getTokenValue()); // print the token
			LOGGER.debug("Invalid Key: %s", jwt.getClaims().get("invalidKey")); // an invalid key just returns null
			LOGGER.debug("Groups: %s", jwt.getClaims().get("groups")); // handle an array value
			LOGGER.debug("Expiration time: %s ", jwt.getExpiresAt()); // print the expiration time
		} catch (Exception e) {
			LOGGER.error("AccessTOken String : " + e.toString());
		}
	}

	/**
	 * Retrieves authorization context for a user with fallback handling.
	 * 
	 * @param username The username to retrieve authorization context for
	 * @return AuthorizationContext containing user's authorization data, or empty context on error
	 */
	private AuthorizationContext getAuthorizationContextForUser(String username) {
		try {
			LOGGER.debug("Retrieving authorization context for user: {}", username);
			AuthorizationContext context = authorizationService.getUserAuthorizationContext(username);
			
			if (context != null && !context.isEmpty()) {
				LOGGER.debug("Retrieved authorization context for user: {} - Roles: {}, Permissions: {}, Business Units: {}", 
						   username, context.getRoleNames().size(), context.getPermissionNames().size(), context.getBusinessUnits().size());
			} else {
				LOGGER.debug("No authorization data found for user: {}", username);
			}
			
			return context != null ? context : new AuthorizationContext();
			
		} catch (Exception e) {
			LOGGER.error("Error retrieving authorization context for user: {}, falling back to empty context", username, e);
			// Return empty context to allow system to continue functioning
			return new AuthorizationContext();
		}
	}

	/**
	 * Adds authorization data to claims map for JWT generation.
	 * 
	 * @param claims The claims map to add authorization data to
	 * @param authContext The authorization context containing user's authorization data
	 */
	private void addAuthorizationDataToClaims(Map<String, Object> claims, AuthorizationContext authContext) {
		if (claims == null || authContext == null) {
			return;
		}

		try {
			// Add roles claim
			if (authContext.getRoleNames() != null && !authContext.getRoleNames().isEmpty()) {
				claims.put("roles", authContext.getRoleNames());
				LOGGER.debug("Added {} roles to JWT claims", authContext.getRoleNames().size());
			} else {
				claims.put("roles", new java.util.ArrayList<String>());
			}

			// Add permissions claim
			if (authContext.getPermissionNames() != null && !authContext.getPermissionNames().isEmpty()) {
				claims.put("permissions", authContext.getPermissionNames());
				LOGGER.debug("Added {} permissions to JWT claims", authContext.getPermissionNames().size());
			} else {
				claims.put("permissions", new java.util.ArrayList<String>());
			}

			// Add business units claim
			if (authContext.getBusinessUnits() != null && !authContext.getBusinessUnits().isEmpty()) {
				claims.put("businessUnits", authContext.getBusinessUnits());
				LOGGER.debug("Added {} business units to JWT claims", authContext.getBusinessUnits().size());
			} else {
				claims.put("businessUnits", new java.util.ArrayList<>());
			}

			// Add read-only access claim
			claims.put("readOnlyAccess", authContext.isReadOnlyAccess());
			LOGGER.debug("Added readOnlyAccess flag to JWT claims: {}", authContext.isReadOnlyAccess());

		} catch (Exception e) {
			LOGGER.error("Error adding authorization data to claims", e);
			// Add empty claims to ensure JWT structure is consistent
			claims.put("roles", new java.util.ArrayList<String>());
			claims.put("permissions", new java.util.ArrayList<String>());
			claims.put("businessUnits", new java.util.ArrayList<>());
			claims.put("readOnlyAccess", false);
		}
	}

	/**
	 * Adds authorization data to claims map from cached UserToken.
	 * 
	 * @param claims The claims map to add authorization data to
	 * @param userToken The cached UserToken containing authorization data
	 */
	private void addAuthorizationDataToClaims(Map<String, Object> claims, UserToken userToken) {
		if (claims == null || userToken == null) {
			return;
		}

		try {
			// Convert UserToken to AuthorizationContext and add to claims
			AuthorizationContext authContext = userToken.toAuthorizationContext();
			addAuthorizationDataToClaims(claims, authContext);
			
		} catch (Exception e) {
			LOGGER.error("Error adding authorization data from cached user token to claims", e);
			// Add empty claims to ensure JWT structure is consistent
			claims.put("roles", new java.util.ArrayList<String>());
			claims.put("permissions", new java.util.ArrayList<String>());
			claims.put("businessUnits", new java.util.ArrayList<>());
			claims.put("readOnlyAccess", false);
		}
	}

}
