package com.recvueoktatoken.authorizer.service;

import com.recvueoktatoken.authorizer.model.AuthorizationContext;

/**
 * Service interface for handling user authorization operations.
 * This service manages user roles, permissions, and business unit access information.
 */
public interface AuthorizationService {

    /**
     * Retrieves the complete authorization context for a user.
     * This includes roles, permissions, business units, and read-only access information.
     * The method first checks the cache and falls back to database queries if needed.
     *
     * @param username The username to retrieve authorization context for
     * @return AuthorizationContext containing all authorization information
     * @throws Exception if there's an error retrieving authorization data
     */
    AuthorizationContext getUserAuthorizationContext(String username) throws Exception;

    /**
     * Invalidates the cached authorization context for a specific user.
     * This should be called when user roles, permissions, or business unit access changes.
     *
     * @param username The username whose authorization cache should be invalidated
     */
    void invalidateUserAuthorizationCache(String username);

    /**
     * Checks if a user is authorized to perform a specific action on a resource.
     * This is a convenience method for quick authorization checks.
     *
     * @param username The username to check authorization for
     * @param resource The resource being accessed
     * @param action The action being performed on the resource
     * @return true if the user is authorized, false otherwise
     */
    boolean isUserAuthorizedForResource(String username, String resource, String action);

    /**
     * Retrieves authorization context from cache only, without database fallback.
     * This method is useful for performance-critical scenarios where database
     * queries should be avoided.
     *
     * @param username The username to retrieve cached authorization context for
     * @return AuthorizationContext from cache, or null if not cached
     */
    AuthorizationContext getCachedAuthorizationContext(String username);

    /**
     * Forces a refresh of the authorization context for a user by clearing cache
     * and reloading from the database.
     *
     * @param username The username whose authorization context should be refreshed
     * @return Updated AuthorizationContext
     * @throws Exception if there's an error refreshing authorization data
     */
    AuthorizationContext refreshUserAuthorizationContext(String username) throws Exception;
}