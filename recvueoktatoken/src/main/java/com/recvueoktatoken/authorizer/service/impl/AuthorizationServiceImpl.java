package com.recvueoktatoken.authorizer.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.recvueoktatoken.authorizer.model.AuthorizationContext;
import com.recvueoktatoken.authorizer.model.BusinessUnitInfo;
import com.recvueoktatoken.authorizer.model.UserToken;
import com.recvueoktatoken.authorizer.monitoring.AuthorizationMetrics;
import com.recvueoktatoken.authorizer.service.AuthorizationService;
import com.recvueoktatoken.common.entity.CoreUser;
import com.recvueoktatoken.common.multitenancy.TenantContext;
import com.recvueoktatoken.coreuser.dao.CoreUserDao;

/**
 * Implementation of AuthorizationService that handles user authorization
 * operations.
 * This service manages user roles, permissions, and business unit access
 * information
 * by integrating with the database through CoreUserDao.
 */
@Service
@ConditionalOnProperty(
    prefix = "authorization",
    name = "enabled",
    havingValue = "true",
    matchIfMissing = true
)
@Transactional(readOnly = true)
public class AuthorizationServiceImpl implements AuthorizationService {

    private static final Logger logger = LoggerFactory.getLogger(AuthorizationServiceImpl.class);

    @Autowired
    private CoreUserDao coreUserDao;

    @Autowired
    @Qualifier("redisTokenTemplate")
    private RedisTemplate<String, UserToken> redisTokenTemplate;

    @Autowired
    private AuthorizationMetrics authorizationMetrics;

    private static final String AUTH_CACHE_PREFIX = "auth_context:";
    private static final int CACHE_EXPIRATION_MINUTES = 58;

    @Override
    public AuthorizationContext getUserAuthorizationContext(String username) throws Exception {
        long startTime = System.currentTimeMillis();
        logger.debug("Retrieving authorization context for username: {}", username);

        if (username == null || username.trim().isEmpty()) {
            logger.warn("Username is null or empty");
            throw new IllegalArgumentException("Username cannot be null or empty");
        }

        try {
            // First try to get cached context
            AuthorizationContext cachedContext = getCachedAuthorizationContext(username);
            if (cachedContext != null) {
                authorizationMetrics.recordCacheHit();
                logger.debug("Retrieved authorization context from cache for username: {}", username);
                
                // Record metrics for cached response
                long responseTime = System.currentTimeMillis() - startTime;
                authorizationMetrics.recordResponseTime(responseTime);
                
                if (cachedContext.isEmpty()) {
                    authorizationMetrics.recordEmptyAuthorizationContext();
                }
                if (cachedContext.isReadOnlyAccess()) {
                    authorizationMetrics.recordReadOnlyUser();
                }
                
                return cachedContext;
            }

            authorizationMetrics.recordCacheMiss();

            // If not cached, retrieve from database
            AuthorizationContext context = retrieveAuthorizationContextFromDatabase(username);

            // Cache the context for future use
            cacheAuthorizationContext(username, context);

            // Record metrics
            long responseTime = System.currentTimeMillis() - startTime;
            authorizationMetrics.recordResponseTime(responseTime);
            
            if (context.isEmpty()) {
                logger.warn("No authorization data found for username: {}", username);
                authorizationMetrics.recordEmptyAuthorizationContext();
            } else {
                logger.debug(
                        "Retrieved authorization context from database for username: {} - Roles: {}, Permissions: {}, Business Units: {}",
                        username, context.getRoleNames().size(), context.getPermissionNames().size(),
                        context.getBusinessUnits().size());
            }
            
            if (context.isReadOnlyAccess()) {
                authorizationMetrics.recordReadOnlyUser();
            }

            return context;

        } catch (Exception e) {
            logger.error("Error retrieving authorization context for username: {}", username, e);
            authorizationMetrics.recordDatabaseError();

            // Try to get cached context as fallback if database fails
            if (isDatabaseConnectivityIssue(e)) {
                logger.warn("Database connectivity issue detected, attempting to use cached data for username: {}",
                        username);
                AuthorizationContext fallbackContext = getCachedAuthorizationContext(username);
                if (fallbackContext != null) {
                    logger.info("Using cached authorization context as fallback for username: {}", username);
                    authorizationMetrics.recordDatabaseFallback();
                    
                    // Record metrics for fallback response
                    long responseTime = System.currentTimeMillis() - startTime;
                    authorizationMetrics.recordResponseTime(responseTime);
                    
                    return fallbackContext;
                }
            }

            throw new Exception("Failed to retrieve authorization context for user: " + username, e);
        }
    }

    @Override
    public void invalidateUserAuthorizationCache(String username) {
        logger.debug("Invalidating authorization cache for username: {}", username);

        if (username == null || username.trim().isEmpty()) {
            logger.warn("Cannot invalidate cache - username is null or empty");
            return;
        }

        try {
            String cacheKey = buildCacheKey(username);
            Boolean deleted = redisTokenTemplate.delete(cacheKey);
            if (Boolean.TRUE.equals(deleted)) {
                logger.debug("Authorization cache invalidated for username: {}", username);
            } else {
                logger.debug("No cached authorization context found to invalidate for username: {}", username);
            }
        } catch (RedisConnectionFailureException e) {
            logger.error("Redis connection failure while invalidating cache for username: {}", username, e);
            // Don't throw exception for cache invalidation failures
        } catch (Exception e) {
            logger.error("Error invalidating authorization cache for username: {}", username, e);
            // Don't throw exception for cache invalidation failures
        }
    }

    @Override
    public boolean isUserAuthorizedForResource(String username, String resource, String action) {
        logger.debug("Checking authorization for username: {}, resource: {}, action: {}", username, resource, action);

        if (username == null || username.trim().isEmpty()) {
            logger.warn("Username is null or empty for authorization check");
            return false;
        }

        if (resource == null || resource.trim().isEmpty()) {
            logger.warn("Resource is null or empty for authorization check");
            return false;
        }

        if (action == null || action.trim().isEmpty()) {
            logger.warn("Action is null or empty for authorization check");
            return false;
        }

        try {
            AuthorizationContext context = getUserAuthorizationContext(username);

            // Simple permission-based authorization check
            // Format: RESOURCE_ACTION (e.g., USER_CREATE, REPORT_VIEW)
            String requiredPermission = resource.toUpperCase() + "_" + action.toUpperCase();

            boolean isAuthorized = context.hasPermission(requiredPermission);
            logger.debug("Authorization check result for username: {}, permission: {} - {}",
                    username, requiredPermission, isAuthorized ? "GRANTED" : "DENIED");

            return isAuthorized;

        } catch (Exception e) {
            logger.error("Error checking authorization for username: {}, resource: {}, action: {}",
                    username, resource, action, e);
            return false; // Deny access on error
        }
    }

    @Override
    public AuthorizationContext getCachedAuthorizationContext(String username) {
        logger.debug("Attempting to retrieve cached authorization context for username: {}", username);

        if (username == null || username.trim().isEmpty()) {
            return null;
        }

        try {
            String cacheKey = buildCacheKey(username);
            UserToken cachedToken = redisTokenTemplate.opsForValue().get(cacheKey);

            if (cachedToken != null) {
                // Check if the cached token has expired
                long currentTime = System.currentTimeMillis() / 1000;
                if (cachedToken.getExpiryTime() != null && currentTime < cachedToken.getExpiryTime()) {
                    logger.debug("Found valid cached authorization context for username: {}", username);
                    return cachedToken.toAuthorizationContext();
                } else {
                    logger.debug("Cached authorization context expired for username: {}", username);
                    // Remove expired cache entry
                    redisTokenTemplate.delete(cacheKey);
                    return null;
                }
            } else {
                logger.debug("No cached authorization context found for username: {}", username);
                return null;
            }
        } catch (RedisConnectionFailureException e) {
            logger.error("Redis connection failure while retrieving cached context for username: {}", username, e);
            authorizationMetrics.recordCacheError();
            return null;
        } catch (Exception e) {
            logger.error("Error retrieving cached authorization context for username: {}", username, e);
            authorizationMetrics.recordCacheError();
            return null;
        }
    }

    @Override
    public AuthorizationContext refreshUserAuthorizationContext(String username) throws Exception {
        logger.debug("Refreshing authorization context for username: {}", username);

        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("Username cannot be null or empty");
        }

        try {
            // Invalidate cache first
            invalidateUserAuthorizationCache(username);

            // Retrieve fresh data from database
            AuthorizationContext context = retrieveAuthorizationContextFromDatabase(username);

            logger.debug(
                    "Refreshed authorization context for username: {} - Roles: {}, Permissions: {}, Business Units: {}",
                    username, context.getRoleNames().size(), context.getPermissionNames().size(),
                    context.getBusinessUnits().size());

            return context;

        } catch (Exception e) {
            logger.error("Error refreshing authorization context for username: {}", username, e);
            throw new Exception("Failed to refresh authorization context for user: " + username, e);
        }
    }

    /**
     * Retrieves authorization context from database using CoreUserDao.
     * This method aggregates user roles, permissions, and business units.
     * 
     * @param username The username to retrieve authorization context for
     * @return AuthorizationContext containing all authorization information
     * @throws Exception if there's an error retrieving data from database
     */
    private AuthorizationContext retrieveAuthorizationContextFromDatabase(String username) throws Exception {
        logger.debug("Retrieving authorization context from database for username: {}", username);

        try {
            authorizationMetrics.recordDatabaseQuery();
            
            // Retrieve all authorization data using separate DAO methods
            List<String> roleNames = coreUserDao.getUserRoleNames(username);
            List<String> permissionNames = coreUserDao.getUserPermissionNames(username);
            List<BusinessUnitInfo> businessUnits = coreUserDao.getUserBusinessUnits(username);
            boolean isReadOnly = coreUserDao.isReadOnlyUser(username);

            // Handle null results from DAO
            if (roleNames == null) {
                roleNames = new ArrayList<>();
                logger.debug("No roles found for username: {}", username);
            }

            if (permissionNames == null) {
                permissionNames = new ArrayList<>();
                logger.debug("No permissions found for username: {}", username);
            }

            if (businessUnits == null) {
                businessUnits = new ArrayList<>();
                logger.debug("No business units found for username: {}", username);
            }

            // Create authorization context
            AuthorizationContext context = new AuthorizationContext(roleNames, permissionNames, businessUnits,
                    isReadOnly);

            logger.debug(
                    "Successfully retrieved authorization context from database for username: {} - Roles: {}, Permissions: {}, Business Units: {}, Read-only: {}",
                    username, roleNames.size(), permissionNames.size(), businessUnits.size(), isReadOnly);

            return context;

        } catch (Exception e) {
            logger.error("Database error retrieving authorization context for username: {}", username, e);

            // Check if it's a connectivity issue
            if (isDatabaseConnectivityIssue(e)) {
                logger.warn("Database connectivity issue detected for username: {}", username);
                throw new Exception("Database connectivity issue while retrieving authorization context", e);
            }

            throw new Exception("Database error retrieving authorization context for user: " + username, e);
        }
    }

    /**
     * Checks if the exception indicates a database connectivity issue.
     * 
     * @param e The exception to check
     * @return true if it's a connectivity issue, false otherwise
     */
    private boolean isDatabaseConnectivityIssue(Exception e) {
        if (e == null) {
            return false;
        }

        String message = e.getMessage();
        if (message == null) {
            return false;
        }

        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("connection") ||
                lowerMessage.contains("timeout") ||
                lowerMessage.contains("network") ||
                lowerMessage.contains("unreachable") ||
                lowerMessage.contains("refused");
    }

    /**
     * Creates an empty authorization context for fallback scenarios.
     * 
     * @param readOnlyAccess The read-only access flag to set
     * @return Empty AuthorizationContext
     */
    private AuthorizationContext createEmptyAuthorizationContext(boolean readOnlyAccess) {
        AuthorizationContext context = new AuthorizationContext();
        context.setReadOnlyAccess(readOnlyAccess);
        return context;
    }

    /**
     * Validates that the authorization context is properly formed.
     * 
     * @param context  The context to validate
     * @param username The username for logging purposes
     * @return true if valid, false otherwise
     */
    private boolean isValidAuthorizationContext(AuthorizationContext context, String username) {
        if (context == null) {
            logger.warn("Authorization context is null for username: {}", username);
            return false;
        }

        if (context.getRoleNames() == null || context.getPermissionNames() == null
                || context.getBusinessUnits() == null) {
            logger.warn("Authorization context has null collections for username: {}", username);
            return false;
        }

        return true;
    }

    /**
     * Logs authorization context details for debugging purposes.
     * 
     * @param context  The authorization context to log
     * @param username The username for context
     */
    private void logAuthorizationContext(AuthorizationContext context, String username) {
        if (logger.isDebugEnabled() && context != null) {
            logger.debug(
                    "Authorization context for username: {} - Roles: {}, Permissions: {}, Business Units: {}, Read-only: {}, Cache timestamp: {}",
                    username,
                    context.getRoleNames(),
                    context.getPermissionNames(),
                    context.getBusinessUnits().stream().map(bu -> bu.getBusinessUnitName()).toArray(),
                    context.isReadOnlyAccess(),
                    context.getCacheTimestamp());
        }
    }

    /**
     * Caches the authorization context for a user.
     * 
     * @param username The username to cache context for
     * @param context  The authorization context to cache
     */
    private void cacheAuthorizationContext(String username, AuthorizationContext context) {
        if (username == null || username.trim().isEmpty() || context == null) {
            return;
        }

        try {
            String cacheKey = buildCacheKey(username);

            // Create a UserToken with authorization context for caching
            // We'll use dummy values for user info since we're only caching authorization
            // data
            long expiryTime = (System.currentTimeMillis() / 1000) + (CACHE_EXPIRATION_MINUTES * 60);
            UserToken cacheToken = UserToken.fromAuthorizationContext(context, username,
                    null, null, null, null, expiryTime, getCurrentTenant());

            // Cache with expiration
            redisTokenTemplate.opsForValue().set(cacheKey, cacheToken, CACHE_EXPIRATION_MINUTES, TimeUnit.MINUTES);

            logger.debug("Cached authorization context for username: {} with expiry: {}", username, expiryTime);

        } catch (RedisConnectionFailureException e) {
            logger.error("Redis connection failure while caching context for username: {}", username, e);
            // Don't throw exception for cache failures
        } catch (Exception e) {
            logger.error("Error caching authorization context for username: {}", username, e);
            // Don't throw exception for cache failures
        }
    }

    /**
     * Builds the cache key for a user's authorization context.
     * Format: auth_context:{tenant}:{username}
     * 
     * @param username The username
     * @return The cache key
     */
    private String buildCacheKey(String username) {
        String tenant = getCurrentTenant();
        return AUTH_CACHE_PREFIX + (tenant != null ? tenant : "default") + ":" + username;
    }

    /**
     * Gets the current tenant identifier for cache key generation.
     * 
     * @return The current tenant identifier or null if not available
     */
    private String getCurrentTenant() {
        try {
            return TenantContext.getCurrentTenant();
        } catch (Exception e) {
            logger.debug("Could not retrieve current tenant context: {}", e.getMessage());
            return null;
        }
    }
}