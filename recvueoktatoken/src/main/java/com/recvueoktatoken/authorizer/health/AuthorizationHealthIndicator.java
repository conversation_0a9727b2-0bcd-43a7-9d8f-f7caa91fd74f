package com.recvueoktatoken.authorizer.health;

import com.recvueoktatoken.authorizer.config.AuthorizationProperties;
import com.recvueoktatoken.authorizer.service.AuthorizationService;
import com.recvueoktatoken.coreuser.dao.CoreUserDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Health indicator for authorization service functionality
 */
@Component
public class AuthorizationHealthIndicator implements HealthIndicator {

    private static final Logger logger = LoggerFactory.getLogger(AuthorizationHealthIndicator.class);

    private final AuthorizationProperties authorizationProperties;
    private final AuthorizationService authorizationService;
    private final CoreUserDao coreUserDao;
    private final RedisTemplate<String, Object> redisTemplate;

    public AuthorizationHealthIndicator(
            AuthorizationProperties authorizationProperties,
            AuthorizationService authorizationService,
            CoreUserDao coreUserDao,
            RedisTemplate<String, Object> redisTemplate) {
        this.authorizationProperties = authorizationProperties;
        this.authorizationService = authorizationService;
        this.coreUserDao = coreUserDao;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public Health health() {
        try {
            Map<String, Object> details = new HashMap<>();
            boolean overallHealthy = true;

            // Check if authorization enhancement is enabled
            details.put("authorizationEnabled", authorizationProperties.isEnabled());
            
            if (!authorizationProperties.isEnabled()) {
                details.put("status", "Authorization enhancement is disabled");
                return Health.up().withDetails(details).build();
            }

            // Check database connectivity
            boolean databaseHealthy = checkDatabaseHealth();
            details.put("databaseHealthy", databaseHealthy);
            if (!databaseHealthy) {
                overallHealthy = false;
            }

            // Check Redis cache connectivity (if caching is enabled)
            if (authorizationProperties.isCachingEnabled()) {
                boolean cacheHealthy = checkCacheHealth();
                details.put("cacheHealthy", cacheHealthy);
                details.put("cachingEnabled", true);
                if (!cacheHealthy) {
                    overallHealthy = false;
                }
            } else {
                details.put("cachingEnabled", false);
                details.put("cacheHealthy", "N/A - Caching disabled");
            }

            // Check authorization service functionality
            boolean serviceHealthy = checkAuthorizationServiceHealth();
            details.put("serviceHealthy", serviceHealthy);
            if (!serviceHealthy) {
                overallHealthy = false;
            }

            // Add configuration details
            addConfigurationDetails(details);

            // Add performance metrics if available
            addPerformanceMetrics(details);

            if (overallHealthy) {
                return Health.up().withDetails(details).build();
            } else {
                return Health.down().withDetails(details).build();
            }

        } catch (Exception e) {
            logger.error("Error checking authorization service health", e);
            return Health.down()
                    .withException(e)
                    .withDetail("error", "Failed to check authorization service health")
                    .build();
        }
    }

    private boolean checkDatabaseHealth() {
        try {
            // Test database connectivity by attempting a simple query
            // Using a lightweight query that should work regardless of data
            coreUserDao.getUserByUsername("__health_check_user__");
            return true;
        } catch (Exception e) {
            logger.warn("Database health check failed", e);
            return false;
        }
    }

    private boolean checkCacheHealth() {
        try {
            // Test Redis connectivity
            String testKey = "auth_health_check";
            String testValue = "healthy";
            
            redisTemplate.opsForValue().set(testKey, testValue);
            Object retrievedValue = redisTemplate.opsForValue().get(testKey);
            redisTemplate.delete(testKey);
            
            return testValue.equals(retrievedValue);
        } catch (Exception e) {
            logger.warn("Cache health check failed", e);
            return false;
        }
    }

    private boolean checkAuthorizationServiceHealth() {
        try {
            // Test authorization service by attempting to get context for a test user
            // This should not throw an exception even if the user doesn't exist
            authorizationService.getUserAuthorizationContext("__health_check_user__");
            return true;
        } catch (Exception e) {
            logger.warn("Authorization service health check failed", e);
            return false;
        }
    }

    private void addConfigurationDetails(Map<String, Object> details) {
        Map<String, Object> config = new HashMap<>();
        
        config.put("cacheExpirationMinutes", authorizationProperties.getCache().getExpirationMinutes());
        config.put("maxCacheSize", authorizationProperties.getCache().getMaxSize());
        config.put("databaseMaxRetries", authorizationProperties.getDatabase().getMaxRetries());
        config.put("queryTimeoutSeconds", authorizationProperties.getDatabase().getQueryTimeoutSeconds());
        
        Map<String, Object> jwtConfig = new HashMap<>();
        jwtConfig.put("includeRoles", authorizationProperties.getJwt().isIncludeRoles());
        jwtConfig.put("includePermissions", authorizationProperties.getJwt().isIncludePermissions());
        jwtConfig.put("includeBusinessUnits", authorizationProperties.getJwt().isIncludeBusinessUnits());
        jwtConfig.put("includeReadOnlyAccess", authorizationProperties.getJwt().isIncludeReadOnlyAccess());
        jwtConfig.put("maxTokenSizeBytes", authorizationProperties.getJwt().getMaxTokenSizeBytes());
        config.put("jwt", jwtConfig);
        
        Map<String, Object> fallbackConfig = new HashMap<>();
        fallbackConfig.put("cacheFallbackEnabled", authorizationProperties.getFallback().isEnableCacheFallback());
        fallbackConfig.put("emptyDataFallbackEnabled", authorizationProperties.getFallback().isEnableEmptyDataFallback());
        fallbackConfig.put("maxCacheAgeMinutes", authorizationProperties.getFallback().getMaxCacheAgeMinutes());
        config.put("fallback", fallbackConfig);
        
        details.put("configuration", config);
    }

    private void addPerformanceMetrics(Map<String, Object> details) {
        try {
            Map<String, Object> metrics = new HashMap<>();
            
            // Add basic JVM metrics
            Runtime runtime = Runtime.getRuntime();
            metrics.put("totalMemoryMB", runtime.totalMemory() / (1024 * 1024));
            metrics.put("freeMemoryMB", runtime.freeMemory() / (1024 * 1024));
            metrics.put("usedMemoryMB", (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024));
            
            // Add timestamp for when health check was performed
            metrics.put("lastHealthCheckTimestamp", System.currentTimeMillis());
            metrics.put("lastHealthCheckTime", new java.util.Date().toString());
            
            details.put("metrics", metrics);
        } catch (Exception e) {
            logger.debug("Could not add performance metrics to health check", e);
            details.put("metrics", "Unable to collect metrics");
        }
    }
}