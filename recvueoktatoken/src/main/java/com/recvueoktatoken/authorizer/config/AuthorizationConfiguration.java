package com.recvueoktatoken.authorizer.config;

import com.recvueoktatoken.authorizer.service.AuthorizationService;
import com.recvueoktatoken.authorizer.service.impl.AuthorizationServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import javax.sql.DataSource;

/**
 * Configuration class for authorization enhancement features
 */
@Configuration
@EnableConfigurationProperties(AuthorizationProperties.class)
public class AuthorizationConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(AuthorizationConfiguration.class);

    private final AuthorizationProperties authorizationProperties;

    public AuthorizationConfiguration(AuthorizationProperties authorizationProperties) {
        this.authorizationProperties = authorizationProperties;
        logConfigurationStatus();
    }

    /**
     * Configure AuthorizationService bean conditionally based on feature flag
     */
    @Bean
    @ConditionalOnProperty(
        prefix = "authorization",
        name = "enabled",
        havingValue = "true",
        matchIfMissing = true
    )
    public AuthorizationService authorizationService(DataSource dataSource) {
        
        logger.info("Initializing AuthorizationService with enhanced features enabled");
        
        AuthorizationServiceImpl service = new AuthorizationServiceImpl();

        // Note: AuthorizationServiceImpl uses @Autowired for dependencies
        // The properties and redis template are injected automatically
        if (authorizationProperties.isCachingEnabled()) {
            logger.info("Authorization caching enabled with {}min expiration",
                       authorizationProperties.getCache().getExpirationMinutes());
        } else {
            logger.warn("Authorization caching is disabled - performance may be impacted");
        }

        return service;
    }

    /**
     * Fallback AuthorizationService when authorization enhancement is disabled
     */
    @Bean
    @ConditionalOnProperty(
        prefix = "authorization",
        name = "enabled",
        havingValue = "false"
    )
    public AuthorizationService disabledAuthorizationService() {
        logger.warn("Authorization enhancement is DISABLED - using fallback service");
        return new DisabledAuthorizationService();
    }

    /**
     * Configure RedisTemplate bean if Redis is available and caching is enabled
     * This is a fallback configuration in case no RedisTemplate is already defined
     */
    @Bean
    @ConditionalOnClass(RedisTemplate.class)
    @ConditionalOnProperty(
        prefix = "authorization.cache",
        name = "enabled",
        havingValue = "true"
    )
    public RedisTemplate<String, Object> authorizationRedisTemplate() {
        logger.info("Creating fallback RedisTemplate for authorization caching");
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        // Note: This is a basic configuration. In production, you should configure
        // connection factory, serializers, etc. based on your Redis setup
        return template;
    }

    /**
     * Log the current configuration status
     */
    private void logConfigurationStatus() {
        logger.info("=== Authorization Enhancement Configuration ===");
        logger.info("Authorization Enhancement: {}", 
                   authorizationProperties.isEnabled() ? "ENABLED" : "DISABLED");
        
        if (authorizationProperties.isEnabled()) {
            logger.info("Caching: {}", 
                       authorizationProperties.isCachingEnabled() ? "ENABLED" : "DISABLED");
            logger.info("Cache Expiration: {} minutes", 
                       authorizationProperties.getCache().getExpirationMinutes());
            logger.info("JWT Claims - Roles: {}, Permissions: {}, Business Units: {}, Read-Only: {}",
                       authorizationProperties.getJwt().isIncludeRoles(),
                       authorizationProperties.getJwt().isIncludePermissions(),
                       authorizationProperties.getJwt().isIncludeBusinessUnits(),
                       authorizationProperties.getJwt().isIncludeReadOnlyAccess());
            logger.info("Monitoring: {}", 
                       authorizationProperties.getMonitoring().isMetricsEnabled() ? "ENABLED" : "DISABLED");
            logger.info("Fallback Mechanisms: Cache={}, Empty Data={}", 
                       authorizationProperties.getFallback().isEnableCacheFallback(),
                       authorizationProperties.getFallback().isEnableEmptyDataFallback());
        }
        logger.info("===============================================");
    }

    /**
     * Disabled authorization service that returns empty authorization data
     */
    private static class DisabledAuthorizationService implements AuthorizationService {
        
        private static final Logger logger = LoggerFactory.getLogger(DisabledAuthorizationService.class);
        
        @Override
        public com.recvueoktatoken.authorizer.model.AuthorizationContext getUserAuthorizationContext(String username) {
            logger.debug("Authorization enhancement disabled - returning empty context for user: {}", username);
            
            com.recvueoktatoken.authorizer.model.AuthorizationContext context = 
                new com.recvueoktatoken.authorizer.model.AuthorizationContext();
            context.setRoleNames(java.util.Collections.emptyList());
            context.setPermissionNames(java.util.Collections.emptyList());
            context.setBusinessUnits(java.util.Collections.emptyList());
            context.setReadOnlyAccess(false);
            
            return context;
        }

        @Override
        public void invalidateUserAuthorizationCache(String username) {
            // No-op when disabled
            logger.debug("Authorization enhancement disabled - cache invalidation skipped for user: {}", username);
        }

        @Override
        public boolean isUserAuthorizedForResource(String username, String resource, String action) {
            // Always return false when disabled (fail-safe)
            logger.debug("Authorization enhancement disabled - denying access for user: {} to resource: {} action: {}",
                        username, resource, action);
            return false;
        }

        @Override
        public com.recvueoktatoken.authorizer.model.AuthorizationContext getCachedAuthorizationContext(String username) {
            // No cache when disabled
            logger.debug("Authorization enhancement disabled - no cached context for user: {}", username);
            return null;
        }

        @Override
        public com.recvueoktatoken.authorizer.model.AuthorizationContext refreshUserAuthorizationContext(String username) throws Exception {
            // Return empty context when disabled
            logger.debug("Authorization enhancement disabled - returning empty context for refresh request for user: {}", username);
            return getUserAuthorizationContext(username);
        }
    }
}