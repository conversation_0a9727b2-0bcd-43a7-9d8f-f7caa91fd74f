package com.recvueoktatoken.authorizer.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for authorization enhancement features
 */
@Component
@ConfigurationProperties(prefix = "authorization")
public class AuthorizationProperties {

    /**
     * Enable/disable authorization enhancement features
     */
    private boolean enabled = true;

    /**
     * Enable/disable authorization context caching
     */
    private boolean cachingEnabled = true;

    /**
     * Cache configuration
     */
    private Cache cache = new Cache();

    /**
     * Database configuration
     */
    private Database database = new Database();

    /**
     * JWT configuration
     */
    private Jwt jwt = new Jwt();

    /**
     * Monitoring configuration
     */
    private Monitoring monitoring = new Monitoring();

    /**
     * Fallback configuration
     */
    private Fallback fallback = new Fallback();

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isCachingEnabled() {
        return cachingEnabled;
    }

    public void setCachingEnabled(boolean cachingEnabled) {
        this.cachingEnabled = cachingEnabled;
    }

    public Cache getCache() {
        return cache;
    }

    public void setCache(Cache cache) {
        this.cache = cache;
    }

    public Database getDatabase() {
        return database;
    }

    public void setDatabase(Database database) {
        this.database = database;
    }

    public Jwt getJwt() {
        return jwt;
    }

    public void setJwt(Jwt jwt) {
        this.jwt = jwt;
    }

    public Monitoring getMonitoring() {
        return monitoring;
    }

    public void setMonitoring(Monitoring monitoring) {
        this.monitoring = monitoring;
    }

    public Fallback getFallback() {
        return fallback;
    }

    public void setFallback(Fallback fallback) {
        this.fallback = fallback;
    }

    /**
     * Cache configuration properties
     */
    public static class Cache {
        /**
         * Cache expiration time in minutes (default: 58 minutes to match user token cache)
         */
        private long expirationMinutes = 58L;

        /**
         * Cache key prefix for authorization contexts
         */
        private String keyPrefix = "auth_context";

        /**
         * Maximum cache size (number of entries)
         */
        private long maxSize = 10000L;

        /**
         * Enable cache statistics collection
         */
        private boolean statisticsEnabled = true;

        public long getExpirationMinutes() {
            return expirationMinutes;
        }

        public void setExpirationMinutes(long expirationMinutes) {
            this.expirationMinutes = expirationMinutes;
        }

        public String getKeyPrefix() {
            return keyPrefix;
        }

        public void setKeyPrefix(String keyPrefix) {
            this.keyPrefix = keyPrefix;
        }

        public long getMaxSize() {
            return maxSize;
        }

        public void setMaxSize(long maxSize) {
            this.maxSize = maxSize;
        }

        public boolean isStatisticsEnabled() {
            return statisticsEnabled;
        }

        public void setStatisticsEnabled(boolean statisticsEnabled) {
            this.statisticsEnabled = statisticsEnabled;
        }
    }

    /**
     * Database configuration properties
     */
    public static class Database {
        /**
         * Connection timeout for authorization queries in seconds
         */
        private int connectionTimeoutSeconds = 30;

        /**
         * Query timeout for authorization queries in seconds
         */
        private int queryTimeoutSeconds = 15;

        /**
         * Maximum number of retries for failed database operations
         */
        private int maxRetries = 3;

        /**
         * Retry delay in milliseconds
         */
        private long retryDelayMs = 1000L;

        /**
         * Enable batch processing for authorization queries
         */
        private boolean batchProcessingEnabled = true;

        /**
         * Batch size for processing multiple authorization requests
         */
        private int batchSize = 100;

        public int getConnectionTimeoutSeconds() {
            return connectionTimeoutSeconds;
        }

        public void setConnectionTimeoutSeconds(int connectionTimeoutSeconds) {
            this.connectionTimeoutSeconds = connectionTimeoutSeconds;
        }

        public int getQueryTimeoutSeconds() {
            return queryTimeoutSeconds;
        }

        public void setQueryTimeoutSeconds(int queryTimeoutSeconds) {
            this.queryTimeoutSeconds = queryTimeoutSeconds;
        }

        public int getMaxRetries() {
            return maxRetries;
        }

        public void setMaxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
        }

        public long getRetryDelayMs() {
            return retryDelayMs;
        }

        public void setRetryDelayMs(long retryDelayMs) {
            this.retryDelayMs = retryDelayMs;
        }

        public boolean isBatchProcessingEnabled() {
            return batchProcessingEnabled;
        }

        public void setBatchProcessingEnabled(boolean batchProcessingEnabled) {
            this.batchProcessingEnabled = batchProcessingEnabled;
        }

        public int getBatchSize() {
            return batchSize;
        }

        public void setBatchSize(int batchSize) {
            this.batchSize = batchSize;
        }
    }

    /**
     * JWT configuration properties
     */
    public static class Jwt {
        /**
         * Include roles claim in JWT tokens
         */
        private boolean includeRoles = true;

        /**
         * Include permissions claim in JWT tokens
         */
        private boolean includePermissions = true;

        /**
         * Include business units claim in JWT tokens
         */
        private boolean includeBusinessUnits = true;

        /**
         * Include read-only access claim in JWT tokens
         */
        private boolean includeReadOnlyAccess = true;

        /**
         * Maximum JWT token size in bytes (warning threshold)
         */
        private int maxTokenSizeBytes = 4096;

        /**
         * Maximum number of roles to include in JWT token
         */
        private int maxRoles = 50;

        /**
         * Maximum number of permissions to include in JWT token
         */
        private int maxPermissions = 100;

        /**
         * Maximum number of business units to include in JWT token
         */
        private int maxBusinessUnits = 20;

        public boolean isIncludeRoles() {
            return includeRoles;
        }

        public void setIncludeRoles(boolean includeRoles) {
            this.includeRoles = includeRoles;
        }

        public boolean isIncludePermissions() {
            return includePermissions;
        }

        public void setIncludePermissions(boolean includePermissions) {
            this.includePermissions = includePermissions;
        }

        public boolean isIncludeBusinessUnits() {
            return includeBusinessUnits;
        }

        public void setIncludeBusinessUnits(boolean includeBusinessUnits) {
            this.includeBusinessUnits = includeBusinessUnits;
        }

        public boolean isIncludeReadOnlyAccess() {
            return includeReadOnlyAccess;
        }

        public void setIncludeReadOnlyAccess(boolean includeReadOnlyAccess) {
            this.includeReadOnlyAccess = includeReadOnlyAccess;
        }

        public int getMaxTokenSizeBytes() {
            return maxTokenSizeBytes;
        }

        public void setMaxTokenSizeBytes(int maxTokenSizeBytes) {
            this.maxTokenSizeBytes = maxTokenSizeBytes;
        }

        public int getMaxRoles() {
            return maxRoles;
        }

        public void setMaxRoles(int maxRoles) {
            this.maxRoles = maxRoles;
        }

        public int getMaxPermissions() {
            return maxPermissions;
        }

        public void setMaxPermissions(int maxPermissions) {
            this.maxPermissions = maxPermissions;
        }

        public int getMaxBusinessUnits() {
            return maxBusinessUnits;
        }

        public void setMaxBusinessUnits(int maxBusinessUnits) {
            this.maxBusinessUnits = maxBusinessUnits;
        }
    }

    /**
     * Monitoring configuration properties
     */
    public static class Monitoring {
        /**
         * Enable performance metrics collection
         */
        private boolean metricsEnabled = true;

        /**
         * Enable detailed logging for authorization operations
         */
        private boolean detailedLoggingEnabled = false;

        /**
         * Log slow authorization queries (threshold in milliseconds)
         */
        private long slowQueryThresholdMs = 1000L;

        /**
         * Enable cache hit/miss ratio monitoring
         */
        private boolean cacheMetricsEnabled = true;

        /**
         * Enable JWT token size monitoring
         */
        private boolean tokenSizeMonitoringEnabled = true;

        /**
         * Health check interval in seconds
         */
        private int healthCheckIntervalSeconds = 60;

        public boolean isMetricsEnabled() {
            return metricsEnabled;
        }

        public void setMetricsEnabled(boolean metricsEnabled) {
            this.metricsEnabled = metricsEnabled;
        }

        public boolean isDetailedLoggingEnabled() {
            return detailedLoggingEnabled;
        }

        public void setDetailedLoggingEnabled(boolean detailedLoggingEnabled) {
            this.detailedLoggingEnabled = detailedLoggingEnabled;
        }

        public long getSlowQueryThresholdMs() {
            return slowQueryThresholdMs;
        }

        public void setSlowQueryThresholdMs(long slowQueryThresholdMs) {
            this.slowQueryThresholdMs = slowQueryThresholdMs;
        }

        public boolean isCacheMetricsEnabled() {
            return cacheMetricsEnabled;
        }

        public void setCacheMetricsEnabled(boolean cacheMetricsEnabled) {
            this.cacheMetricsEnabled = cacheMetricsEnabled;
        }

        public boolean isTokenSizeMonitoringEnabled() {
            return tokenSizeMonitoringEnabled;
        }

        public void setTokenSizeMonitoringEnabled(boolean tokenSizeMonitoringEnabled) {
            this.tokenSizeMonitoringEnabled = tokenSizeMonitoringEnabled;
        }

        public int getHealthCheckIntervalSeconds() {
            return healthCheckIntervalSeconds;
        }

        public void setHealthCheckIntervalSeconds(int healthCheckIntervalSeconds) {
            this.healthCheckIntervalSeconds = healthCheckIntervalSeconds;
        }
    }

    /**
     * Fallback configuration properties
     */
    public static class Fallback {
        /**
         * Enable fallback to cached data when database is unavailable
         */
        private boolean enableCacheFallback = true;

        /**
         * Enable fallback to empty authorization data when all else fails
         */
        private boolean enableEmptyDataFallback = true;

        /**
         * Maximum age of cached data to use for fallback (in minutes)
         */
        private long maxCacheAgeMinutes = 120L;

        /**
         * Circuit breaker failure threshold
         */
        private int circuitBreakerFailureThreshold = 5;

        /**
         * Circuit breaker timeout in seconds
         */
        private int circuitBreakerTimeoutSeconds = 60;

        public boolean isEnableCacheFallback() {
            return enableCacheFallback;
        }

        public void setEnableCacheFallback(boolean enableCacheFallback) {
            this.enableCacheFallback = enableCacheFallback;
        }

        public boolean isEnableEmptyDataFallback() {
            return enableEmptyDataFallback;
        }

        public void setEnableEmptyDataFallback(boolean enableEmptyDataFallback) {
            this.enableEmptyDataFallback = enableEmptyDataFallback;
        }

        public long getMaxCacheAgeMinutes() {
            return maxCacheAgeMinutes;
        }

        public void setMaxCacheAgeMinutes(long maxCacheAgeMinutes) {
            this.maxCacheAgeMinutes = maxCacheAgeMinutes;
        }

        public int getCircuitBreakerFailureThreshold() {
            return circuitBreakerFailureThreshold;
        }

        public void setCircuitBreakerFailureThreshold(int circuitBreakerFailureThreshold) {
            this.circuitBreakerFailureThreshold = circuitBreakerFailureThreshold;
        }

        public int getCircuitBreakerTimeoutSeconds() {
            return circuitBreakerTimeoutSeconds;
        }

        public void setCircuitBreakerTimeoutSeconds(int circuitBreakerTimeoutSeconds) {
            this.circuitBreakerTimeoutSeconds = circuitBreakerTimeoutSeconds;
        }
    }
}