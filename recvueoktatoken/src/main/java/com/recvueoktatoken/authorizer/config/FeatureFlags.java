package com.recvueoktatoken.authorizer.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Feature flags for authorization enhancement
 * Allows granular control over feature enablement
 */
@Component
@ConfigurationProperties(prefix = "feature.authorization")
public class FeatureFlags {

    /**
     * Master switch for all authorization enhancement features
     */
    private boolean enabled = true;

    /**
     * Enable/disable role claims in JWT tokens
     */
    private boolean rolesEnabled = true;

    /**
     * Enable/disable permission claims in JWT tokens
     */
    private boolean permissionsEnabled = true;

    /**
     * Enable/disable business unit claims in JWT tokens
     */
    private boolean businessUnitsEnabled = true;

    /**
     * Enable/disable read-only access claims in JWT tokens
     */
    private boolean readOnlyAccessEnabled = true;

    /**
     * Enable/disable authorization context caching
     */
    private boolean cachingEnabled = true;

    /**
     * Enable/disable database fallback when cache fails
     */
    private boolean databaseFallbackEnabled = true;

    /**
     * Enable/disable empty data fallback when all else fails
     */
    private boolean emptyDataFallbackEnabled = true;

    /**
     * Enable/disable performance metrics collection
     */
    private boolean metricsEnabled = true;

    /**
     * Enable/disable detailed logging (use with caution in production)
     */
    private boolean detailedLoggingEnabled = false;

    /**
     * Enable/disable batch processing for authorization queries
     */
    private boolean batchProcessingEnabled = true;

    /**
     * Enable/disable circuit breaker for database operations
     */
    private boolean circuitBreakerEnabled = true;

    /**
     * Enable/disable JWT token size monitoring
     */
    private boolean tokenSizeMonitoringEnabled = true;

    /**
     * Enable/disable cache statistics collection
     */
    private boolean cacheStatisticsEnabled = true;

    /**
     * Enable/disable health check endpoint
     */
    private boolean healthCheckEnabled = true;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isRolesEnabled() {
        return enabled && rolesEnabled;
    }

    public void setRolesEnabled(boolean rolesEnabled) {
        this.rolesEnabled = rolesEnabled;
    }

    public boolean isPermissionsEnabled() {
        return enabled && permissionsEnabled;
    }

    public void setPermissionsEnabled(boolean permissionsEnabled) {
        this.permissionsEnabled = permissionsEnabled;
    }

    public boolean isBusinessUnitsEnabled() {
        return enabled && businessUnitsEnabled;
    }

    public void setBusinessUnitsEnabled(boolean businessUnitsEnabled) {
        this.businessUnitsEnabled = businessUnitsEnabled;
    }

    public boolean isReadOnlyAccessEnabled() {
        return enabled && readOnlyAccessEnabled;
    }

    public void setReadOnlyAccessEnabled(boolean readOnlyAccessEnabled) {
        this.readOnlyAccessEnabled = readOnlyAccessEnabled;
    }

    public boolean isCachingEnabled() {
        return enabled && cachingEnabled;
    }

    public void setCachingEnabled(boolean cachingEnabled) {
        this.cachingEnabled = cachingEnabled;
    }

    public boolean isDatabaseFallbackEnabled() {
        return enabled && databaseFallbackEnabled;
    }

    public void setDatabaseFallbackEnabled(boolean databaseFallbackEnabled) {
        this.databaseFallbackEnabled = databaseFallbackEnabled;
    }

    public boolean isEmptyDataFallbackEnabled() {
        return enabled && emptyDataFallbackEnabled;
    }

    public void setEmptyDataFallbackEnabled(boolean emptyDataFallbackEnabled) {
        this.emptyDataFallbackEnabled = emptyDataFallbackEnabled;
    }

    public boolean isMetricsEnabled() {
        return enabled && metricsEnabled;
    }

    public void setMetricsEnabled(boolean metricsEnabled) {
        this.metricsEnabled = metricsEnabled;
    }

    public boolean isDetailedLoggingEnabled() {
        return enabled && detailedLoggingEnabled;
    }

    public void setDetailedLoggingEnabled(boolean detailedLoggingEnabled) {
        this.detailedLoggingEnabled = detailedLoggingEnabled;
    }

    public boolean isBatchProcessingEnabled() {
        return enabled && batchProcessingEnabled;
    }

    public void setBatchProcessingEnabled(boolean batchProcessingEnabled) {
        this.batchProcessingEnabled = batchProcessingEnabled;
    }

    public boolean isCircuitBreakerEnabled() {
        return enabled && circuitBreakerEnabled;
    }

    public void setCircuitBreakerEnabled(boolean circuitBreakerEnabled) {
        this.circuitBreakerEnabled = circuitBreakerEnabled;
    }

    public boolean isTokenSizeMonitoringEnabled() {
        return enabled && tokenSizeMonitoringEnabled;
    }

    public void setTokenSizeMonitoringEnabled(boolean tokenSizeMonitoringEnabled) {
        this.tokenSizeMonitoringEnabled = tokenSizeMonitoringEnabled;
    }

    public boolean isCacheStatisticsEnabled() {
        return enabled && cacheStatisticsEnabled;
    }

    public void setCacheStatisticsEnabled(boolean cacheStatisticsEnabled) {
        this.cacheStatisticsEnabled = cacheStatisticsEnabled;
    }

    public boolean isHealthCheckEnabled() {
        return enabled && healthCheckEnabled;
    }

    public void setHealthCheckEnabled(boolean healthCheckEnabled) {
        this.healthCheckEnabled = healthCheckEnabled;
    }

    /**
     * Check if any JWT claim enhancement is enabled
     */
    public boolean isAnyJwtClaimEnabled() {
        return isRolesEnabled() || isPermissionsEnabled() || 
               isBusinessUnitsEnabled() || isReadOnlyAccessEnabled();
    }

    /**
     * Check if any fallback mechanism is enabled
     */
    public boolean isAnyFallbackEnabled() {
        return isDatabaseFallbackEnabled() || isEmptyDataFallbackEnabled();
    }

    /**
     * Get a summary of enabled features for logging
     */
    public String getEnabledFeaturesSummary() {
        if (!enabled) {
            return "Authorization enhancement: DISABLED";
        }

        StringBuilder summary = new StringBuilder("Authorization enhancement: ENABLED [");
        
        if (isRolesEnabled()) summary.append("roles,");
        if (isPermissionsEnabled()) summary.append("permissions,");
        if (isBusinessUnitsEnabled()) summary.append("businessUnits,");
        if (isReadOnlyAccessEnabled()) summary.append("readOnlyAccess,");
        if (isCachingEnabled()) summary.append("caching,");
        if (isMetricsEnabled()) summary.append("metrics,");
        
        // Remove trailing comma
        if (summary.charAt(summary.length() - 1) == ',') {
            summary.setLength(summary.length() - 1);
        }
        
        summary.append("]");
        return summary.toString();
    }
}