package com.recvueoktatoken.coreuser.dao.impl;

import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.recvueoktatoken.authorizer.model.BusinessUnitInfo;
import com.recvueoktatoken.common.entity.CoreUser;
import com.recvueoktatoken.coreuser.dao.CoreUserDao;

/**
 * Integration tests for authorization-related methods in CoreUserDaoImpl.
 * These tests require a database connection and test data.
 * 
 * To run these tests, set system property: -Drun.integration.tests=true
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@EnabledIfSystemProperty(named = "run.integration.tests", matches = "true")
class CoreUserDaoImplAuthorizationIntegrationTest {

    @Autowired
    private CoreUserDao coreUserDao;

    private static final String TEST_ADMIN_USER = "admin.test";
    private static final String TEST_READONLY_USER = "readonly.test";
    private static final String TEST_MULTI_ROLE_USER = "multirole.test";
    private static final String NON_EXISTENT_USER = "nonexistent.user";

    @Test
    void testGetUserWithAuthorizationContext_AdminUser() {
        // Act
        CoreUser result = coreUserDao.getUserWithAuthorizationContext(TEST_ADMIN_USER);

        // Assert
        if (result != null) { // Only test if test data exists
            assertNotNull(result);
            assertEquals(TEST_ADMIN_USER, result.getUserName());
            assertEquals("A", result.getStatus());

            // Verify that roles are fetched
            assertNotNull(result.getCoreRoles());

            // Log for debugging
            System.out.println("Admin user roles: " + result.getCoreRoles().size());
        }
    }

    @Test
    void testGetUserWithAuthorizationContext_NonExistentUser() {
        // Act
        CoreUser result = coreUserDao.getUserWithAuthorizationContext(NON_EXISTENT_USER);

        // Assert
        assertNull(result);
    }

    @Test
    void testGetUserRoleNames_AdminUser() {
        // Act
        List<String> roles = coreUserDao.getUserRoleNames(TEST_ADMIN_USER);

        // Assert
        assertNotNull(roles);

        if (!roles.isEmpty()) { // Only test if test data exists
            assertTrue(roles.size() > 0);

            // Log for debugging
            System.out.println("Admin user role names: " + roles);

            // Verify no duplicates
            assertEquals(roles.size(), roles.stream().distinct().count());
        }
    }

    @Test
    void testGetUserRoleNames_NonExistentUser() {
        // Act
        List<String> roles = coreUserDao.getUserRoleNames(NON_EXISTENT_USER);

        // Assert
        assertNotNull(roles);
        assertTrue(roles.isEmpty());
    }

    @Test
    void testGetUserPermissionNames_AdminUser() {
        // Act
        List<String> permissions = coreUserDao.getUserPermissionNames(TEST_ADMIN_USER);

        // Assert
        assertNotNull(permissions);

        if (!permissions.isEmpty()) { // Only test if test data exists
            assertTrue(permissions.size() > 0);

            // Log for debugging
            System.out.println("Admin user permissions: " + permissions);

            // Verify no duplicates
            assertEquals(permissions.size(), permissions.stream().distinct().count());
        }
    }

    @Test
    void testGetUserPermissionNames_NonExistentUser() {
        // Act
        List<String> permissions = coreUserDao.getUserPermissionNames(NON_EXISTENT_USER);

        // Assert
        assertNotNull(permissions);
        assertTrue(permissions.isEmpty());
    }

    @Test
    void testGetUserBusinessUnits_AdminUser() {
        // Act
        List<BusinessUnitInfo> businessUnits = coreUserDao.getUserBusinessUnits(TEST_ADMIN_USER);

        // Assert
        assertNotNull(businessUnits);

        if (!businessUnits.isEmpty()) { // Only test if test data exists
            assertTrue(businessUnits.size() > 0);

            // Verify structure
            for (BusinessUnitInfo bu : businessUnits) {
                assertNotNull(bu.getBusinessUnitId());
                assertNotNull(bu.getBusinessUnitName());
                assertTrue(bu.getBusinessUnitId() > 0);
                assertFalse(bu.getBusinessUnitName().trim().isEmpty());
            }

            // Log for debugging
            System.out.println("Admin user business units: " + businessUnits);

            // Verify no duplicates based on ID
            long distinctCount = businessUnits.stream()
                    .mapToLong(BusinessUnitInfo::getBusinessUnitId)
                    .distinct()
                    .count();
            assertEquals(businessUnits.size(), distinctCount);
        }
    }

    @Test
    void testGetUserBusinessUnits_NonExistentUser() {
        // Act
        List<BusinessUnitInfo> businessUnits = coreUserDao.getUserBusinessUnits(NON_EXISTENT_USER);

        // Assert
        assertNotNull(businessUnits);
        assertTrue(businessUnits.isEmpty());
    }

    @Test
    void testIsReadOnlyUser_ReadOnlyUser() {
        // Act
        boolean isReadOnly = coreUserDao.isReadOnlyUser(TEST_READONLY_USER);

        // Assert - This will depend on test data setup
        // For now, just verify the method doesn't throw an exception
        // In a real test environment, you would set up specific test data
        assertNotNull(isReadOnly); // Just verify boolean is returned

        // Log for debugging
        System.out.println("Read-only user status: " + isReadOnly);
    }

    @Test
    void testIsReadOnlyUser_RegularUser() {
        // Act
        boolean isReadOnly = coreUserDao.isReadOnlyUser(TEST_ADMIN_USER);

        // Assert - This will depend on test data setup
        assertNotNull(isReadOnly); // Just verify boolean is returned

        // Log for debugging
        System.out.println("Admin user read-only status: " + isReadOnly);
    }

    @Test
    void testIsReadOnlyUser_NonExistentUser() {
        // Act
        boolean isReadOnly = coreUserDao.isReadOnlyUser(NON_EXISTENT_USER);

        // Assert
        assertFalse(isReadOnly); // Should return false for non-existent users
    }

    @Test
    void testMultiRoleUser_AllMethods() {
        // Test a user with multiple roles to ensure aggregation works correctly

        // Act
        List<String> roles = coreUserDao.getUserRoleNames(TEST_MULTI_ROLE_USER);
        List<String> permissions = coreUserDao.getUserPermissionNames(TEST_MULTI_ROLE_USER);
        List<BusinessUnitInfo> businessUnits = coreUserDao.getUserBusinessUnits(TEST_MULTI_ROLE_USER);
        boolean isReadOnly = coreUserDao.isReadOnlyUser(TEST_MULTI_ROLE_USER);

        // Assert
        assertNotNull(roles);
        assertNotNull(permissions);
        assertNotNull(businessUnits);

        if (!roles.isEmpty()) { // Only test if test data exists
            // Log for debugging
            System.out.println("Multi-role user roles: " + roles);
            System.out.println("Multi-role user permissions: " + permissions);
            System.out.println("Multi-role user business units: " + businessUnits);
            System.out.println("Multi-role user read-only: " + isReadOnly);

            // Verify aggregation - permissions should be >= roles (since roles contain
            // permissions)
            assertTrue(permissions.size() >= roles.size() || permissions.isEmpty());

            // Verify no duplicates in any collection
            assertEquals(roles.size(), roles.stream().distinct().count());
            assertEquals(permissions.size(), permissions.stream().distinct().count());
            long distinctBUCount = businessUnits.stream()
                    .mapToLong(BusinessUnitInfo::getBusinessUnitId)
                    .distinct()
                    .count();
            assertEquals(businessUnits.size(), distinctBUCount);
        }
    }

    @Test
    void testPerformanceWithLargeDataset() {
        // Test performance with a user that has many roles/permissions
        long startTime = System.currentTimeMillis();

        // Act
        CoreUser user = coreUserDao.getUserWithAuthorizationContext(TEST_ADMIN_USER);
        List<String> roles = coreUserDao.getUserRoleNames(TEST_ADMIN_USER);
        List<String> permissions = coreUserDao.getUserPermissionNames(TEST_ADMIN_USER);
        List<BusinessUnitInfo> businessUnits = coreUserDao.getUserBusinessUnits(TEST_ADMIN_USER);
        boolean isReadOnly = coreUserDao.isReadOnlyUser(TEST_ADMIN_USER);

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // Assert
        assertTrue(duration < 5000, "Authorization queries took too long: " + duration + "ms");

        // Log performance metrics
        System.out.println("Authorization queries completed in: " + duration + "ms");
        if (user != null) {
            System.out.println("Results - Roles: " + roles.size() +
                    ", Permissions: " + permissions.size() +
                    ", Business Units: " + businessUnits.size() +
                    ", Read-only: " + isReadOnly);
        }
    }

    @Test
    void testDataConsistency() {
        // Test that the same user returns consistent data across different method calls

        // Act - Call methods multiple times
        List<String> roles1 = coreUserDao.getUserRoleNames(TEST_ADMIN_USER);
        List<String> roles2 = coreUserDao.getUserRoleNames(TEST_ADMIN_USER);

        List<String> permissions1 = coreUserDao.getUserPermissionNames(TEST_ADMIN_USER);
        List<String> permissions2 = coreUserDao.getUserPermissionNames(TEST_ADMIN_USER);

        boolean readOnly1 = coreUserDao.isReadOnlyUser(TEST_ADMIN_USER);
        boolean readOnly2 = coreUserDao.isReadOnlyUser(TEST_ADMIN_USER);

        // Assert - Results should be consistent
        assertEquals(roles1.size(), roles2.size());
        assertEquals(permissions1.size(), permissions2.size());
        assertEquals(readOnly1, readOnly2);

        // Verify content is the same
        assertTrue(roles1.containsAll(roles2) && roles2.containsAll(roles1));
        assertTrue(permissions1.containsAll(permissions2) && permissions2.containsAll(permissions1));
    }

    @Test
    void testQueryOptimization() {
        // Test that the fetch join query is more efficient than separate queries

        long startTime1 = System.currentTimeMillis();

        // Single optimized query
        CoreUser userWithContext = coreUserDao.getUserWithAuthorizationContext(TEST_ADMIN_USER);

        long endTime1 = System.currentTimeMillis();
        long optimizedDuration = endTime1 - startTime1;

        long startTime2 = System.currentTimeMillis();

        // Multiple separate queries
        List<String> roles = coreUserDao.getUserRoleNames(TEST_ADMIN_USER);
        List<String> permissions = coreUserDao.getUserPermissionNames(TEST_ADMIN_USER);
        List<BusinessUnitInfo> businessUnits = coreUserDao.getUserBusinessUnits(TEST_ADMIN_USER);
        boolean isReadOnly = coreUserDao.isReadOnlyUser(TEST_ADMIN_USER);

        long endTime2 = System.currentTimeMillis();
        long separateDuration = endTime2 - startTime2;

        // Log performance comparison
        System.out.println("Optimized query duration: " + optimizedDuration + "ms");
        System.out.println("Separate queries duration: " + separateDuration + "ms");

        // The optimized query should generally be faster, but this depends on data size
        // For now, just verify both approaches work
        if (userWithContext != null) {
            assertNotNull(userWithContext);
        }
        assertNotNull(roles);
        assertNotNull(permissions);
        assertNotNull(businessUnits);
    }
}