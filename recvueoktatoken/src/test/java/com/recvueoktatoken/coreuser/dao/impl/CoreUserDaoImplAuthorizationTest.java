package com.recvueoktatoken.coreuser.dao.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.List;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.hibernate.Session;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.recvueoktatoken.authorizer.model.BusinessUnitInfo;
import com.recvueoktatoken.common.entity.CoreUser;

/**
 * Unit tests for authorization-related methods in CoreUserDaoImpl
 */
@ExtendWith(MockitoExtension.class)
class CoreUserDaoImplAuthorizationTest {

    @Mock
    private Session session;

    @Mock
    private Query query;

    @InjectMocks
    private CoreUserDaoImpl coreUserDao;

    private CoreUser testUser;
    private String testUsername = "testuser";

    @BeforeEach
    void setUp() {
        testUser = new CoreUser();
        testUser.setUserId(1L);
        testUser.setUserName(testUsername);
        testUser.setStatus("A");
        testUser.setReadOnlyUser("N");

        // Mock the getCurrentSession method
        when(coreUserDao.getCurrentSession()).thenReturn(session);
    }

    @Test
    void testGetUserWithAuthorizationContext_Success() {
        // Arrange
        when(session.createQuery(anyString(), eq(CoreUser.class))).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getSingleResult()).thenReturn(testUser);

        // Act
        CoreUser result = coreUserDao.getUserWithAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(testUsername, result.getUserName());
        assertEquals("A", result.getStatus());
        
        verify(session).createQuery(contains("LEFT JOIN FETCH cu.coreRoles"), eq(CoreUser.class));
        verify(query).setParameter("username", testUsername);
    }

    @Test
    void testGetUserWithAuthorizationContext_UserNotFound() {
        // Arrange
        when(session.createQuery(anyString(), eq(CoreUser.class))).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getSingleResult()).thenThrow(new NoResultException());

        // Act
        CoreUser result = coreUserDao.getUserWithAuthorizationContext(testUsername);

        // Assert
        assertNull(result);
    }

    @Test
    void testGetUserWithAuthorizationContext_DatabaseError() {
        // Arrange
        when(session.createQuery(anyString(), eq(CoreUser.class))).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getSingleResult()).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            coreUserDao.getUserWithAuthorizationContext(testUsername);
        });
    }

    @Test
    void testGetUserRoleNames_Success() {
        // Arrange
        List<String> expectedRoles = Arrays.asList("ADMIN", "USER_MANAGER");
        when(session.createQuery(anyString())).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getResultList()).thenReturn(expectedRoles);

        // Act
        List<String> result = coreUserDao.getUserRoleNames(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("ADMIN"));
        assertTrue(result.contains("USER_MANAGER"));
        
        verify(session).createQuery(contains("SELECT DISTINCT cr.roleName"));
        verify(query).setParameter("username", testUsername);
    }

    @Test
    void testGetUserRoleNames_EmptyResult() {
        // Arrange
        when(session.createQuery(anyString())).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getResultList()).thenReturn(Arrays.asList());

        // Act
        List<String> result = coreUserDao.getUserRoleNames(testUsername);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetUserRoleNames_DatabaseError() {
        // Arrange
        when(session.createQuery(anyString())).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getResultList()).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            coreUserDao.getUserRoleNames(testUsername);
        });
    }

    @Test
    void testGetUserPermissionNames_Success() {
        // Arrange
        List<String> expectedPermissions = Arrays.asList("USER_CREATE", "USER_READ", "USER_UPDATE", "REPORT_VIEW");
        when(session.createQuery(anyString())).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getResultList()).thenReturn(expectedPermissions);

        // Act
        List<String> result = coreUserDao.getUserPermissionNames(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(4, result.size());
        assertTrue(result.contains("USER_CREATE"));
        assertTrue(result.contains("USER_READ"));
        assertTrue(result.contains("USER_UPDATE"));
        assertTrue(result.contains("REPORT_VIEW"));
        
        verify(session).createQuery(contains("SELECT DISTINCT cp.perName"));
        verify(query).setParameter("username", testUsername);
    }

    @Test
    void testGetUserPermissionNames_EmptyResult() {
        // Arrange
        when(session.createQuery(anyString())).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getResultList()).thenReturn(Arrays.asList());

        // Act
        List<String> result = coreUserDao.getUserPermissionNames(testUsername);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetUserPermissionNames_DatabaseError() {
        // Arrange
        when(session.createQuery(anyString())).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getResultList()).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            coreUserDao.getUserPermissionNames(testUsername);
        });
    }

    @Test
    void testGetUserBusinessUnits_Success() {
        // Arrange
        Object[] bu1 = {1L, "North America"};
        Object[] bu2 = {2L, "Europe"};
        List<Object[]> expectedResults = Arrays.asList(bu1, bu2);
        
        when(session.createQuery(anyString())).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getResultList()).thenReturn(expectedResults);

        // Act
        List<BusinessUnitInfo> result = coreUserDao.getUserBusinessUnits(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        
        BusinessUnitInfo firstBU = result.get(0);
        assertEquals(Long.valueOf(1L), firstBU.getBusinessUnitId());
        assertEquals("North America", firstBU.getBusinessUnitName());
        
        BusinessUnitInfo secondBU = result.get(1);
        assertEquals(Long.valueOf(2L), secondBU.getBusinessUnitId());
        assertEquals("Europe", secondBU.getBusinessUnitName());
        
        verify(session).createQuery(contains("SELECT DISTINCT bu.orgId, bu.name"));
        verify(query).setParameter("username", testUsername);
    }

    @Test
    void testGetUserBusinessUnits_EmptyResult() {
        // Arrange
        when(session.createQuery(anyString())).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getResultList()).thenReturn(Arrays.asList());

        // Act
        List<BusinessUnitInfo> result = coreUserDao.getUserBusinessUnits(testUsername);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetUserBusinessUnits_DatabaseError() {
        // Arrange
        when(session.createQuery(anyString())).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getResultList()).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            coreUserDao.getUserBusinessUnits(testUsername);
        });
    }

    @Test
    void testIsReadOnlyUser_True() {
        // Arrange
        when(session.createQuery(anyString(), eq(String.class))).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getSingleResult()).thenReturn("Y");

        // Act
        boolean result = coreUserDao.isReadOnlyUser(testUsername);

        // Assert
        assertTrue(result);
        
        verify(session).createQuery(contains("SELECT cu.readOnlyUser"), eq(String.class));
        verify(query).setParameter("username", testUsername);
    }

    @Test
    void testIsReadOnlyUser_False() {
        // Arrange
        when(session.createQuery(anyString(), eq(String.class))).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getSingleResult()).thenReturn("N");

        // Act
        boolean result = coreUserDao.isReadOnlyUser(testUsername);

        // Assert
        assertFalse(result);
    }

    @Test
    void testIsReadOnlyUser_NullValue() {
        // Arrange
        when(session.createQuery(anyString(), eq(String.class))).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getSingleResult()).thenReturn(null);

        // Act
        boolean result = coreUserDao.isReadOnlyUser(testUsername);

        // Assert
        assertFalse(result);
    }

    @Test
    void testIsReadOnlyUser_CaseInsensitive() {
        // Arrange
        when(session.createQuery(anyString(), eq(String.class))).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getSingleResult()).thenReturn("y"); // lowercase

        // Act
        boolean result = coreUserDao.isReadOnlyUser(testUsername);

        // Assert
        assertTrue(result);
    }

    @Test
    void testIsReadOnlyUser_UserNotFound() {
        // Arrange
        when(session.createQuery(anyString(), eq(String.class))).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getSingleResult()).thenThrow(new NoResultException());

        // Act
        boolean result = coreUserDao.isReadOnlyUser(testUsername);

        // Assert
        assertFalse(result);
    }

    @Test
    void testIsReadOnlyUser_DatabaseError() {
        // Arrange
        when(session.createQuery(anyString(), eq(String.class))).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getSingleResult()).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            coreUserDao.isReadOnlyUser(testUsername);
        });
    }

    @Test
    void testGetUserWithAuthorizationContext_QueryStructure() {
        // Arrange
        when(session.createQuery(anyString(), eq(CoreUser.class))).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getSingleResult()).thenReturn(testUser);

        // Act
        coreUserDao.getUserWithAuthorizationContext(testUsername);

        // Assert - Verify the query contains all necessary joins
        verify(session).createQuery(argThat(hql -> {
            String query = (String) hql;
            return query.contains("LEFT JOIN FETCH cu.coreRoles cr") &&
                   query.contains("LEFT JOIN FETCH cr.corePermissions cp") &&
                   query.contains("LEFT JOIN FETCH cr.businessUnits bu") &&
                   query.contains("LEFT JOIN FETCH bu.businessUnits") &&
                   query.contains("cu.status = 'A'");
        }), eq(CoreUser.class));
    }

    @Test
    void testGetUserRoleNames_QueryStructure() {
        // Arrange
        when(session.createQuery(anyString())).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getResultList()).thenReturn(Arrays.asList());

        // Act
        coreUserDao.getUserRoleNames(testUsername);

        // Assert - Verify the query structure
        verify(session).createQuery(argThat(hql -> {
            String query = (String) hql;
            return query.contains("SELECT DISTINCT cr.roleName") &&
                   query.contains("JOIN cu.coreRoles cr") &&
                   query.contains("cu.status = 'A'");
        }));
    }

    @Test
    void testGetUserPermissionNames_QueryStructure() {
        // Arrange
        when(session.createQuery(anyString())).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getResultList()).thenReturn(Arrays.asList());

        // Act
        coreUserDao.getUserPermissionNames(testUsername);

        // Assert - Verify the query structure
        verify(session).createQuery(argThat(hql -> {
            String query = (String) hql;
            return query.contains("SELECT DISTINCT cp.perName") &&
                   query.contains("JOIN cu.coreRoles cr") &&
                   query.contains("JOIN cr.corePermissions cp") &&
                   query.contains("cu.status = 'A'");
        }));
    }

    @Test
    void testGetUserBusinessUnits_QueryStructure() {
        // Arrange
        when(session.createQuery(anyString())).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getResultList()).thenReturn(Arrays.asList());

        // Act
        coreUserDao.getUserBusinessUnits(testUsername);

        // Assert - Verify the query structure
        verify(session).createQuery(argThat(hql -> {
            String query = (String) hql;
            return query.contains("SELECT DISTINCT bu.orgId, bu.name") &&
                   query.contains("JOIN cu.coreRoles cr") &&
                   query.contains("JOIN cr.businessUnits crbu") &&
                   query.contains("JOIN crbu.businessUnits bu") &&
                   query.contains("cu.status = 'A'");
        }));
    }

    @Test
    void testIsReadOnlyUser_QueryStructure() {
        // Arrange
        when(session.createQuery(anyString(), eq(String.class))).thenReturn(query);
        when(query.setParameter("username", testUsername)).thenReturn(query);
        when(query.getSingleResult()).thenReturn("N");

        // Act
        coreUserDao.isReadOnlyUser(testUsername);

        // Assert - Verify the query structure
        verify(session).createQuery(argThat(hql -> {
            String query = (String) hql;
            return query.contains("SELECT cu.readOnlyUser") &&
                   query.contains("cu.status = 'A'");
        }), eq(String.class));
    }
}