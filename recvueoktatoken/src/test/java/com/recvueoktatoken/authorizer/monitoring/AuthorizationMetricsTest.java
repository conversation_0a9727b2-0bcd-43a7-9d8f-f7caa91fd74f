package com.recvueoktatoken.authorizer.monitoring;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Unit tests for AuthorizationMetrics
 */
class AuthorizationMetricsTest {

    private AuthorizationMetrics metrics;

    @BeforeEach
    void setUp() {
        metrics = new AuthorizationMetrics();
        metrics.resetMetrics(); // Ensure clean state
    }

    @Test
    void testCacheMetrics() {
        // Test initial state
        assertEquals(0, metrics.getCacheHits());
        assertEquals(0, metrics.getCacheMisses());
        assertEquals(0, metrics.getCacheErrors());
        assertEquals(0.0, metrics.getCacheHitRatio());

        // Test cache hits
        metrics.recordCacheHit();
        metrics.recordCacheHit();
        assertEquals(2, metrics.getCacheHits());
        assertEquals(100.0, metrics.getCacheHitRatio());

        // Test cache misses
        metrics.recordCacheMiss();
        assertEquals(1, metrics.getCacheMisses());
        assertEquals(66.67, metrics.getCacheHitRatio(), 0.01);

        // Test cache errors
        metrics.recordCacheError();
        assertEquals(1, metrics.getCacheErrors());
    }

    @Test
    void testDatabaseMetrics() {
        // Test initial state
        assertEquals(0, metrics.getDatabaseQueries());
        assertEquals(0, metrics.getDatabaseErrors());
        assertEquals(0, metrics.getDatabaseFallbacks());
        assertEquals(0.0, metrics.getDatabaseErrorRatio());

        // Test database queries
        metrics.recordDatabaseQuery();
        metrics.recordDatabaseQuery();
        assertEquals(2, metrics.getDatabaseQueries());

        // Test database errors
        metrics.recordDatabaseError();
        assertEquals(1, metrics.getDatabaseErrors());
        assertEquals(50.0, metrics.getDatabaseErrorRatio());

        // Test database fallbacks
        metrics.recordDatabaseFallback();
        assertEquals(1, metrics.getDatabaseFallbacks());
    }

    @Test
    void testPerformanceMetrics() {
        // Test initial state
        assertEquals(0, metrics.getTotalRequests());
        assertEquals(0, metrics.getAverageResponseTime());

        // Test response time recording
        metrics.recordResponseTime(100);
        assertEquals(1, metrics.getTotalRequests());
        assertEquals(100, metrics.getAverageResponseTime());

        metrics.recordResponseTime(200);
        assertEquals(2, metrics.getTotalRequests());
        assertEquals(150, metrics.getAverageResponseTime());

        metrics.recordResponseTime(300);
        assertEquals(3, metrics.getTotalRequests());
        assertEquals(200, metrics.getAverageResponseTime());
    }

    @Test
    void testAuthorizationDataMetrics() {
        // Test initial state
        assertEquals(0, metrics.getEmptyAuthorizationContexts());
        assertEquals(0, metrics.getReadOnlyUsers());

        // Test empty authorization contexts
        metrics.recordEmptyAuthorizationContext();
        metrics.recordEmptyAuthorizationContext();
        assertEquals(2, metrics.getEmptyAuthorizationContexts());

        // Test read-only users
        metrics.recordReadOnlyUser();
        assertEquals(1, metrics.getReadOnlyUsers());
    }

    @Test
    void testJwtMetrics() {
        // Test initial state
        assertEquals(0, metrics.getJwtGenerationCount());
        assertEquals(0, metrics.getJwtGenerationErrors());
        assertEquals(0, metrics.getAverageJwtSize());

        // Test JWT generation
        metrics.recordJwtGeneration(1000);
        assertEquals(1, metrics.getJwtGenerationCount());
        assertEquals(1000, metrics.getAverageJwtSize());

        metrics.recordJwtGeneration(2000);
        assertEquals(2, metrics.getJwtGenerationCount());
        assertEquals(1500, metrics.getAverageJwtSize());

        // Test JWT generation errors
        metrics.recordJwtGenerationError();
        assertEquals(1, metrics.getJwtGenerationErrors());
    }

    @Test
    void testCacheHitRatioCalculation() {
        // Test with no data
        assertEquals(0.0, metrics.getCacheHitRatio());

        // Test with only hits
        metrics.recordCacheHit();
        metrics.recordCacheHit();
        assertEquals(100.0, metrics.getCacheHitRatio());

        // Test with only misses
        metrics.resetMetrics();
        metrics.recordCacheMiss();
        metrics.recordCacheMiss();
        assertEquals(0.0, metrics.getCacheHitRatio());

        // Test with mixed hits and misses
        metrics.resetMetrics();
        metrics.recordCacheHit();
        metrics.recordCacheHit();
        metrics.recordCacheHit();
        metrics.recordCacheMiss();
        assertEquals(75.0, metrics.getCacheHitRatio());
    }

    @Test
    void testDatabaseErrorRatioCalculation() {
        // Test with no data
        assertEquals(0.0, metrics.getDatabaseErrorRatio());

        // Test with only successful queries
        metrics.recordDatabaseQuery();
        metrics.recordDatabaseQuery();
        assertEquals(0.0, metrics.getDatabaseErrorRatio());

        // Test with only errors
        metrics.resetMetrics();
        metrics.recordDatabaseQuery();
        metrics.recordDatabaseError();
        assertEquals(100.0, metrics.getDatabaseErrorRatio());

        // Test with mixed success and errors
        metrics.resetMetrics();
        metrics.recordDatabaseQuery();
        metrics.recordDatabaseQuery();
        metrics.recordDatabaseQuery();
        metrics.recordDatabaseQuery();
        metrics.recordDatabaseError();
        assertEquals(25.0, metrics.getDatabaseErrorRatio());
    }

    @Test
    void testAverageJwtSizeCalculation() {
        // Test with single JWT
        metrics.recordJwtGeneration(1000);
        assertEquals(1000, metrics.getAverageJwtSize());

        // Test with multiple JWTs
        metrics.recordJwtGeneration(2000);
        assertEquals(1500, metrics.getAverageJwtSize());

        metrics.recordJwtGeneration(3000);
        assertEquals(2000, metrics.getAverageJwtSize());

        // Test with varying sizes
        metrics.resetMetrics();
        metrics.recordJwtGeneration(500);
        metrics.recordJwtGeneration(1500);
        assertEquals(1000, metrics.getAverageJwtSize());
    }

    @Test
    void testMetricsReset() {
        // Set up some metrics
        metrics.recordCacheHit();
        metrics.recordCacheMiss();
        metrics.recordCacheError();
        metrics.recordDatabaseQuery();
        metrics.recordDatabaseError();
        metrics.recordDatabaseFallback();
        metrics.recordResponseTime(100);
        metrics.recordEmptyAuthorizationContext();
        metrics.recordReadOnlyUser();
        metrics.recordJwtGeneration(1000);
        metrics.recordJwtGenerationError();

        // Verify metrics are set
        assertTrue(metrics.getCacheHits() > 0);
        assertTrue(metrics.getTotalRequests() > 0);
        assertTrue(metrics.getJwtGenerationCount() > 0);

        // Reset metrics
        metrics.resetMetrics();

        // Verify all metrics are reset
        assertEquals(0, metrics.getCacheHits());
        assertEquals(0, metrics.getCacheMisses());
        assertEquals(0, metrics.getCacheErrors());
        assertEquals(0, metrics.getDatabaseQueries());
        assertEquals(0, metrics.getDatabaseErrors());
        assertEquals(0, metrics.getDatabaseFallbacks());
        assertEquals(0, metrics.getTotalRequests());
        assertEquals(0, metrics.getAverageResponseTime());
        assertEquals(0, metrics.getEmptyAuthorizationContexts());
        assertEquals(0, metrics.getReadOnlyUsers());
        assertEquals(0, metrics.getJwtGenerationCount());
        assertEquals(0, metrics.getJwtGenerationErrors());
        assertEquals(0, metrics.getAverageJwtSize());
    }

    @Test
    void testLogMetricsSummary() {
        // Set up some metrics
        metrics.recordCacheHit();
        metrics.recordCacheHit();
        metrics.recordCacheMiss();
        metrics.recordDatabaseQuery();
        metrics.recordDatabaseQuery();
        metrics.recordDatabaseError();
        metrics.recordResponseTime(150);
        metrics.recordResponseTime(250);
        metrics.recordEmptyAuthorizationContext();
        metrics.recordReadOnlyUser();
        metrics.recordJwtGeneration(1200);
        metrics.recordJwtGeneration(1800);

        // This should not throw an exception
        assertDoesNotThrow(() -> {
            metrics.logMetricsSummary();
        });
    }

    @Test
    void testSlowOperationDetection() {
        // Test that slow operations are properly recorded
        // (This would be verified through log output in a real scenario)
        
        // Record a slow operation (> 1000ms)
        assertDoesNotThrow(() -> {
            metrics.recordResponseTime(1500);
        });
        
        assertEquals(1, metrics.getTotalRequests());
        assertEquals(1500, metrics.getAverageResponseTime());
    }

    @Test
    void testLargeJwtDetection() {
        // Test that large JWT tokens are properly recorded
        // (This would be verified through log output in a real scenario)
        
        // Record a large JWT (> 8192 bytes)
        assertDoesNotThrow(() -> {
            metrics.recordJwtGeneration(10000);
        });
        
        assertEquals(1, metrics.getJwtGenerationCount());
        assertEquals(10000, metrics.getAverageJwtSize());
    }

    @Test
    void testConcurrentAccess() throws InterruptedException {
        // Test that metrics are thread-safe
        int numThreads = 10;
        int operationsPerThread = 100;
        
        Thread[] threads = new Thread[numThreads];
        
        for (int i = 0; i < numThreads; i++) {
            threads[i] = new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    metrics.recordCacheHit();
                    metrics.recordCacheMiss();
                    metrics.recordDatabaseQuery();
                    metrics.recordResponseTime(100);
                    metrics.recordJwtGeneration(1000);
                }
            });
        }
        
        // Start all threads
        for (Thread thread : threads) {
            thread.start();
        }
        
        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join();
        }
        
        // Verify final counts
        assertEquals(numThreads * operationsPerThread, metrics.getCacheHits());
        assertEquals(numThreads * operationsPerThread, metrics.getCacheMisses());
        assertEquals(numThreads * operationsPerThread, metrics.getDatabaseQueries());
        assertEquals(numThreads * operationsPerThread, metrics.getTotalRequests());
        assertEquals(numThreads * operationsPerThread, metrics.getJwtGenerationCount());
        
        // Verify ratios
        assertEquals(50.0, metrics.getCacheHitRatio());
        assertEquals(0.0, metrics.getDatabaseErrorRatio());
        assertEquals(100, metrics.getAverageResponseTime());
        assertEquals(1000, metrics.getAverageJwtSize());
    }

    @Test
    void testEdgeCases() {
        // Test division by zero scenarios
        assertEquals(0.0, metrics.getCacheHitRatio());
        assertEquals(0.0, metrics.getDatabaseErrorRatio());
        
        // Test with zero response times
        metrics.recordResponseTime(0);
        assertEquals(0, metrics.getAverageResponseTime());
        
        // Test with zero JWT size
        metrics.recordJwtGeneration(0);
        assertEquals(0, metrics.getAverageJwtSize());
    }
}