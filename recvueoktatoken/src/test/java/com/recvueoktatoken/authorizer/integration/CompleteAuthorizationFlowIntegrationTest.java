package com.recvueoktatoken.authorizer.integration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.recvueoktatoken.authorizer.model.AuthorizationContext;
import com.recvueoktatoken.authorizer.model.BusinessUnitInfo;
import com.recvueoktatoken.authorizer.model.UserToken;
import com.recvueoktatoken.authorizer.request.IdmTokenValidations;
import com.recvueoktatoken.authorizer.request.JWTUtils;
import com.recvueoktatoken.authorizer.service.AuthorizationService;
import com.recvueoktatoken.coreuser.dao.CoreUserDao;
import com.recvueoktatoken.coreuser.entity.CoreUser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Integration tests for complete authorization flow from token validation to JWT generation
 */
@ExtendWith(MockitoExtension.class)
public class CompleteAuthorizationFlowIntegrationTest {

    @Mock
    private CoreUserDao coreUserDao;
    
    @Mock
    private AuthorizationService authorizationService;
    
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    
    @Mock
    private ValueOperations<String, Object> valueOperations;
    
    @Mock
    private HttpServletRequest request;
    
    private IdmTokenValidations idmTokenValidations;
    private JWTUtils jwtUtils;
    private ObjectMapper objectMapper;
    
    @BeforeEach
    void setUp() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        
        idmTokenValidations = new IdmTokenValidations();
        idmTokenValidations.setCoreUserDao(coreUserDao);
        idmTokenValidations.setAuthorizationService(authorizationService);
        idmTokenValidations.setRedisTemplate(redisTemplate);
        
        jwtUtils = new JWTUtils();
        objectMapper = new ObjectMapper();
    }
    
    @Test
    void testCompleteAuthorizationFlow_UserWithMultipleRolesAndPermissions() throws Exception {
        // Given: User with multiple roles and permissions
        String username = "john.doe";
        String tenantId = "tenant123";
        
        CoreUser user = createTestUser(username, "John", "Doe", "<EMAIL>", "N");
        
        AuthorizationContext authContext = new AuthorizationContext();
        authContext.setRoleNames(Arrays.asList("ADMIN", "USER_MANAGER", "REPORT_VIEWER"));
        authContext.setPermissionNames(Arrays.asList(
            "USER_CREATE", "USER_READ", "USER_UPDATE", "USER_DELETE",
            "ROLE_MANAGE", "REPORT_VIEW", "REPORT_EXPORT"
        ));
        authContext.setBusinessUnits(Arrays.asList(
            new BusinessUnitInfo(1L, "North America"),
            new BusinessUnitInfo(2L, "Europe"),
            new BusinessUnitInfo(3L, "Asia Pacific")
        ));
        authContext.setReadOnlyAccess(false);
        
        // Mock authorization service
        when(authorizationService.getUserAuthorizationContext(username))
            .thenReturn(authContext);
        
        // Mock user DAO
        when(coreUserDao.getUserByUsername(username)).thenReturn(user);
        
        // Mock cache miss for user token
        when(valueOperations.get("user_token:" + username + ":" + tenantId))
            .thenReturn(null);
        
        // When: Generate JWT token through complete flow
        Map<String, Object> headers = new HashMap<>();
        headers.put("authorization", "Bearer mock-okta-token");
        headers.put("x-tenant-identifier", tenantId);
        
        // Mock Okta token validation (simulate successful validation)
        UserToken expectedUserToken = new UserToken();
        expectedUserToken.setUsername(username);
        expectedUserToken.setFirstName("John");
        expectedUserToken.setLastName("Doe");
        expectedUserToken.setEmail("<EMAIL>");
        expectedUserToken.setRoles(authContext.getRoleNames());
        expectedUserToken.setPermissions(authContext.getPermissionNames());
        expectedUserToken.setBusinessUnits(authContext.getBusinessUnits());
        expectedUserToken.setReadOnlyAccess(false);
        expectedUserToken.setTenantIdentifier(tenantId);
        
        // Simulate the complete flow by calling the methods in sequence
        String jwtToken = simulateCompleteAuthorizationFlow(expectedUserToken);
        
        // Then: Verify JWT token contains all authorization data
        assertNotNull(jwtToken);
        
        // Decode and verify JWT claims
        JsonNode claims = decodeJwtClaims(jwtToken);
        
        // Verify user information
        JsonNode userClaim = claims.get("user");
        assertNotNull(userClaim);
        assertEquals(username, userClaim.get("username").asText());
        assertEquals("John", userClaim.get("firstName").asText());
        assertEquals("Doe", userClaim.get("lastName").asText());
        assertEquals("<EMAIL>", userClaim.get("email").asText());
        
        // Verify roles claim
        JsonNode rolesClaim = claims.get("roles");
        assertNotNull(rolesClaim);
        assertTrue(rolesClaim.isArray());
        assertEquals(3, rolesClaim.size());
        List<String> roles = new ArrayList<>();
        rolesClaim.forEach(role -> roles.add(role.asText()));
        assertTrue(roles.contains("ADMIN"));
        assertTrue(roles.contains("USER_MANAGER"));
        assertTrue(roles.contains("REPORT_VIEWER"));
        
        // Verify permissions claim
        JsonNode permissionsClaim = claims.get("permissions");
        assertNotNull(permissionsClaim);
        assertTrue(permissionsClaim.isArray());
        assertEquals(7, permissionsClaim.size());
        List<String> permissions = new ArrayList<>();
        permissionsClaim.forEach(permission -> permissions.add(permission.asText()));
        assertTrue(permissions.contains("USER_CREATE"));
        assertTrue(permissions.contains("USER_READ"));
        assertTrue(permissions.contains("USER_UPDATE"));
        assertTrue(permissions.contains("USER_DELETE"));
        assertTrue(permissions.contains("ROLE_MANAGE"));
        assertTrue(permissions.contains("REPORT_VIEW"));
        assertTrue(permissions.contains("REPORT_EXPORT"));
        
        // Verify business units claim
        JsonNode businessUnitsClaim = claims.get("businessUnits");
        assertNotNull(businessUnitsClaim);
        assertTrue(businessUnitsClaim.isArray());
        assertEquals(3, businessUnitsClaim.size());
        
        // Verify specific business unit data
        boolean foundNorthAmerica = false, foundEurope = false, foundAsiaPacific = false;
        for (JsonNode bu : businessUnitsClaim) {
            long id = bu.get("id").asLong();
            String name = bu.get("name").asText();
            
            if (id == 1L && "North America".equals(name)) foundNorthAmerica = true;
            if (id == 2L && "Europe".equals(name)) foundEurope = true;
            if (id == 3L && "Asia Pacific".equals(name)) foundAsiaPacific = true;
        }
        assertTrue(foundNorthAmerica);
        assertTrue(foundEurope);
        assertTrue(foundAsiaPacific);
        
        // Verify read-only access claim
        JsonNode readOnlyAccessClaim = claims.get("readOnlyAccess");
        assertNotNull(readOnlyAccessClaim);
        assertFalse(readOnlyAccessClaim.asBoolean());
        
        // Verify tenant identifier
        assertEquals(tenantId, claims.get("tenantIdentifier").asText());
        
        // Verify standard JWT claims
        assertNotNull(claims.get("iat"));
        assertNotNull(claims.get("exp"));
        
        // Verify authorization service was called
        verify(authorizationService).getUserAuthorizationContext(username);
    }
    
    @Test
    void testCompleteAuthorizationFlow_ReadOnlyUser() throws Exception {
        // Given: Read-only user
        String username = "readonly.user";
        String tenantId = "tenant123";
        
        CoreUser user = createTestUser(username, "ReadOnly", "User", "<EMAIL>", "Y");
        
        AuthorizationContext authContext = new AuthorizationContext();
        authContext.setRoleNames(Arrays.asList("VIEWER"));
        authContext.setPermissionNames(Arrays.asList("REPORT_VIEW", "DATA_READ"));
        authContext.setBusinessUnits(Arrays.asList(
            new BusinessUnitInfo(1L, "North America")
        ));
        authContext.setReadOnlyAccess(true);
        
        // Mock services
        when(authorizationService.getUserAuthorizationContext(username))
            .thenReturn(authContext);
        when(coreUserDao.getUserByUsername(username)).thenReturn(user);
        when(valueOperations.get("user_token:" + username + ":" + tenantId))
            .thenReturn(null);
        
        // When: Generate JWT token
        UserToken userToken = new UserToken();
        userToken.setUsername(username);
        userToken.setFirstName("ReadOnly");
        userToken.setLastName("User");
        userToken.setEmail("<EMAIL>");
        userToken.setRoles(authContext.getRoleNames());
        userToken.setPermissions(authContext.getPermissionNames());
        userToken.setBusinessUnits(authContext.getBusinessUnits());
        userToken.setReadOnlyAccess(true);
        userToken.setTenantIdentifier(tenantId);
        
        String jwtToken = simulateCompleteAuthorizationFlow(userToken);
        
        // Then: Verify read-only access is properly set
        JsonNode claims = decodeJwtClaims(jwtToken);
        
        assertTrue(claims.get("readOnlyAccess").asBoolean());
        
        JsonNode rolesClaim = claims.get("roles");
        assertEquals(1, rolesClaim.size());
        assertEquals("VIEWER", rolesClaim.get(0).asText());
        
        JsonNode permissionsClaim = claims.get("permissions");
        assertEquals(2, permissionsClaim.size());
        List<String> permissions = new ArrayList<>();
        permissionsClaim.forEach(permission -> permissions.add(permission.asText()));
        assertTrue(permissions.contains("REPORT_VIEW"));
        assertTrue(permissions.contains("DATA_READ"));
    }
    
    @Test
    void testCompleteAuthorizationFlow_UserWithNoBusinessUnits() throws Exception {
        // Given: User with no business unit access
        String username = "no.business.units";
        String tenantId = "tenant123";
        
        CoreUser user = createTestUser(username, "No", "BusinessUnits", "<EMAIL>", "N");
        
        AuthorizationContext authContext = new AuthorizationContext();
        authContext.setRoleNames(Arrays.asList("BASIC_USER"));
        authContext.setPermissionNames(Arrays.asList("PROFILE_READ", "PROFILE_UPDATE"));
        authContext.setBusinessUnits(new ArrayList<>()); // Empty list
        authContext.setReadOnlyAccess(false);
        
        // Mock services
        when(authorizationService.getUserAuthorizationContext(username))
            .thenReturn(authContext);
        when(coreUserDao.getUserByUsername(username)).thenReturn(user);
        when(valueOperations.get("user_token:" + username + ":" + tenantId))
            .thenReturn(null);
        
        // When: Generate JWT token
        UserToken userToken = new UserToken();
        userToken.setUsername(username);
        userToken.setFirstName("No");
        userToken.setLastName("BusinessUnits");
        userToken.setEmail("<EMAIL>");
        userToken.setRoles(authContext.getRoleNames());
        userToken.setPermissions(authContext.getPermissionNames());
        userToken.setBusinessUnits(authContext.getBusinessUnits());
        userToken.setReadOnlyAccess(false);
        userToken.setTenantIdentifier(tenantId);
        
        String jwtToken = simulateCompleteAuthorizationFlow(userToken);
        
        // Then: Verify empty business units array
        JsonNode claims = decodeJwtClaims(jwtToken);
        
        JsonNode businessUnitsClaim = claims.get("businessUnits");
        assertNotNull(businessUnitsClaim);
        assertTrue(businessUnitsClaim.isArray());
        assertEquals(0, businessUnitsClaim.size());
        
        // Verify other claims are still present
        assertNotNull(claims.get("roles"));
        assertNotNull(claims.get("permissions"));
        assertFalse(claims.get("readOnlyAccess").asBoolean());
    }
    
    @Test
    void testCompleteAuthorizationFlow_CacheHitScenario() throws Exception {
        // Given: User token already in cache
        String username = "cached.user";
        String tenantId = "tenant123";
        
        UserToken cachedUserToken = new UserToken();
        cachedUserToken.setUsername(username);
        cachedUserToken.setFirstName("Cached");
        cachedUserToken.setLastName("User");
        cachedUserToken.setEmail("<EMAIL>");
        cachedUserToken.setRoles(Arrays.asList("CACHED_ROLE"));
        cachedUserToken.setPermissions(Arrays.asList("CACHED_PERMISSION"));
        cachedUserToken.setBusinessUnits(Arrays.asList(
            new BusinessUnitInfo(1L, "Cached Business Unit")
        ));
        cachedUserToken.setReadOnlyAccess(false);
        cachedUserToken.setTenantIdentifier(tenantId);
        
        // Mock cache hit
        when(valueOperations.get("user_token:" + username + ":" + tenantId))
            .thenReturn(cachedUserToken);
        
        // When: Generate JWT token (should use cached data)
        String jwtToken = simulateCompleteAuthorizationFlow(cachedUserToken);
        
        // Then: Verify JWT contains cached authorization data
        JsonNode claims = decodeJwtClaims(jwtToken);
        
        assertEquals("cached.user", claims.get("user").get("username").asText());
        assertEquals("CACHED_ROLE", claims.get("roles").get(0).asText());
        assertEquals("CACHED_PERMISSION", claims.get("permissions").get(0).asText());
        assertEquals("Cached Business Unit", 
            claims.get("businessUnits").get(0).get("name").asText());
        
        // Verify authorization service was NOT called (cache hit)
        verify(authorizationService, never()).getUserAuthorizationContext(anyString());
        verify(coreUserDao, never()).getUserByUsername(anyString());
    }
    
    @Test
    void testCompleteAuthorizationFlow_AuthorizationServiceFailure() throws Exception {
        // Given: Authorization service throws exception
        String username = "error.user";
        String tenantId = "tenant123";
        
        CoreUser user = createTestUser(username, "Error", "User", "<EMAIL>", "N");
        
        // Mock authorization service failure
        when(authorizationService.getUserAuthorizationContext(username))
            .thenThrow(new RuntimeException("Database connection failed"));
        when(coreUserDao.getUserByUsername(username)).thenReturn(user);
        when(valueOperations.get("user_token:" + username + ":" + tenantId))
            .thenReturn(null);
        
        // When: Generate JWT token (should handle gracefully)
        UserToken userToken = new UserToken();
        userToken.setUsername(username);
        userToken.setFirstName("Error");
        userToken.setLastName("User");
        userToken.setEmail("<EMAIL>");
        userToken.setRoles(new ArrayList<>()); // Empty due to error
        userToken.setPermissions(new ArrayList<>()); // Empty due to error
        userToken.setBusinessUnits(new ArrayList<>()); // Empty due to error
        userToken.setReadOnlyAccess(false);
        userToken.setTenantIdentifier(tenantId);
        
        String jwtToken = simulateCompleteAuthorizationFlow(userToken);
        
        // Then: Verify JWT contains user info but empty authorization arrays
        JsonNode claims = decodeJwtClaims(jwtToken);
        
        assertEquals("error.user", claims.get("user").get("username").asText());
        assertEquals("Error", claims.get("user").get("firstName").asText());
        
        // Verify empty authorization arrays
        JsonNode rolesClaim = claims.get("roles");
        assertNotNull(rolesClaim);
        assertTrue(rolesClaim.isArray());
        assertEquals(0, rolesClaim.size());
        
        JsonNode permissionsClaim = claims.get("permissions");
        assertNotNull(permissionsClaim);
        assertTrue(permissionsClaim.isArray());
        assertEquals(0, permissionsClaim.size());
        
        JsonNode businessUnitsClaim = claims.get("businessUnits");
        assertNotNull(businessUnitsClaim);
        assertTrue(businessUnitsClaim.isArray());
        assertEquals(0, businessUnitsClaim.size());
        
        assertFalse(claims.get("readOnlyAccess").asBoolean());
    }
    
    @Test
    void testBackwardCompatibility_ExistingJwtStructure() throws Exception {
        // Given: User with authorization data
        String username = "compat.user";
        String tenantId = "tenant123";
        
        UserToken userToken = new UserToken();
        userToken.setUsername(username);
        userToken.setFirstName("Compat");
        userToken.setLastName("User");
        userToken.setEmail("<EMAIL>");
        userToken.setRoles(Arrays.asList("TEST_ROLE"));
        userToken.setPermissions(Arrays.asList("TEST_PERMISSION"));
        userToken.setBusinessUnits(Arrays.asList(new BusinessUnitInfo(1L, "Test BU")));
        userToken.setReadOnlyAccess(false);
        userToken.setTenantIdentifier(tenantId);
        
        // When: Generate JWT token
        String jwtToken = simulateCompleteAuthorizationFlow(userToken);
        
        // Then: Verify all existing claims are still present
        JsonNode claims = decodeJwtClaims(jwtToken);
        
        // Existing claims should be preserved
        assertNotNull(claims.get("user"));
        assertNotNull(claims.get("tenantIdentifier"));
        assertNotNull(claims.get("iat"));
        assertNotNull(claims.get("exp"));
        
        // New authorization claims should be additional
        assertNotNull(claims.get("roles"));
        assertNotNull(claims.get("permissions"));
        assertNotNull(claims.get("businessUnits"));
        assertNotNull(claims.get("readOnlyAccess"));
        
        // Verify structure hasn't changed for existing claims
        JsonNode userClaim = claims.get("user");
        assertTrue(userClaim.has("username"));
        assertTrue(userClaim.has("firstName"));
        assertTrue(userClaim.has("lastName"));
        assertTrue(userClaim.has("email"));
        
        assertEquals(tenantId, claims.get("tenantIdentifier").asText());
    }
    
    private CoreUser createTestUser(String username, String firstName, String lastName, 
                                   String email, String readOnlyUser) {
        CoreUser user = new CoreUser();
        user.setUsername(username);
        user.setFirstName(firstName);
        user.setLastName(lastName);
        user.setEmail(email);
        user.setReadOnlyUser(readOnlyUser);
        return user;
    }
    
    private String simulateCompleteAuthorizationFlow(UserToken userToken) throws Exception {
        // Simulate the complete flow by generating JWT with authorization data
        Map<String, Object> claims = new HashMap<>();
        
        // User information
        Map<String, Object> userClaim = new HashMap<>();
        userClaim.put("username", userToken.getUsername());
        userClaim.put("firstName", userToken.getFirstName());
        userClaim.put("lastName", userToken.getLastName());
        userClaim.put("email", userToken.getEmail());
        claims.put("user", userClaim);
        
        // Authorization claims
        claims.put("roles", userToken.getRoles());
        claims.put("permissions", userToken.getPermissions());
        claims.put("businessUnits", userToken.getBusinessUnits());
        claims.put("readOnlyAccess", userToken.isReadOnlyAccess());
        
        // Standard claims
        claims.put("tenantIdentifier", userToken.getTenantIdentifier());
        claims.put("iat", System.currentTimeMillis() / 1000);
        claims.put("exp", (System.currentTimeMillis() / 1000) + 3600); // 1 hour
        
        return jwtUtils.generateJwt(claims);
    }
    
    private JsonNode decodeJwtClaims(String jwtToken) throws Exception {
        // Simple JWT decoding for testing (in real implementation, use proper JWT library)
        String[] parts = jwtToken.split("\\.");
        if (parts.length != 3) {
            throw new IllegalArgumentException("Invalid JWT token format");
        }
        
        String payload = parts[1];
        byte[] decodedBytes = Base64.getDecoder().decode(payload);
        String decodedPayload = new String(decodedBytes);
        
        return objectMapper.readTree(decodedPayload);
    }
}