package com.recvueoktatoken.authorizer.integration;

import com.recvueoktatoken.authorizer.model.AuthorizationContext;
import com.recvueoktatoken.authorizer.model.BusinessUnitInfo;
import com.recvueoktatoken.authorizer.model.UserToken;
import com.recvueoktatoken.authorizer.service.impl.AuthorizationServiceImpl;
import com.recvueoktatoken.coreuser.dao.CoreUserDao;
import com.recvueoktatoken.coreuser.entity.CoreUser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Integration tests for Redis caching in authorization flow
 */
@ExtendWith(MockitoExtension.class)
public class RedisAuthorizationCacheIntegrationTest {

    @Mock
    private CoreUserDao coreUserDao;
    
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    
    @Mock
    private ValueOperations<String, Object> valueOperations;
    
    private AuthorizationServiceImpl authorizationService;
    
    @BeforeEach
    void setUp() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        
        authorizationService = new AuthorizationServiceImpl();
        authorizationService.setCoreUserDao(coreUserDao);
        authorizationService.setRedisTemplate(redisTemplate);
    }
    
    @Test
    void testAuthorizationContextCaching_CacheMiss() throws Exception {
        // Given: No cached authorization context
        String username = "cache.miss.user";
        String cacheKey = "auth_context:" + username;
        
        when(valueOperations.get(cacheKey)).thenReturn(null);
        
        // Mock database response
        when(coreUserDao.getUserRoleNames(username))
            .thenReturn(Arrays.asList("ADMIN", "USER_MANAGER"));
        when(coreUserDao.getUserPermissionNames(username))
            .thenReturn(Arrays.asList("USER_CREATE", "USER_READ", "USER_UPDATE"));
        when(coreUserDao.getUserBusinessUnits(username))
            .thenReturn(Arrays.asList(new BusinessUnitInfo(1L, "Test BU")));
        when(coreUserDao.isReadOnlyUser(username))
            .thenReturn(false);
        
        // When: Get authorization context
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(username);
        
        // Then: Verify database was queried and result cached
        assertNotNull(result);
        assertEquals(Arrays.asList("ADMIN", "USER_MANAGER"), result.getRoleNames());
        assertEquals(Arrays.asList("USER_CREATE", "USER_READ", "USER_UPDATE"), result.getPermissionNames());
        assertEquals(1, result.getBusinessUnits().size());
        assertEquals("Test BU", result.getBusinessUnits().get(0).getName());
        assertFalse(result.isReadOnlyAccess());
        
        // Verify database methods were called
        verify(coreUserDao).getUserRoleNames(username);
        verify(coreUserDao).getUserPermissionNames(username);
        verify(coreUserDao).getUserBusinessUnits(username);
        verify(coreUserDao).isReadOnlyUser(username);
        
        // Verify caching occurred
        verify(valueOperations).set(eq(cacheKey), any(AuthorizationContext.class), 
                                   eq(58L), eq(TimeUnit.MINUTES));
    }
    
    @Test
    void testAuthorizationContextCaching_CacheHit() throws Exception {
        // Given: Cached authorization context exists
        String username = "cache.hit.user";
        String cacheKey = "auth_context:" + username;
        
        AuthorizationContext cachedContext = new AuthorizationContext();
        cachedContext.setRoleNames(Arrays.asList("CACHED_ROLE"));
        cachedContext.setPermissionNames(Arrays.asList("CACHED_PERMISSION"));
        cachedContext.setBusinessUnits(Arrays.asList(new BusinessUnitInfo(2L, "Cached BU")));
        cachedContext.setReadOnlyAccess(true);
        
        when(valueOperations.get(cacheKey)).thenReturn(cachedContext);
        
        // When: Get authorization context
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(username);
        
        // Then: Verify cached data is returned
        assertNotNull(result);
        assertEquals(Arrays.asList("CACHED_ROLE"), result.getRoleNames());
        assertEquals(Arrays.asList("CACHED_PERMISSION"), result.getPermissionNames());
        assertEquals(1, result.getBusinessUnits().size());
        assertEquals("Cached BU", result.getBusinessUnits().get(0).getName());
        assertTrue(result.isReadOnlyAccess());
        
        // Verify database was NOT queried
        verify(coreUserDao, never()).getUserRoleNames(anyString());
        verify(coreUserDao, never()).getUserPermissionNames(anyString());
        verify(coreUserDao, never()).getUserBusinessUnits(anyString());
        verify(coreUserDao, never()).isReadOnlyUser(anyString());
        
        // Verify no additional caching occurred
        verify(valueOperations, never()).set(anyString(), any(), anyLong(), any(TimeUnit.class));
    }
    
    @Test
    void testUserTokenCaching_CompleteFlow() throws Exception {
        // Given: User token caching scenario
        String username = "token.cache.user";
        String tenantId = "tenant123";
        String tokenCacheKey = "user_token:" + username + ":" + tenantId;
        
        // Mock no cached user token initially
        when(valueOperations.get(tokenCacheKey)).thenReturn(null);
        
        // Mock user data
        CoreUser user = new CoreUser();
        user.setUsername(username);
        user.setFirstName("Token");
        user.setLastName("Cache");
        user.setEmail("<EMAIL>");
        user.setReadOnlyUser("N");
        
        when(coreUserDao.getUserByUsername(username)).thenReturn(user);
        
        // Mock authorization context (cache miss)
        String authCacheKey = "auth_context:" + username;
        when(valueOperations.get(authCacheKey)).thenReturn(null);
        
        when(coreUserDao.getUserRoleNames(username))
            .thenReturn(Arrays.asList("TOKEN_ROLE"));
        when(coreUserDao.getUserPermissionNames(username))
            .thenReturn(Arrays.asList("TOKEN_PERMISSION"));
        when(coreUserDao.getUserBusinessUnits(username))
            .thenReturn(Arrays.asList(new BusinessUnitInfo(3L, "Token BU")));
        when(coreUserDao.isReadOnlyUser(username))
            .thenReturn(false);
        
        // When: Simulate complete token creation and caching
        AuthorizationContext authContext = authorizationService.getUserAuthorizationContext(username);
        
        UserToken userToken = new UserToken();
        userToken.setUsername(user.getUsername());
        userToken.setFirstName(user.getFirstName());
        userToken.setLastName(user.getLastName());
        userToken.setEmail(user.getEmail());
        userToken.setRoles(authContext.getRoleNames());
        userToken.setPermissions(authContext.getPermissionNames());
        userToken.setBusinessUnits(authContext.getBusinessUnits());
        userToken.setReadOnlyAccess(authContext.isReadOnlyAccess());
        userToken.setTenantIdentifier(tenantId);
        
        // Cache the user token
        valueOperations.set(tokenCacheKey, userToken, 58L, TimeUnit.MINUTES);
        
        // Then: Verify both authorization context and user token were cached
        verify(valueOperations).set(eq(authCacheKey), any(AuthorizationContext.class), 
                                   eq(58L), eq(TimeUnit.MINUTES));
        verify(valueOperations).set(eq(tokenCacheKey), eq(userToken), 
                                   eq(58L), eq(TimeUnit.MINUTES));
        
        // Verify user token contains authorization data
        assertEquals(Arrays.asList("TOKEN_ROLE"), userToken.getRoles());
        assertEquals(Arrays.asList("TOKEN_PERMISSION"), userToken.getPermissions());
        assertEquals(1, userToken.getBusinessUnits().size());
        assertEquals("Token BU", userToken.getBusinessUnits().get(0).getName());
        assertFalse(userToken.isReadOnlyAccess());
    }
    
    @Test
    void testCacheInvalidation() throws Exception {
        // Given: User with cached authorization context
        String username = "invalidation.user";
        String authCacheKey = "auth_context:" + username;
        
        AuthorizationContext cachedContext = new AuthorizationContext();
        cachedContext.setRoleNames(Arrays.asList("OLD_ROLE"));
        cachedContext.setPermissionNames(Arrays.asList("OLD_PERMISSION"));
        cachedContext.setBusinessUnits(Arrays.asList(new BusinessUnitInfo(1L, "Old BU")));
        cachedContext.setReadOnlyAccess(false);
        
        when(valueOperations.get(authCacheKey)).thenReturn(cachedContext);
        
        // When: Invalidate cache
        authorizationService.invalidateUserAuthorizationCache(username);
        
        // Then: Verify cache deletion
        verify(redisTemplate).delete(authCacheKey);
    }
    
    @Test
    void testCacheFailureGracefulHandling() throws Exception {
        // Given: Redis cache is unavailable
        String username = "cache.failure.user";
        String cacheKey = "auth_context:" + username;
        
        // Mock cache operations to throw exceptions
        when(valueOperations.get(cacheKey))
            .thenThrow(new RuntimeException("Redis connection failed"));
        doThrow(new RuntimeException("Redis connection failed"))
            .when(valueOperations).set(anyString(), any(), anyLong(), any(TimeUnit.class));
        
        // Mock database response
        when(coreUserDao.getUserRoleNames(username))
            .thenReturn(Arrays.asList("FALLBACK_ROLE"));
        when(coreUserDao.getUserPermissionNames(username))
            .thenReturn(Arrays.asList("FALLBACK_PERMISSION"));
        when(coreUserDao.getUserBusinessUnits(username))
            .thenReturn(Arrays.asList(new BusinessUnitInfo(1L, "Fallback BU")));
        when(coreUserDao.isReadOnlyUser(username))
            .thenReturn(false);
        
        // When: Get authorization context (should fallback to database)
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(username);
        
        // Then: Verify database fallback works
        assertNotNull(result);
        assertEquals(Arrays.asList("FALLBACK_ROLE"), result.getRoleNames());
        assertEquals(Arrays.asList("FALLBACK_PERMISSION"), result.getPermissionNames());
        assertEquals(1, result.getBusinessUnits().size());
        assertEquals("Fallback BU", result.getBusinessUnits().get(0).getName());
        assertFalse(result.isReadOnlyAccess());
        
        // Verify database was queried
        verify(coreUserDao).getUserRoleNames(username);
        verify(coreUserDao).getUserPermissionNames(username);
        verify(coreUserDao).getUserBusinessUnits(username);
        verify(coreUserDao).isReadOnlyUser(username);
    }
    
    @Test
    void testCacheKeyGeneration_MultiTenant() throws Exception {
        // Given: Multiple tenants for same user
        String username = "multi.tenant";
        String tenant1 = "tenant_a";
        String tenant2 = "tenant_b";
        
        String authCacheKey = "auth_context:" + username;
        String tokenCacheKey1 = "user_token:" + username + ":" + tenant1;
        String tokenCacheKey2 = "user_token:" + username + ":" + tenant2;
        
        // Mock authorization context (shared across tenants)
        AuthorizationContext authContext = new AuthorizationContext();
        authContext.setRoleNames(Arrays.asList("MULTI_ROLE"));
        authContext.setPermissionNames(Arrays.asList("MULTI_PERMISSION"));
        authContext.setBusinessUnits(Arrays.asList(new BusinessUnitInfo(1L, "Multi BU")));
        authContext.setReadOnlyAccess(false);
        
        when(valueOperations.get(authCacheKey)).thenReturn(authContext);
        when(valueOperations.get(tokenCacheKey1)).thenReturn(null);
        when(valueOperations.get(tokenCacheKey2)).thenReturn(null);
        
        // When: Create user tokens for different tenants
        UserToken token1 = new UserToken();
        token1.setUsername(username);
        token1.setTenantIdentifier(tenant1);
        token1.setRoles(authContext.getRoleNames());
        token1.setPermissions(authContext.getPermissionNames());
        token1.setBusinessUnits(authContext.getBusinessUnits());
        token1.setReadOnlyAccess(authContext.isReadOnlyAccess());
        
        UserToken token2 = new UserToken();
        token2.setUsername(username);
        token2.setTenantIdentifier(tenant2);
        token2.setRoles(authContext.getRoleNames());
        token2.setPermissions(authContext.getPermissionNames());
        token2.setBusinessUnits(authContext.getBusinessUnits());
        token2.setReadOnlyAccess(authContext.isReadOnlyAccess());
        
        // Cache tokens separately
        valueOperations.set(tokenCacheKey1, token1, 58L, TimeUnit.MINUTES);
        valueOperations.set(tokenCacheKey2, token2, 58L, TimeUnit.MINUTES);
        
        // Then: Verify separate cache keys for different tenants
        verify(valueOperations).set(eq(tokenCacheKey1), eq(token1), 
                                   eq(58L), eq(TimeUnit.MINUTES));
        verify(valueOperations).set(eq(tokenCacheKey2), eq(token2), 
                                   eq(58L), eq(TimeUnit.MINUTES));
        
        // Verify authorization context is shared (single cache key)
        verify(valueOperations, times(2)).get(authCacheKey);
    }
    
    @Test
    void testCacheExpiration_ConsistentTiming() throws Exception {
        // Given: Authorization context and user token caching
        String username = "expiration.user";
        String tenantId = "tenant123";
        String authCacheKey = "auth_context:" + username;
        String tokenCacheKey = "user_token:" + username + ":" + tenantId;
        
        // Mock cache misses
        when(valueOperations.get(authCacheKey)).thenReturn(null);
        when(valueOperations.get(tokenCacheKey)).thenReturn(null);
        
        // Mock database responses
        when(coreUserDao.getUserRoleNames(username))
            .thenReturn(Arrays.asList("EXPIRATION_ROLE"));
        when(coreUserDao.getUserPermissionNames(username))
            .thenReturn(Arrays.asList("EXPIRATION_PERMISSION"));
        when(coreUserDao.getUserBusinessUnits(username))
            .thenReturn(Arrays.asList(new BusinessUnitInfo(1L, "Expiration BU")));
        when(coreUserDao.isReadOnlyUser(username))
            .thenReturn(false);
        
        CoreUser user = new CoreUser();
        user.setUsername(username);
        user.setFirstName("Expiration");
        user.setLastName("User");
        user.setEmail("<EMAIL>");
        user.setReadOnlyUser("N");
        
        when(coreUserDao.getUserByUsername(username)).thenReturn(user);
        
        // When: Process complete authorization flow
        AuthorizationContext authContext = authorizationService.getUserAuthorizationContext(username);
        
        UserToken userToken = new UserToken();
        userToken.setUsername(user.getUsername());
        userToken.setFirstName(user.getFirstName());
        userToken.setLastName(user.getLastName());
        userToken.setEmail(user.getEmail());
        userToken.setRoles(authContext.getRoleNames());
        userToken.setPermissions(authContext.getPermissionNames());
        userToken.setBusinessUnits(authContext.getBusinessUnits());
        userToken.setReadOnlyAccess(authContext.isReadOnlyAccess());
        userToken.setTenantIdentifier(tenantId);
        
        valueOperations.set(tokenCacheKey, userToken, 58L, TimeUnit.MINUTES);
        
        // Then: Verify both caches use same expiration time (58 minutes)
        verify(valueOperations).set(eq(authCacheKey), any(AuthorizationContext.class), 
                                   eq(58L), eq(TimeUnit.MINUTES));
        verify(valueOperations).set(eq(tokenCacheKey), eq(userToken), 
                                   eq(58L), eq(TimeUnit.MINUTES));
    }
    
    @Test
    void testCachePerformance_BulkOperations() throws Exception {
        // Given: Multiple users requiring authorization context
        String[] usernames = {"user1", "user2", "user3", "user4", "user5"};
        
        // Mock cache misses for all users
        for (String username : usernames) {
            String cacheKey = "auth_context:" + username;
            when(valueOperations.get(cacheKey)).thenReturn(null);
            
            // Mock database responses
            when(coreUserDao.getUserRoleNames(username))
                .thenReturn(Arrays.asList("BULK_ROLE_" + username));
            when(coreUserDao.getUserPermissionNames(username))
                .thenReturn(Arrays.asList("BULK_PERMISSION_" + username));
            when(coreUserDao.getUserBusinessUnits(username))
                .thenReturn(Arrays.asList(new BusinessUnitInfo(1L, "Bulk BU " + username)));
            when(coreUserDao.isReadOnlyUser(username))
                .thenReturn(false);
        }
        
        // When: Process authorization for all users
        for (String username : usernames) {
            AuthorizationContext result = authorizationService.getUserAuthorizationContext(username);
            assertNotNull(result);
        }
        
        // Then: Verify all users were cached
        for (String username : usernames) {
            String cacheKey = "auth_context:" + username;
            verify(valueOperations).set(eq(cacheKey), any(AuthorizationContext.class), 
                                       eq(58L), eq(TimeUnit.MINUTES));
        }
        
        // Verify database was queried for each user
        for (String username : usernames) {
            verify(coreUserDao).getUserRoleNames(username);
            verify(coreUserDao).getUserPermissionNames(username);
            verify(coreUserDao).getUserBusinessUnits(username);
            verify(coreUserDao).isReadOnlyUser(username);
        }
    }
}