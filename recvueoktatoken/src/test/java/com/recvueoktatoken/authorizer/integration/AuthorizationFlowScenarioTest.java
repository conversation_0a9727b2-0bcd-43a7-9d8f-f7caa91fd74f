package com.recvueoktatoken.authorizer.integration;

import com.recvueoktatoken.authorizer.model.AuthorizationContext;
import com.recvueoktatoken.authorizer.model.BusinessUnitInfo;
import com.recvueoktatoken.authorizer.model.UserToken;
import com.recvueoktatoken.authorizer.request.IdmTokenValidations;
import com.recvueoktatoken.authorizer.service.AuthorizationService;
import com.recvueoktatoken.coreuser.dao.CoreUserDao;
import com.recvueoktatoken.coreuser.entity.CoreUser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Integration tests for specific authorization flow scenarios
 */
@ExtendWith(MockitoExtension.class)
public class AuthorizationFlowScenarioTest {

    @Mock
    private CoreUserDao coreUserDao;
    
    @Mock
    private AuthorizationService authorizationService;
    
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    
    @Mock
    private ValueOperations<String, Object> valueOperations;
    
    private IdmTokenValidations idmTokenValidations;
    
    @BeforeEach
    void setUp() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        
        idmTokenValidations = new IdmTokenValidations();
        idmTokenValidations.setCoreUserDao(coreUserDao);
        idmTokenValidations.setAuthorizationService(authorizationService);
        idmTokenValidations.setRedisTemplate(redisTemplate);
    }
    
    @Test
    void testAuthorizationFlow_SuperAdminUser() throws Exception {
        // Given: Super admin user with extensive permissions
        String username = "super.admin";
        String tenantId = "tenant123";
        
        CoreUser user = createTestUser(username, "Super", "Admin", "<EMAIL>", "N");
        
        AuthorizationContext authContext = new AuthorizationContext();
        authContext.setRoleNames(Arrays.asList(
            "SUPER_ADMIN", "TENANT_ADMIN", "USER_MANAGER", "REPORT_ADMIN", "SYSTEM_ADMIN"
        ));
        authContext.setPermissionNames(Arrays.asList(
            "USER_CREATE", "USER_READ", "USER_UPDATE", "USER_DELETE",
            "ROLE_CREATE", "ROLE_READ", "ROLE_UPDATE", "ROLE_DELETE",
            "TENANT_MANAGE", "SYSTEM_CONFIG", "AUDIT_VIEW", "AUDIT_EXPORT",
            "REPORT_CREATE", "REPORT_READ", "REPORT_UPDATE", "REPORT_DELETE",
            "BUSINESS_UNIT_MANAGE", "PERMISSION_MANAGE"
        ));
        authContext.setBusinessUnits(Arrays.asList(
            new BusinessUnitInfo(1L, "Global"),
            new BusinessUnitInfo(2L, "North America"),
            new BusinessUnitInfo(3L, "Europe"),
            new BusinessUnitInfo(4L, "Asia Pacific"),
            new BusinessUnitInfo(5L, "Latin America")
        ));
        authContext.setReadOnlyAccess(false);
        
        // Mock services
        when(authorizationService.getUserAuthorizationContext(username))
            .thenReturn(authContext);
        when(coreUserDao.getUserByUsername(username)).thenReturn(user);
        when(valueOperations.get("user_token:" + username + ":" + tenantId))
            .thenReturn(null);
        
        // When: Process authorization flow
        UserToken result = simulateAuthorizationFlow(username, tenantId);
        
        // Then: Verify comprehensive authorization data
        assertNotNull(result);
        assertEquals(username, result.getUsername());
        assertEquals(5, result.getRoles().size());
        assertEquals(15, result.getPermissions().size());
        assertEquals(5, result.getBusinessUnits().size());
        assertFalse(result.isReadOnlyAccess());
        
        // Verify specific roles
        assertTrue(result.getRoles().contains("SUPER_ADMIN"));
        assertTrue(result.getRoles().contains("SYSTEM_ADMIN"));
        
        // Verify specific permissions
        assertTrue(result.getPermissions().contains("SYSTEM_CONFIG"));
        assertTrue(result.getPermissions().contains("TENANT_MANAGE"));
        assertTrue(result.getPermissions().contains("PERMISSION_MANAGE"));
        
        // Verify global business unit access
        Optional<BusinessUnitInfo> globalBU = result.getBusinessUnits().stream()
            .filter(bu -> "Global".equals(bu.getName()))
            .findFirst();
        assertTrue(globalBU.isPresent());
        assertEquals(1L, globalBU.get().getId().longValue());
        
        // Verify caching occurred
        verify(valueOperations).set(eq("user_token:" + username + ":" + tenantId), 
                                   any(UserToken.class), eq(58L), eq(TimeUnit.MINUTES));
    }
    
    @Test
    void testAuthorizationFlow_RegionalManager() throws Exception {
        // Given: Regional manager with limited business unit access
        String username = "regional.manager";
        String tenantId = "tenant456";
        
        CoreUser user = createTestUser(username, "Regional", "Manager", "<EMAIL>", "N");
        
        AuthorizationContext authContext = new AuthorizationContext();
        authContext.setRoleNames(Arrays.asList("REGIONAL_MANAGER", "USER_MANAGER"));
        authContext.setPermissionNames(Arrays.asList(
            "USER_READ", "USER_UPDATE", "REPORT_VIEW", "REPORT_EXPORT",
            "TEAM_MANAGE", "REGIONAL_CONFIG"
        ));
        authContext.setBusinessUnits(Arrays.asList(
            new BusinessUnitInfo(2L, "North America"),
            new BusinessUnitInfo(6L, "Canada")
        ));
        authContext.setReadOnlyAccess(false);
        
        // Mock services
        when(authorizationService.getUserAuthorizationContext(username))
            .thenReturn(authContext);
        when(coreUserDao.getUserByUsername(username)).thenReturn(user);
        when(valueOperations.get("user_token:" + username + ":" + tenantId))
            .thenReturn(null);
        
        // When: Process authorization flow
        UserToken result = simulateAuthorizationFlow(username, tenantId);
        
        // Then: Verify regional access limitations
        assertNotNull(result);
        assertEquals(2, result.getRoles().size());
        assertEquals(6, result.getPermissions().size());
        assertEquals(2, result.getBusinessUnits().size());
        
        // Verify no global admin permissions
        assertFalse(result.getPermissions().contains("USER_CREATE"));
        assertFalse(result.getPermissions().contains("USER_DELETE"));
        assertFalse(result.getPermissions().contains("SYSTEM_CONFIG"));
        
        // Verify regional permissions
        assertTrue(result.getPermissions().contains("TEAM_MANAGE"));
        assertTrue(result.getPermissions().contains("REGIONAL_CONFIG"));
        
        // Verify limited business unit access
        List<String> businessUnitNames = result.getBusinessUnits().stream()
            .map(BusinessUnitInfo::getName)
            .sorted()
            .toList();
        assertEquals(Arrays.asList("Canada", "North America"), businessUnitNames);
    }
    
    @Test
    void testAuthorizationFlow_BasicUser() throws Exception {
        // Given: Basic user with minimal permissions
        String username = "basic.user";
        String tenantId = "tenant789";
        
        CoreUser user = createTestUser(username, "Basic", "User", "<EMAIL>", "N");
        
        AuthorizationContext authContext = new AuthorizationContext();
        authContext.setRoleNames(Arrays.asList("BASIC_USER"));
        authContext.setPermissionNames(Arrays.asList("PROFILE_READ", "PROFILE_UPDATE", "REPORT_VIEW"));
        authContext.setBusinessUnits(Arrays.asList(
            new BusinessUnitInfo(7L, "Local Office")
        ));
        authContext.setReadOnlyAccess(false);
        
        // Mock services
        when(authorizationService.getUserAuthorizationContext(username))
            .thenReturn(authContext);
        when(coreUserDao.getUserByUsername(username)).thenReturn(user);
        when(valueOperations.get("user_token:" + username + ":" + tenantId))
            .thenReturn(null);
        
        // When: Process authorization flow
        UserToken result = simulateAuthorizationFlow(username, tenantId);
        
        // Then: Verify minimal access
        assertNotNull(result);
        assertEquals(1, result.getRoles().size());
        assertEquals("BASIC_USER", result.getRoles().get(0));
        assertEquals(3, result.getPermissions().size());
        assertEquals(1, result.getBusinessUnits().size());
        
        // Verify only basic permissions
        assertTrue(result.getPermissions().contains("PROFILE_READ"));
        assertTrue(result.getPermissions().contains("PROFILE_UPDATE"));
        assertTrue(result.getPermissions().contains("REPORT_VIEW"));
        
        // Verify no administrative permissions
        assertFalse(result.getPermissions().contains("USER_CREATE"));
        assertFalse(result.getPermissions().contains("ROLE_MANAGE"));
        assertFalse(result.getPermissions().contains("SYSTEM_CONFIG"));
        
        // Verify single business unit
        assertEquals("Local Office", result.getBusinessUnits().get(0).getName());
        assertEquals(7L, result.getBusinessUnits().get(0).getId().longValue());
    }
    
    @Test
    void testAuthorizationFlow_ReadOnlyAnalyst() throws Exception {
        // Given: Read-only analyst user
        String username = "readonly.analyst";
        String tenantId = "tenant101";
        
        CoreUser user = createTestUser(username, "ReadOnly", "Analyst", "<EMAIL>", "Y");
        
        AuthorizationContext authContext = new AuthorizationContext();
        authContext.setRoleNames(Arrays.asList("DATA_ANALYST", "REPORT_VIEWER"));
        authContext.setPermissionNames(Arrays.asList(
            "REPORT_VIEW", "REPORT_EXPORT", "DATA_READ", "ANALYTICS_VIEW"
        ));
        authContext.setBusinessUnits(Arrays.asList(
            new BusinessUnitInfo(1L, "Analytics Department"),
            new BusinessUnitInfo(2L, "Research Division")
        ));
        authContext.setReadOnlyAccess(true);
        
        // Mock services
        when(authorizationService.getUserAuthorizationContext(username))
            .thenReturn(authContext);
        when(coreUserDao.getUserByUsername(username)).thenReturn(user);
        when(valueOperations.get("user_token:" + username + ":" + tenantId))
            .thenReturn(null);
        
        // When: Process authorization flow
        UserToken result = simulateAuthorizationFlow(username, tenantId);
        
        // Then: Verify read-only access
        assertNotNull(result);
        assertTrue(result.isReadOnlyAccess());
        assertEquals(2, result.getRoles().size());
        assertEquals(4, result.getPermissions().size());
        
        // Verify only read permissions
        assertTrue(result.getPermissions().contains("REPORT_VIEW"));
        assertTrue(result.getPermissions().contains("DATA_READ"));
        assertTrue(result.getPermissions().contains("ANALYTICS_VIEW"));
        
        // Verify no write permissions
        assertFalse(result.getPermissions().contains("REPORT_CREATE"));
        assertFalse(result.getPermissions().contains("REPORT_UPDATE"));
        assertFalse(result.getPermissions().contains("DATA_WRITE"));
        
        // Verify analyst-specific business units
        assertEquals(2, result.getBusinessUnits().size());
        List<String> buNames = result.getBusinessUnits().stream()
            .map(BusinessUnitInfo::getName)
            .sorted()
            .toList();
        assertEquals(Arrays.asList("Analytics Department", "Research Division"), buNames);
    }
    
    @Test
    void testAuthorizationFlow_CachePerformance() throws Exception {
        // Given: Multiple requests for the same user
        String username = "performance.user";
        String tenantId = "tenant123";
        
        UserToken cachedToken = new UserToken();
        cachedToken.setUsername(username);
        cachedToken.setFirstName("Performance");
        cachedToken.setLastName("User");
        cachedToken.setEmail("<EMAIL>");
        cachedToken.setRoles(Arrays.asList("PERFORMANCE_ROLE"));
        cachedToken.setPermissions(Arrays.asList("PERFORMANCE_PERMISSION"));
        cachedToken.setBusinessUnits(Arrays.asList(new BusinessUnitInfo(1L, "Performance BU")));
        cachedToken.setReadOnlyAccess(false);
        cachedToken.setTenantIdentifier(tenantId);
        
        // Mock cache hit for subsequent requests
        when(valueOperations.get("user_token:" + username + ":" + tenantId))
            .thenReturn(null) // First call - cache miss
            .thenReturn(cachedToken) // Subsequent calls - cache hit
            .thenReturn(cachedToken)
            .thenReturn(cachedToken);
        
        // Mock authorization service for first call only
        AuthorizationContext authContext = new AuthorizationContext();
        authContext.setRoleNames(Arrays.asList("PERFORMANCE_ROLE"));
        authContext.setPermissionNames(Arrays.asList("PERFORMANCE_PERMISSION"));
        authContext.setBusinessUnits(Arrays.asList(new BusinessUnitInfo(1L, "Performance BU")));
        authContext.setReadOnlyAccess(false);
        
        when(authorizationService.getUserAuthorizationContext(username))
            .thenReturn(authContext);
        
        CoreUser user = createTestUser(username, "Performance", "User", "<EMAIL>", "N");
        when(coreUserDao.getUserByUsername(username)).thenReturn(user);
        
        // When: Make multiple requests
        UserToken result1 = simulateAuthorizationFlow(username, tenantId);
        UserToken result2 = simulateAuthorizationFlow(username, tenantId);
        UserToken result3 = simulateAuthorizationFlow(username, tenantId);
        UserToken result4 = simulateAuthorizationFlow(username, tenantId);
        
        // Then: Verify all results are consistent
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotNull(result3);
        assertNotNull(result4);
        
        assertEquals(result1.getUsername(), result2.getUsername());
        assertEquals(result1.getRoles(), result2.getRoles());
        assertEquals(result1.getPermissions(), result2.getPermissions());
        
        // Verify authorization service was called only once (first request)
        verify(authorizationService, times(1)).getUserAuthorizationContext(username);
        
        // Verify cache was checked multiple times
        verify(valueOperations, times(4)).get("user_token:" + username + ":" + tenantId);
    }
    
    @Test
    void testAuthorizationFlow_DatabaseFailureWithCacheFallback() throws Exception {
        // Given: Database failure but cached data available
        String username = "fallback.user";
        String tenantId = "tenant123";
        
        UserToken cachedToken = new UserToken();
        cachedToken.setUsername(username);
        cachedToken.setFirstName("Fallback");
        cachedToken.setLastName("User");
        cachedToken.setEmail("<EMAIL>");
        cachedToken.setRoles(Arrays.asList("CACHED_ROLE"));
        cachedToken.setPermissions(Arrays.asList("CACHED_PERMISSION"));
        cachedToken.setBusinessUnits(Arrays.asList(new BusinessUnitInfo(1L, "Cached BU")));
        cachedToken.setReadOnlyAccess(false);
        cachedToken.setTenantIdentifier(tenantId);
        
        // Mock cache miss initially, then return cached data on fallback
        when(valueOperations.get("user_token:" + username + ":" + tenantId))
            .thenReturn(null) // Initial cache miss
            .thenReturn(cachedToken); // Fallback cache hit
        
        // Mock database/authorization service failure
        when(authorizationService.getUserAuthorizationContext(username))
            .thenThrow(new RuntimeException("Database connection failed"));
        
        CoreUser user = createTestUser(username, "Fallback", "User", "<EMAIL>", "N");
        when(coreUserDao.getUserByUsername(username)).thenReturn(user);
        
        // When: Process authorization flow with fallback
        UserToken result = simulateAuthorizationFlowWithFallback(username, tenantId, cachedToken);
        
        // Then: Verify fallback data is used
        assertNotNull(result);
        assertEquals("fallback.user", result.getUsername());
        assertEquals(Arrays.asList("CACHED_ROLE"), result.getRoles());
        assertEquals(Arrays.asList("CACHED_PERMISSION"), result.getPermissions());
        assertEquals(1, result.getBusinessUnits().size());
        assertEquals("Cached BU", result.getBusinessUnits().get(0).getName());
        
        // Verify authorization service was attempted
        verify(authorizationService).getUserAuthorizationContext(username);
    }
    
    @Test
    void testAuthorizationFlow_MultiTenantIsolation() throws Exception {
        // Given: Same user in different tenants
        String username = "multi.tenant.user";
        String tenant1 = "tenant_a";
        String tenant2 = "tenant_b";
        
        CoreUser user = createTestUser(username, "Multi", "Tenant", "<EMAIL>", "N");
        
        // Different authorization contexts for different tenants
        AuthorizationContext tenant1Auth = new AuthorizationContext();
        tenant1Auth.setRoleNames(Arrays.asList("TENANT_A_ADMIN"));
        tenant1Auth.setPermissionNames(Arrays.asList("TENANT_A_MANAGE"));
        tenant1Auth.setBusinessUnits(Arrays.asList(new BusinessUnitInfo(1L, "Tenant A BU")));
        tenant1Auth.setReadOnlyAccess(false);
        
        AuthorizationContext tenant2Auth = new AuthorizationContext();
        tenant2Auth.setRoleNames(Arrays.asList("TENANT_B_USER"));
        tenant2Auth.setPermissionNames(Arrays.asList("TENANT_B_READ"));
        tenant2Auth.setBusinessUnits(Arrays.asList(new BusinessUnitInfo(2L, "Tenant B BU")));
        tenant2Auth.setReadOnlyAccess(true);
        
        // Mock services for both tenants
        when(authorizationService.getUserAuthorizationContext(username))
            .thenReturn(tenant1Auth) // First call for tenant A
            .thenReturn(tenant2Auth); // Second call for tenant B
        
        when(coreUserDao.getUserByUsername(username)).thenReturn(user);
        
        // Mock cache misses for both tenants
        when(valueOperations.get("user_token:" + username + ":" + tenant1))
            .thenReturn(null);
        when(valueOperations.get("user_token:" + username + ":" + tenant2))
            .thenReturn(null);
        
        // When: Process authorization flow for both tenants
        UserToken result1 = simulateAuthorizationFlow(username, tenant1);
        UserToken result2 = simulateAuthorizationFlow(username, tenant2);
        
        // Then: Verify tenant isolation
        assertNotNull(result1);
        assertNotNull(result2);
        
        // Verify different authorization data per tenant
        assertEquals(Arrays.asList("TENANT_A_ADMIN"), result1.getRoles());
        assertEquals(Arrays.asList("TENANT_B_USER"), result2.getRoles());
        
        assertEquals(Arrays.asList("TENANT_A_MANAGE"), result1.getPermissions());
        assertEquals(Arrays.asList("TENANT_B_READ"), result2.getPermissions());
        
        assertFalse(result1.isReadOnlyAccess());
        assertTrue(result2.isReadOnlyAccess());
        
        assertEquals("Tenant A BU", result1.getBusinessUnits().get(0).getName());
        assertEquals("Tenant B BU", result2.getBusinessUnits().get(0).getName());
        
        // Verify separate cache entries
        verify(valueOperations).set(eq("user_token:" + username + ":" + tenant1), 
                                   any(UserToken.class), eq(58L), eq(TimeUnit.MINUTES));
        verify(valueOperations).set(eq("user_token:" + username + ":" + tenant2), 
                                   any(UserToken.class), eq(58L), eq(TimeUnit.MINUTES));
    }
    
    private CoreUser createTestUser(String username, String firstName, String lastName, 
                                   String email, String readOnlyUser) {
        CoreUser user = new CoreUser();
        user.setUsername(username);
        user.setFirstName(firstName);
        user.setLastName(lastName);
        user.setEmail(email);
        user.setReadOnlyUser(readOnlyUser);
        return user;
    }
    
    private UserToken simulateAuthorizationFlow(String username, String tenantId) throws Exception {
        // Simulate the authorization flow logic
        String cacheKey = "user_token:" + username + ":" + tenantId;
        UserToken cachedToken = (UserToken) valueOperations.get(cacheKey);
        
        if (cachedToken != null) {
            return cachedToken;
        }
        
        // Get user from database
        CoreUser user = coreUserDao.getUserByUsername(username);
        if (user == null) {
            throw new RuntimeException("User not found: " + username);
        }
        
        // Get authorization context
        AuthorizationContext authContext = authorizationService.getUserAuthorizationContext(username);
        
        // Create user token with authorization data
        UserToken userToken = new UserToken();
        userToken.setUsername(user.getUsername());
        userToken.setFirstName(user.getFirstName());
        userToken.setLastName(user.getLastName());
        userToken.setEmail(user.getEmail());
        userToken.setRoles(authContext.getRoleNames());
        userToken.setPermissions(authContext.getPermissionNames());
        userToken.setBusinessUnits(authContext.getBusinessUnits());
        userToken.setReadOnlyAccess(authContext.isReadOnlyAccess());
        userToken.setTenantIdentifier(tenantId);
        
        // Cache the token
        valueOperations.set(cacheKey, userToken, 58L, TimeUnit.MINUTES);
        
        return userToken;
    }
    
    private UserToken simulateAuthorizationFlowWithFallback(String username, String tenantId, 
                                                           UserToken fallbackToken) throws Exception {
        String cacheKey = "user_token:" + username + ":" + tenantId;
        UserToken cachedToken = (UserToken) valueOperations.get(cacheKey);
        
        if (cachedToken != null) {
            return cachedToken;
        }
        
        try {
            // Attempt to get fresh data
            CoreUser user = coreUserDao.getUserByUsername(username);
            AuthorizationContext authContext = authorizationService.getUserAuthorizationContext(username);
            
            // Create new token (this won't be reached due to mocked exception)
            UserToken userToken = new UserToken();
            userToken.setUsername(user.getUsername());
            userToken.setFirstName(user.getFirstName());
            userToken.setLastName(user.getLastName());
            userToken.setEmail(user.getEmail());
            userToken.setRoles(authContext.getRoleNames());
            userToken.setPermissions(authContext.getPermissionNames());
            userToken.setBusinessUnits(authContext.getBusinessUnits());
            userToken.setReadOnlyAccess(authContext.isReadOnlyAccess());
            userToken.setTenantIdentifier(tenantId);
            
            return userToken;
        } catch (Exception e) {
            // Fallback to cached data
            return fallbackToken;
        }
    }
}