package com.recvueoktatoken.authorizer.integration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.recvueoktatoken.authorizer.model.AuthorizationContext;
import com.recvueoktatoken.authorizer.model.BusinessUnitInfo;
import com.recvueoktatoken.authorizer.model.UserToken;
import com.recvueoktatoken.authorizer.request.JWTUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests to verify backward compatibility with existing JWT token structure
 */
@ExtendWith(MockitoExtension.class)
public class BackwardCompatibilityIntegrationTest {

    private JWTUtils jwtUtils;
    private ObjectMapper objectMapper;
    
    @BeforeEach
    void setUp() {
        jwtUtils = new JWTUtils();
        objectMapper = new ObjectMapper();
    }
    
    @Test
    void testBackwardCompatibility_LegacyJwtStructure() throws Exception {
        // Given: Legacy JWT token structure (without authorization claims)
        Map<String, Object> legacyClaims = new HashMap<>();
        
        // Legacy user claim structure
        Map<String, Object> userClaim = new HashMap<>();
        userClaim.put("username", "legacy.user");
        userClaim.put("firstName", "Legacy");
        userClaim.put("lastName", "User");
        userClaim.put("email", "<EMAIL>");
        legacyClaims.put("user", userClaim);
        
        // Legacy standard claims
        legacyClaims.put("tenantIdentifier", "legacy_tenant");
        legacyClaims.put("iat", System.currentTimeMillis() / 1000);
        legacyClaims.put("exp", (System.currentTimeMillis() / 1000) + 3600);
        
        // When: Generate JWT token with legacy structure
        String legacyJwtToken = jwtUtils.generateJwt(legacyClaims);
        
        // Then: Verify legacy token is valid and parseable
        assertNotNull(legacyJwtToken);
        assertTrue(legacyJwtToken.split("\\.").length == 3); // Valid JWT format
        
        JsonNode claims = decodeJwtClaims(legacyJwtToken);
        
        // Verify legacy claims are preserved
        JsonNode userClaimNode = claims.get("user");
        assertNotNull(userClaimNode);
        assertEquals("legacy.user", userClaimNode.get("username").asText());
        assertEquals("Legacy", userClaimNode.get("firstName").asText());
        assertEquals("User", userClaimNode.get("lastName").asText());
        assertEquals("<EMAIL>", userClaimNode.get("email").asText());
        
        assertEquals("legacy_tenant", claims.get("tenantIdentifier").asText());
        assertNotNull(claims.get("iat"));
        assertNotNull(claims.get("exp"));
        
        // Verify no authorization claims are present (as expected for legacy)
        assertNull(claims.get("roles"));
        assertNull(claims.get("permissions"));
        assertNull(claims.get("businessUnits"));
        assertNull(claims.get("readOnlyAccess"));
    }
    
    @Test
    void testBackwardCompatibility_EnhancedJwtWithAllClaims() throws Exception {
        // Given: Enhanced JWT token with all authorization claims
        Map<String, Object> enhancedClaims = new HashMap<>();
        
        // User claim (unchanged structure)
        Map<String, Object> userClaim = new HashMap<>();
        userClaim.put("username", "enhanced.user");
        userClaim.put("firstName", "Enhanced");
        userClaim.put("lastName", "User");
        userClaim.put("email", "<EMAIL>");
        enhancedClaims.put("user", userClaim);
        
        // New authorization claims
        enhancedClaims.put("roles", Arrays.asList("ADMIN", "USER_MANAGER"));
        enhancedClaims.put("permissions", Arrays.asList("USER_CREATE", "USER_READ", "USER_UPDATE"));
        
        List<Map<String, Object>> businessUnits = new ArrayList<>();
        Map<String, Object> bu1 = new HashMap<>();
        bu1.put("id", 1L);
        bu1.put("name", "North America");
        businessUnits.add(bu1);
        
        Map<String, Object> bu2 = new HashMap<>();
        bu2.put("id", 2L);
        bu2.put("name", "Europe");
        businessUnits.add(bu2);
        
        enhancedClaims.put("businessUnits", businessUnits);
        enhancedClaims.put("readOnlyAccess", false);
        
        // Standard claims (unchanged)
        enhancedClaims.put("tenantIdentifier", "enhanced_tenant");
        enhancedClaims.put("iat", System.currentTimeMillis() / 1000);
        enhancedClaims.put("exp", (System.currentTimeMillis() / 1000) + 3600);
        
        // When: Generate JWT token with enhanced structure
        String enhancedJwtToken = jwtUtils.generateJwt(enhancedClaims);
        
        // Then: Verify enhanced token contains both legacy and new claims
        assertNotNull(enhancedJwtToken);
        JsonNode claims = decodeJwtClaims(enhancedJwtToken);
        
        // Verify legacy claims are still present and unchanged
        JsonNode userClaimNode = claims.get("user");
        assertNotNull(userClaimNode);
        assertEquals("enhanced.user", userClaimNode.get("username").asText());
        assertEquals("Enhanced", userClaimNode.get("firstName").asText());
        assertEquals("User", userClaimNode.get("lastName").asText());
        assertEquals("<EMAIL>", userClaimNode.get("email").asText());
        
        assertEquals("enhanced_tenant", claims.get("tenantIdentifier").asText());
        assertNotNull(claims.get("iat"));
        assertNotNull(claims.get("exp"));
        
        // Verify new authorization claims are present
        JsonNode rolesClaim = claims.get("roles");
        assertNotNull(rolesClaim);
        assertTrue(rolesClaim.isArray());
        assertEquals(2, rolesClaim.size());
        
        JsonNode permissionsClaim = claims.get("permissions");
        assertNotNull(permissionsClaim);
        assertTrue(permissionsClaim.isArray());
        assertEquals(3, permissionsClaim.size());
        
        JsonNode businessUnitsClaim = claims.get("businessUnits");
        assertNotNull(businessUnitsClaim);
        assertTrue(businessUnitsClaim.isArray());
        assertEquals(2, businessUnitsClaim.size());
        
        JsonNode readOnlyAccessClaim = claims.get("readOnlyAccess");
        assertNotNull(readOnlyAccessClaim);
        assertFalse(readOnlyAccessClaim.asBoolean());
    }
    
    @Test
    void testBackwardCompatibility_PartialAuthorizationClaims() throws Exception {
        // Given: JWT token with some authorization claims missing (graceful degradation)
        Map<String, Object> partialClaims = new HashMap<>();
        
        // User claim
        Map<String, Object> userClaim = new HashMap<>();
        userClaim.put("username", "partial.user");
        userClaim.put("firstName", "Partial");
        userClaim.put("lastName", "User");
        userClaim.put("email", "<EMAIL>");
        partialClaims.put("user", userClaim);
        
        // Only some authorization claims present
        partialClaims.put("roles", Arrays.asList("BASIC_USER"));
        // permissions missing
        partialClaims.put("businessUnits", new ArrayList<>()); // empty array
        partialClaims.put("readOnlyAccess", true);
        
        // Standard claims
        partialClaims.put("tenantIdentifier", "partial_tenant");
        partialClaims.put("iat", System.currentTimeMillis() / 1000);
        partialClaims.put("exp", (System.currentTimeMillis() / 1000) + 3600);
        
        // When: Generate JWT token with partial authorization claims
        String partialJwtToken = jwtUtils.generateJwt(partialClaims);
        
        // Then: Verify token is valid and handles missing claims gracefully
        assertNotNull(partialJwtToken);
        JsonNode claims = decodeJwtClaims(partialJwtToken);
        
        // Verify user and standard claims
        assertEquals("partial.user", claims.get("user").get("username").asText());
        assertEquals("partial_tenant", claims.get("tenantIdentifier").asText());
        
        // Verify present authorization claims
        JsonNode rolesClaim = claims.get("roles");
        assertNotNull(rolesClaim);
        assertEquals(1, rolesClaim.size());
        assertEquals("BASIC_USER", rolesClaim.get(0).asText());
        
        JsonNode businessUnitsClaim = claims.get("businessUnits");
        assertNotNull(businessUnitsClaim);
        assertTrue(businessUnitsClaim.isArray());
        assertEquals(0, businessUnitsClaim.size());
        
        assertTrue(claims.get("readOnlyAccess").asBoolean());
        
        // Verify missing claim is handled (should be null, not cause errors)
        assertNull(claims.get("permissions"));
    }
    
    @Test
    void testBackwardCompatibility_LegacyTokenConsumption() throws Exception {
        // Given: Simulate how existing downstream services would consume tokens
        Map<String, Object> tokenClaims = new HashMap<>();
        
        // Standard user claim that existing services expect
        Map<String, Object> userClaim = new HashMap<>();
        userClaim.put("username", "downstream.user");
        userClaim.put("firstName", "Downstream");
        userClaim.put("lastName", "Service");
        userClaim.put("email", "<EMAIL>");
        tokenClaims.put("user", userClaim);
        
        // Standard claims existing services expect
        tokenClaims.put("tenantIdentifier", "downstream_tenant");
        tokenClaims.put("iat", System.currentTimeMillis() / 1000);
        tokenClaims.put("exp", (System.currentTimeMillis() / 1000) + 3600);
        
        // New authorization claims (should not break existing services)
        tokenClaims.put("roles", Arrays.asList("SERVICE_USER"));
        tokenClaims.put("permissions", Arrays.asList("SERVICE_ACCESS"));
        tokenClaims.put("businessUnits", Arrays.asList(
            Map.of("id", 1L, "name", "Service BU")
        ));
        tokenClaims.put("readOnlyAccess", false);
        
        // When: Generate token and simulate legacy consumption
        String jwtToken = jwtUtils.generateJwt(tokenClaims);
        JsonNode claims = decodeJwtClaims(jwtToken);
        
        // Then: Verify existing services can still extract expected claims
        // Simulate legacy service extracting user information
        JsonNode userInfo = claims.get("user");
        assertNotNull(userInfo);
        String extractedUsername = userInfo.get("username").asText();
        String extractedFirstName = userInfo.get("firstName").asText();
        String extractedLastName = userInfo.get("lastName").asText();
        String extractedEmail = userInfo.get("email").asText();
        
        assertEquals("downstream.user", extractedUsername);
        assertEquals("Downstream", extractedFirstName);
        assertEquals("Service", extractedLastName);
        assertEquals("<EMAIL>", extractedEmail);
        
        // Simulate legacy service extracting tenant information
        String extractedTenant = claims.get("tenantIdentifier").asText();
        assertEquals("downstream_tenant", extractedTenant);
        
        // Simulate legacy service checking token validity
        long issuedAt = claims.get("iat").asLong();
        long expiresAt = claims.get("exp").asLong();
        assertTrue(issuedAt > 0);
        assertTrue(expiresAt > issuedAt);
        
        // Verify new claims don't interfere with legacy processing
        // (Legacy services would simply ignore unknown claims)
        assertNotNull(claims.get("roles")); // Present but ignored by legacy services
        assertNotNull(claims.get("permissions")); // Present but ignored by legacy services
    }
    
    @Test
    void testBackwardCompatibility_UserTokenStructureEvolution() throws Exception {
        // Given: Evolution from legacy UserToken to enhanced UserToken
        
        // Legacy UserToken structure (before authorization enhancement)
        UserToken legacyUserToken = new UserToken();
        legacyUserToken.setUsername("evolution.user");
        legacyUserToken.setFirstName("Evolution");
        legacyUserToken.setLastName("User");
        legacyUserToken.setEmail("<EMAIL>");
        legacyUserToken.setTenantIdentifier("evolution_tenant");
        // Authorization fields would be null/empty in legacy version
        
        // Enhanced UserToken structure (after authorization enhancement)
        UserToken enhancedUserToken = new UserToken();
        enhancedUserToken.setUsername("evolution.user");
        enhancedUserToken.setFirstName("Evolution");
        enhancedUserToken.setLastName("User");
        enhancedUserToken.setEmail("<EMAIL>");
        enhancedUserToken.setTenantIdentifier("evolution_tenant");
        // New authorization fields
        enhancedUserToken.setRoles(Arrays.asList("EVOLVED_ROLE"));
        enhancedUserToken.setPermissions(Arrays.asList("EVOLVED_PERMISSION"));
        enhancedUserToken.setBusinessUnits(Arrays.asList(new BusinessUnitInfo(1L, "Evolved BU")));
        enhancedUserToken.setReadOnlyAccess(false);
        
        // When: Generate JWT tokens from both structures
        Map<String, Object> legacyClaims = createClaimsFromUserToken(legacyUserToken);
        Map<String, Object> enhancedClaims = createClaimsFromUserToken(enhancedUserToken);
        
        String legacyJwt = jwtUtils.generateJwt(legacyClaims);
        String enhancedJwt = jwtUtils.generateJwt(enhancedClaims);
        
        // Then: Verify both tokens are valid and compatible
        JsonNode legacyClaimsNode = decodeJwtClaims(legacyJwt);
        JsonNode enhancedClaimsNode = decodeJwtClaims(enhancedJwt);
        
        // Verify core user information is identical
        assertEquals(
            legacyClaimsNode.get("user").get("username").asText(),
            enhancedClaimsNode.get("user").get("username").asText()
        );
        assertEquals(
            legacyClaimsNode.get("tenantIdentifier").asText(),
            enhancedClaimsNode.get("tenantIdentifier").asText()
        );
        
        // Verify legacy token has no authorization claims
        assertNull(legacyClaimsNode.get("roles"));
        assertNull(legacyClaimsNode.get("permissions"));
        assertNull(legacyClaimsNode.get("businessUnits"));
        assertNull(legacyClaimsNode.get("readOnlyAccess"));
        
        // Verify enhanced token has authorization claims
        assertNotNull(enhancedClaimsNode.get("roles"));
        assertNotNull(enhancedClaimsNode.get("permissions"));
        assertNotNull(enhancedClaimsNode.get("businessUnits"));
        assertNotNull(enhancedClaimsNode.get("readOnlyAccess"));
    }
    
    @Test
    void testBackwardCompatibility_EmptyAuthorizationClaims() throws Exception {
        // Given: User with no roles, permissions, or business units (edge case)
        Map<String, Object> emptyClaims = new HashMap<>();
        
        Map<String, Object> userClaim = new HashMap<>();
        userClaim.put("username", "empty.auth.user");
        userClaim.put("firstName", "Empty");
        userClaim.put("lastName", "Auth");
        userClaim.put("email", "<EMAIL>");
        emptyClaims.put("user", userClaim);
        
        // Empty authorization claims
        emptyClaims.put("roles", new ArrayList<>());
        emptyClaims.put("permissions", new ArrayList<>());
        emptyClaims.put("businessUnits", new ArrayList<>());
        emptyClaims.put("readOnlyAccess", false);
        
        emptyClaims.put("tenantIdentifier", "empty_tenant");
        emptyClaims.put("iat", System.currentTimeMillis() / 1000);
        emptyClaims.put("exp", (System.currentTimeMillis() / 1000) + 3600);
        
        // When: Generate JWT token with empty authorization claims
        String emptyAuthJwt = jwtUtils.generateJwt(emptyClaims);
        
        // Then: Verify token is valid and handles empty arrays gracefully
        assertNotNull(emptyAuthJwt);
        JsonNode claims = decodeJwtClaims(emptyAuthJwt);
        
        // Verify user information is present
        assertEquals("empty.auth.user", claims.get("user").get("username").asText());
        
        // Verify empty authorization arrays are handled correctly
        JsonNode rolesClaim = claims.get("roles");
        assertNotNull(rolesClaim);
        assertTrue(rolesClaim.isArray());
        assertEquals(0, rolesClaim.size());
        
        JsonNode permissionsClaim = claims.get("permissions");
        assertNotNull(permissionsClaim);
        assertTrue(permissionsClaim.isArray());
        assertEquals(0, permissionsClaim.size());
        
        JsonNode businessUnitsClaim = claims.get("businessUnits");
        assertNotNull(businessUnitsClaim);
        assertTrue(businessUnitsClaim.isArray());
        assertEquals(0, businessUnitsClaim.size());
        
        assertFalse(claims.get("readOnlyAccess").asBoolean());
    }
    
    @Test
    void testBackwardCompatibility_TokenSizeImpact() throws Exception {
        // Given: Compare token sizes between legacy and enhanced versions
        
        // Legacy token (minimal claims)
        Map<String, Object> legacyClaims = new HashMap<>();
        Map<String, Object> userClaim = new HashMap<>();
        userClaim.put("username", "size.test.user");
        userClaim.put("firstName", "Size");
        userClaim.put("lastName", "Test");
        userClaim.put("email", "<EMAIL>");
        legacyClaims.put("user", userClaim);
        legacyClaims.put("tenantIdentifier", "size_tenant");
        legacyClaims.put("iat", System.currentTimeMillis() / 1000);
        legacyClaims.put("exp", (System.currentTimeMillis() / 1000) + 3600);
        
        // Enhanced token (with authorization claims)
        Map<String, Object> enhancedClaims = new HashMap<>(legacyClaims);
        enhancedClaims.put("roles", Arrays.asList("ROLE1", "ROLE2", "ROLE3"));
        enhancedClaims.put("permissions", Arrays.asList(
            "PERM1", "PERM2", "PERM3", "PERM4", "PERM5"
        ));
        enhancedClaims.put("businessUnits", Arrays.asList(
            Map.of("id", 1L, "name", "Business Unit 1"),
            Map.of("id", 2L, "name", "Business Unit 2")
        ));
        enhancedClaims.put("readOnlyAccess", false);
        
        // When: Generate both tokens
        String legacyJwt = jwtUtils.generateJwt(legacyClaims);
        String enhancedJwt = jwtUtils.generateJwt(enhancedClaims);
        
        // Then: Verify token size increase is reasonable
        int legacySize = legacyJwt.length();
        int enhancedSize = enhancedJwt.length();
        
        assertTrue(enhancedSize > legacySize, "Enhanced token should be larger");
        
        // Verify size increase is not excessive (should be less than 100% increase)
        double sizeIncrease = ((double) (enhancedSize - legacySize) / legacySize) * 100;
        assertTrue(sizeIncrease < 100, 
            "Token size increase should be reasonable: " + sizeIncrease + "%");
        
        // Verify both tokens are still within reasonable size limits
        assertTrue(legacySize < 2048, "Legacy token should be under 2KB");
        assertTrue(enhancedSize < 4096, "Enhanced token should be under 4KB");
    }
    
    private Map<String, Object> createClaimsFromUserToken(UserToken userToken) {
        Map<String, Object> claims = new HashMap<>();
        
        // User claim
        Map<String, Object> userClaim = new HashMap<>();
        userClaim.put("username", userToken.getUsername());
        userClaim.put("firstName", userToken.getFirstName());
        userClaim.put("lastName", userToken.getLastName());
        userClaim.put("email", userToken.getEmail());
        claims.put("user", userClaim);
        
        // Authorization claims (only if present)
        if (userToken.getRoles() != null && !userToken.getRoles().isEmpty()) {
            claims.put("roles", userToken.getRoles());
        }
        if (userToken.getPermissions() != null && !userToken.getPermissions().isEmpty()) {
            claims.put("permissions", userToken.getPermissions());
        }
        if (userToken.getBusinessUnits() != null && !userToken.getBusinessUnits().isEmpty()) {
            claims.put("businessUnits", userToken.getBusinessUnits());
        }
        claims.put("readOnlyAccess", userToken.isReadOnlyAccess());
        
        // Standard claims
        claims.put("tenantIdentifier", userToken.getTenantIdentifier());
        claims.put("iat", System.currentTimeMillis() / 1000);
        claims.put("exp", (System.currentTimeMillis() / 1000) + 3600);
        
        return claims;
    }
    
    private JsonNode decodeJwtClaims(String jwtToken) throws Exception {
        String[] parts = jwtToken.split("\\.");
        if (parts.length != 3) {
            throw new IllegalArgumentException("Invalid JWT token format");
        }
        
        String payload = parts[1];
        byte[] decodedBytes = Base64.getDecoder().decode(payload);
        String decodedPayload = new String(decodedBytes);
        
        return objectMapper.readTree(decodedPayload);
    }
}