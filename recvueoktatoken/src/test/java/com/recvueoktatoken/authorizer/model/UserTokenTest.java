package com.recvueoktatoken.authorizer.model;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Unit tests for enhanced UserToken class with authorization fields
 */
class UserTokenTest {

    private UserToken userToken;
    private List<String> testRoles;
    private List<String> testPermissions;
    private List<BusinessUnitInfo> testBusinessUnits;
    private AuthorizationContext testContext;

    @BeforeEach
    void setUp() {
        userToken = new UserToken();
        testRoles = Arrays.asList("ADMIN", "USER_MANAGER");
        testPermissions = Arrays.asList("USER_CREATE", "USER_READ", "USER_UPDATE", "REPORT_VIEW");
        testBusinessUnits = Arrays.asList(
                new BusinessUnitInfo(1L, "North America"),
                new BusinessUnitInfo(2L, "Europe")
        );
        testContext = new AuthorizationContext(testRoles, testPermissions, testBusinessUnits, false);
    }

    @Test
    void testSerializable() {
        // Test that UserToken implements Serializable
        assertTrue(userToken instanceof java.io.Serializable);
        assertEquals(1L, UserToken.serialVersionUID);
    }

    @Test
    void testSetRoles() {
        // Act
        userToken.setRoles(testRoles);

        // Assert
        assertNotNull(userToken.getRoles());
        assertEquals(2, userToken.getRoles().size());
        assertTrue(userToken.getRoles().contains("ADMIN"));
        assertTrue(userToken.getRoles().contains("USER_MANAGER"));
    }

    @Test
    void testSetRoles_Null() {
        // Act
        userToken.setRoles(null);

        // Assert
        assertNotNull(userToken.getRoles());
        assertTrue(userToken.getRoles().isEmpty());
    }

    @Test
    void testSetRoles_DefensiveCopy() {
        // Act
        userToken.setRoles(testRoles);
        testRoles.add("NEW_ROLE"); // Modify original list

        // Assert
        assertEquals(2, userToken.getRoles().size()); // Should not be affected
        assertFalse(userToken.getRoles().contains("NEW_ROLE"));
    }

    @Test
    void testSetPermissions() {
        // Act
        userToken.setPermissions(testPermissions);

        // Assert
        assertNotNull(userToken.getPermissions());
        assertEquals(4, userToken.getPermissions().size());
        assertTrue(userToken.getPermissions().contains("USER_CREATE"));
        assertTrue(userToken.getPermissions().contains("REPORT_VIEW"));
    }

    @Test
    void testSetPermissions_Null() {
        // Act
        userToken.setPermissions(null);

        // Assert
        assertNotNull(userToken.getPermissions());
        assertTrue(userToken.getPermissions().isEmpty());
    }

    @Test
    void testSetPermissions_DefensiveCopy() {
        // Act
        userToken.setPermissions(testPermissions);
        testPermissions.add("NEW_PERMISSION"); // Modify original list

        // Assert
        assertEquals(4, userToken.getPermissions().size()); // Should not be affected
        assertFalse(userToken.getPermissions().contains("NEW_PERMISSION"));
    }

    @Test
    void testSetBusinessUnits() {
        // Act
        userToken.setBusinessUnits(testBusinessUnits);

        // Assert
        assertNotNull(userToken.getBusinessUnits());
        assertEquals(2, userToken.getBusinessUnits().size());
        assertEquals("North America", userToken.getBusinessUnits().get(0).getBusinessUnitName());
        assertEquals("Europe", userToken.getBusinessUnits().get(1).getBusinessUnitName());
    }

    @Test
    void testSetBusinessUnits_Null() {
        // Act
        userToken.setBusinessUnits(null);

        // Assert
        assertNotNull(userToken.getBusinessUnits());
        assertTrue(userToken.getBusinessUnits().isEmpty());
    }

    @Test
    void testSetBusinessUnits_DefensiveCopy() {
        // Act
        userToken.setBusinessUnits(testBusinessUnits);
        testBusinessUnits.add(new BusinessUnitInfo(3L, "Asia")); // Modify original list

        // Assert
        assertEquals(2, userToken.getBusinessUnits().size()); // Should not be affected
    }

    @Test
    void testReadOnlyAccess() {
        // Test default value
        assertFalse(userToken.isReadOnlyAccess());

        // Test setting to true
        userToken.setReadOnlyAccess(true);
        assertTrue(userToken.isReadOnlyAccess());

        // Test setting to false
        userToken.setReadOnlyAccess(false);
        assertFalse(userToken.isReadOnlyAccess());
    }

    @Test
    void testFromAuthorizationContext_Complete() {
        // Act
        UserToken result = UserToken.fromAuthorizationContext(testContext, "testuser", 
                "John", "Doe", "<EMAIL>", "jwt-token", 1234567890L, "server1");

        // Assert
        assertNotNull(result);
        assertEquals("testuser", result.getLogin());
        assertEquals("John", result.getFirstName());
        assertEquals("Doe", result.getLastName());
        assertEquals("<EMAIL>", result.getEmail());
        assertEquals("jwt-token", result.getJwtToken());
        assertEquals(Long.valueOf(1234567890L), result.getExpiryTime());
        assertEquals("server1", result.getServerName());

        // Verify authorization data
        assertEquals(2, result.getRoles().size());
        assertEquals(4, result.getPermissions().size());
        assertEquals(2, result.getBusinessUnits().size());
        assertFalse(result.isReadOnlyAccess());

        assertTrue(result.getRoles().contains("ADMIN"));
        assertTrue(result.getPermissions().contains("USER_CREATE"));
        assertEquals("North America", result.getBusinessUnits().get(0).getBusinessUnitName());
    }

    @Test
    void testFromAuthorizationContext_NullContext() {
        // Act
        UserToken result = UserToken.fromAuthorizationContext(null, "testuser", 
                "John", "Doe", "<EMAIL>", "jwt-token", 1234567890L, "server1");

        // Assert
        assertNotNull(result);
        assertEquals("testuser", result.getLogin());
        assertEquals("John", result.getFirstName());
        assertEquals("Doe", result.getLastName());
        assertEquals("<EMAIL>", result.getEmail());
        assertEquals("jwt-token", result.getJwtToken());
        assertEquals(Long.valueOf(1234567890L), result.getExpiryTime());
        assertEquals("server1", result.getServerName());

        // Verify authorization data is empty
        assertNull(result.getRoles());
        assertNull(result.getPermissions());
        assertNull(result.getBusinessUnits());
        assertFalse(result.isReadOnlyAccess());
    }

    @Test
    void testFromAuthorizationContext_ReadOnlyUser() {
        // Arrange
        AuthorizationContext readOnlyContext = new AuthorizationContext(testRoles, testPermissions, testBusinessUnits, true);

        // Act
        UserToken result = UserToken.fromAuthorizationContext(readOnlyContext, "testuser", 
                "John", "Doe", "<EMAIL>", "jwt-token", 1234567890L, "server1");

        // Assert
        assertTrue(result.isReadOnlyAccess());
    }

    @Test
    void testToAuthorizationContext() {
        // Arrange
        userToken.setLogin("testuser");
        userToken.setRoles(testRoles);
        userToken.setPermissions(testPermissions);
        userToken.setBusinessUnits(testBusinessUnits);
        userToken.setReadOnlyAccess(true);

        // Act
        AuthorizationContext result = userToken.toAuthorizationContext();

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getRoleNames().size());
        assertEquals(4, result.getPermissionNames().size());
        assertEquals(2, result.getBusinessUnits().size());
        assertTrue(result.isReadOnlyAccess());

        assertTrue(result.hasRole("ADMIN"));
        assertTrue(result.hasPermission("USER_CREATE"));
        assertTrue(result.hasBusinessUnitAccess(1L));
    }

    @Test
    void testToAuthorizationContext_EmptyData() {
        // Arrange - UserToken with no authorization data set
        userToken.setLogin("testuser");

        // Act
        AuthorizationContext result = userToken.toAuthorizationContext();

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRoleNames());
        assertNotNull(result.getPermissionNames());
        assertNotNull(result.getBusinessUnits());
        assertTrue(result.getRoleNames().isEmpty());
        assertTrue(result.getPermissionNames().isEmpty());
        assertTrue(result.getBusinessUnits().isEmpty());
        assertFalse(result.isReadOnlyAccess());
    }

    @Test
    void testRoundTripConversion() {
        // Test that converting from AuthorizationContext to UserToken and back preserves data
        
        // Act
        UserToken userToken = UserToken.fromAuthorizationContext(testContext, "testuser", 
                "John", "Doe", "<EMAIL>", "jwt-token", 1234567890L, "server1");
        AuthorizationContext convertedBack = userToken.toAuthorizationContext();

        // Assert
        assertEquals(testContext.getRoleNames().size(), convertedBack.getRoleNames().size());
        assertEquals(testContext.getPermissionNames().size(), convertedBack.getPermissionNames().size());
        assertEquals(testContext.getBusinessUnits().size(), convertedBack.getBusinessUnits().size());
        assertEquals(testContext.isReadOnlyAccess(), convertedBack.isReadOnlyAccess());

        // Verify content is preserved
        assertTrue(convertedBack.getRoleNames().containsAll(testContext.getRoleNames()));
        assertTrue(convertedBack.getPermissionNames().containsAll(testContext.getPermissionNames()));
        
        for (int i = 0; i < testContext.getBusinessUnits().size(); i++) {
            BusinessUnitInfo original = testContext.getBusinessUnits().get(i);
            BusinessUnitInfo converted = convertedBack.getBusinessUnits().get(i);
            assertEquals(original.getBusinessUnitId(), converted.getBusinessUnitId());
            assertEquals(original.getBusinessUnitName(), converted.getBusinessUnitName());
        }
    }

    @Test
    void testBackwardCompatibility() {
        // Test that existing UserToken functionality still works
        
        // Act - Set existing fields
        userToken.setAccessToken("access-token");
        userToken.setJwtToken("jwt-token");
        userToken.setLogin("testuser");
        userToken.setFirstName("John");
        userToken.setLastName("Doe");
        userToken.setEmail("<EMAIL>");
        userToken.setProfileNames("profile1|profile2");
        userToken.setExpiryTime(1234567890L);
        userToken.setServerName("server1");

        // Assert - Verify existing functionality
        assertEquals("access-token", userToken.getAccessToken());
        assertEquals("jwt-token", userToken.getJwtToken());
        assertEquals("testuser", userToken.getLogin());
        assertEquals("John", userToken.getFirstName());
        assertEquals("Doe", userToken.getLastName());
        assertEquals("<EMAIL>", userToken.getEmail());
        assertEquals("profile1|profile2", userToken.getProfileNames());
        assertEquals(Long.valueOf(1234567890L), userToken.getExpiryTime());
        assertEquals("server1", userToken.getServerName());
    }

    @Test
    void testAuthorizationFieldsInitialization() {
        // Test that new authorization fields are properly initialized
        UserToken newToken = new UserToken();

        // Assert - New fields should be null initially (not empty lists)
        assertNull(newToken.getRoles());
        assertNull(newToken.getPermissions());
        assertNull(newToken.getBusinessUnits());
        assertFalse(newToken.isReadOnlyAccess()); // boolean defaults to false
    }

    @Test
    void testImmutabilityOfReturnedCollections() {
        // Arrange
        userToken.setRoles(testRoles);
        userToken.setPermissions(testPermissions);
        userToken.setBusinessUnits(testBusinessUnits);

        // Act - Get collections and try to modify them
        List<String> returnedRoles = userToken.getRoles();
        List<String> returnedPermissions = userToken.getPermissions();
        List<BusinessUnitInfo> returnedBusinessUnits = userToken.getBusinessUnits();

        // Try to modify returned collections
        returnedRoles.add("NEW_ROLE");
        returnedPermissions.add("NEW_PERMISSION");
        returnedBusinessUnits.add(new BusinessUnitInfo(999L, "New BU"));

        // Assert - Original data should not be affected
        assertEquals(2, userToken.getRoles().size());
        assertEquals(4, userToken.getPermissions().size());
        assertEquals(2, userToken.getBusinessUnits().size());
    }
}