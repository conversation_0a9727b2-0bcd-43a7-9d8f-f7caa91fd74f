package com.recvueoktatoken.authorizer.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for BusinessUnitInfo class
 */
class BusinessUnitInfoTest {

    private BusinessUnitInfo businessUnitInfo;

    @BeforeEach
    void setUp() {
        businessUnitInfo = new BusinessUnitInfo(1L, "North America");
    }

    @Test
    void testDefaultConstructor() {
        BusinessUnitInfo info = new BusinessUnitInfo();
        
        assertNull(info.getBusinessUnitId());
        assertNull(info.getBusinessUnitName());
    }

    @Test
    void testParameterizedConstructor() {
        assertEquals(Long.valueOf(1L), businessUnitInfo.getBusinessUnitId());
        assertEquals("North America", businessUnitInfo.getBusinessUnitName());
    }

    @Test
    void testParameterizedConstructorWithNullValues() {
        BusinessUnitInfo info = new BusinessUnitInfo(null, null);
        
        assertNull(info.getBusinessUnitId());
        assertNull(info.getBusinessUnitName());
    }

    @Test
    void testSetBusinessUnitId() {
        businessUnitInfo.setBusinessUnitId(2L);
        assertEquals(Long.valueOf(2L), businessUnitInfo.getBusinessUnitId());
        
        businessUnitInfo.setBusinessUnitId(null);
        assertNull(businessUnitInfo.getBusinessUnitId());
    }

    @Test
    void testSetBusinessUnitName() {
        businessUnitInfo.setBusinessUnitName("Europe");
        assertEquals("Europe", businessUnitInfo.getBusinessUnitName());
        
        businessUnitInfo.setBusinessUnitName(null);
        assertNull(businessUnitInfo.getBusinessUnitName());
        
        businessUnitInfo.setBusinessUnitName("");
        assertEquals("", businessUnitInfo.getBusinessUnitName());
    }

    @Test
    void testEquals() {
        BusinessUnitInfo info1 = new BusinessUnitInfo(1L, "North America");
        BusinessUnitInfo info2 = new BusinessUnitInfo(1L, "North America");
        BusinessUnitInfo info3 = new BusinessUnitInfo(2L, "Europe");
        BusinessUnitInfo info4 = new BusinessUnitInfo(1L, "Europe");
        BusinessUnitInfo info5 = new BusinessUnitInfo(null, "North America");
        BusinessUnitInfo info6 = new BusinessUnitInfo(1L, null);
        BusinessUnitInfo info7 = new BusinessUnitInfo(null, null);
        BusinessUnitInfo info8 = new BusinessUnitInfo(null, null);
        
        // Test reflexivity
        assertEquals(businessUnitInfo, businessUnitInfo);
        
        // Test symmetry
        assertEquals(info1, info2);
        assertEquals(info2, info1);
        
        // Test with different objects
        assertNotEquals(info1, info3);
        assertNotEquals(info1, info4);
        assertNotEquals(info1, info5);
        assertNotEquals(info1, info6);
        assertNotEquals(info1, info7);
        
        // Test with null values
        assertEquals(info7, info8);
        
        // Test with null object
        assertNotEquals(info1, null);
        
        // Test with different class
        assertNotEquals(info1, "string");
    }

    @Test
    void testHashCode() {
        BusinessUnitInfo info1 = new BusinessUnitInfo(1L, "North America");
        BusinessUnitInfo info2 = new BusinessUnitInfo(1L, "North America");
        BusinessUnitInfo info3 = new BusinessUnitInfo(2L, "Europe");
        
        // Equal objects should have equal hash codes
        assertEquals(info1.hashCode(), info2.hashCode());
        
        // Different objects may have different hash codes
        assertNotEquals(info1.hashCode(), info3.hashCode());
        
        // Test with null values
        BusinessUnitInfo infoWithNulls = new BusinessUnitInfo(null, null);
        assertNotNull(infoWithNulls.hashCode()); // Should not throw exception
    }

    @Test
    void testToString() {
        String result = businessUnitInfo.toString();
        
        assertNotNull(result);
        assertTrue(result.contains("BusinessUnitInfo"));
        assertTrue(result.contains("businessUnitId=1"));
        assertTrue(result.contains("businessUnitName='North America'"));
    }

    @Test
    void testToStringWithNullValues() {
        BusinessUnitInfo info = new BusinessUnitInfo(null, null);
        String result = info.toString();
        
        assertNotNull(result);
        assertTrue(result.contains("BusinessUnitInfo"));
        assertTrue(result.contains("businessUnitId=null"));
        assertTrue(result.contains("businessUnitName='null'"));
    }

    @Test
    void testSerializable() {
        // Test that the class implements Serializable properly
        assertTrue(businessUnitInfo instanceof java.io.Serializable);
        
        // Test serialVersionUID exists
        assertEquals(1L, BusinessUnitInfo.serialVersionUID);
    }

    @Test
    void testEqualsContract() {
        BusinessUnitInfo x = new BusinessUnitInfo(1L, "Test");
        BusinessUnitInfo y = new BusinessUnitInfo(1L, "Test");
        BusinessUnitInfo z = new BusinessUnitInfo(1L, "Test");
        
        // Reflexive: x.equals(x) should return true
        assertTrue(x.equals(x));
        
        // Symmetric: x.equals(y) should return true if and only if y.equals(x) returns true
        assertTrue(x.equals(y));
        assertTrue(y.equals(x));
        
        // Transitive: if x.equals(y) returns true and y.equals(z) returns true, then x.equals(z) should return true
        assertTrue(x.equals(y));
        assertTrue(y.equals(z));
        assertTrue(x.equals(z));
        
        // Consistent: multiple invocations should return the same result
        assertTrue(x.equals(y));
        assertTrue(x.equals(y));
        
        // x.equals(null) should return false
        assertFalse(x.equals(null));
    }
}