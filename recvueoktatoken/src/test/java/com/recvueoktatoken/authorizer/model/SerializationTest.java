package com.recvueoktatoken.authorizer.model;

import org.junit.jupiter.api.Test;

import java.io.*;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for serialization and deserialization of authorization data structures.
 * This is important for Redis caching functionality.
 */
class SerializationTest {

    @Test
    void testBusinessUnitInfoSerialization() throws IOException, ClassNotFoundException {
        BusinessUnitInfo original = new BusinessUnitInfo(1L, "North America");
        
        // Serialize
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(baos);
        oos.writeObject(original);
        oos.close();
        
        // Deserialize
        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        ObjectInputStream ois = new ObjectInputStream(bais);
        BusinessUnitInfo deserialized = (BusinessUnitInfo) ois.readObject();
        ois.close();
        
        // Verify
        assertEquals(original, deserialized);
        assertEquals(original.getBusinessUnitId(), deserialized.getBusinessUnitId());
        assertEquals(original.getBusinessUnitName(), deserialized.getBusinessUnitName());
    }

    @Test
    void testBusinessUnitInfoSerializationWithNulls() throws IOException, ClassNotFoundException {
        BusinessUnitInfo original = new BusinessUnitInfo(null, null);
        
        // Serialize
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(baos);
        oos.writeObject(original);
        oos.close();
        
        // Deserialize
        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        ObjectInputStream ois = new ObjectInputStream(bais);
        BusinessUnitInfo deserialized = (BusinessUnitInfo) ois.readObject();
        ois.close();
        
        // Verify
        assertEquals(original, deserialized);
        assertNull(deserialized.getBusinessUnitId());
        assertNull(deserialized.getBusinessUnitName());
    }

    @Test
    void testAuthorizationContextSerialization() throws IOException, ClassNotFoundException {
        List<String> roles = Arrays.asList("ADMIN", "USER_MANAGER");
        List<String> permissions = Arrays.asList("USER_CREATE", "USER_READ", "USER_UPDATE");
        List<BusinessUnitInfo> businessUnits = Arrays.asList(
                new BusinessUnitInfo(1L, "North America"),
                new BusinessUnitInfo(2L, "Europe")
        );
        
        AuthorizationContext original = new AuthorizationContext(roles, permissions, businessUnits, true);
        original.setCacheTimestamp(1640995200000L); // Set fixed timestamp for testing
        
        // Serialize
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(baos);
        oos.writeObject(original);
        oos.close();
        
        // Deserialize
        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        ObjectInputStream ois = new ObjectInputStream(bais);
        AuthorizationContext deserialized = (AuthorizationContext) ois.readObject();
        ois.close();
        
        // Verify
        assertEquals(original.getRoleNames().size(), deserialized.getRoleNames().size());
        assertEquals(original.getPermissionNames().size(), deserialized.getPermissionNames().size());
        assertEquals(original.getBusinessUnits().size(), deserialized.getBusinessUnits().size());
        assertEquals(original.isReadOnlyAccess(), deserialized.isReadOnlyAccess());
        assertEquals(original.getCacheTimestamp(), deserialized.getCacheTimestamp());
        
        // Verify role names
        assertTrue(deserialized.hasRole("ADMIN"));
        assertTrue(deserialized.hasRole("USER_MANAGER"));
        
        // Verify permissions
        assertTrue(deserialized.hasPermission("USER_CREATE"));
        assertTrue(deserialized.hasPermission("USER_READ"));
        assertTrue(deserialized.hasPermission("USER_UPDATE"));
        
        // Verify business units
        assertTrue(deserialized.hasBusinessUnitAccess(1L));
        assertTrue(deserialized.hasBusinessUnitAccess(2L));
        
        // Verify business unit details
        assertEquals("North America", deserialized.getBusinessUnits().get(0).getBusinessUnitName());
        assertEquals("Europe", deserialized.getBusinessUnits().get(1).getBusinessUnitName());
    }

    @Test
    void testAuthorizationContextSerializationEmpty() throws IOException, ClassNotFoundException {
        AuthorizationContext original = new AuthorizationContext();
        
        // Serialize
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(baos);
        oos.writeObject(original);
        oos.close();
        
        // Deserialize
        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        ObjectInputStream ois = new ObjectInputStream(bais);
        AuthorizationContext deserialized = (AuthorizationContext) ois.readObject();
        ois.close();
        
        // Verify
        assertTrue(deserialized.isEmpty());
        assertFalse(deserialized.isReadOnlyAccess());
        assertTrue(deserialized.getCacheTimestamp() > 0);
        assertNotNull(deserialized.getRoleNames());
        assertNotNull(deserialized.getPermissionNames());
        assertNotNull(deserialized.getBusinessUnits());
    }

    @Test
    void testSerializationPerformance() throws IOException, ClassNotFoundException {
        // Create a large authorization context to test performance
        List<String> roles = Arrays.asList("ROLE1", "ROLE2", "ROLE3", "ROLE4", "ROLE5");
        List<String> permissions = Arrays.asList(
                "PERM1", "PERM2", "PERM3", "PERM4", "PERM5",
                "PERM6", "PERM7", "PERM8", "PERM9", "PERM10"
        );
        List<BusinessUnitInfo> businessUnits = Arrays.asList(
                new BusinessUnitInfo(1L, "Business Unit 1"),
                new BusinessUnitInfo(2L, "Business Unit 2"),
                new BusinessUnitInfo(3L, "Business Unit 3"),
                new BusinessUnitInfo(4L, "Business Unit 4"),
                new BusinessUnitInfo(5L, "Business Unit 5")
        );
        
        AuthorizationContext original = new AuthorizationContext(roles, permissions, businessUnits, false);
        
        long startTime = System.currentTimeMillis();
        
        // Serialize
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(baos);
        oos.writeObject(original);
        oos.close();
        
        // Deserialize
        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        ObjectInputStream ois = new ObjectInputStream(bais);
        AuthorizationContext deserialized = (AuthorizationContext) ois.readObject();
        ois.close();
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // Verify serialization was successful
        assertEquals(original.getRoleNames().size(), deserialized.getRoleNames().size());
        assertEquals(original.getPermissionNames().size(), deserialized.getPermissionNames().size());
        assertEquals(original.getBusinessUnits().size(), deserialized.getBusinessUnits().size());
        
        // Performance should be reasonable (less than 100ms for this small dataset)
        assertTrue(duration < 100, "Serialization took too long: " + duration + "ms");
        
        // Verify serialized data size is reasonable
        byte[] serializedData = baos.toByteArray();
        assertTrue(serializedData.length < 10000, "Serialized data too large: " + serializedData.length + " bytes");
    }

    @Test
    void testSerializationVersionCompatibility() throws IOException, ClassNotFoundException {
        // Test that serialVersionUID is properly defined
        assertEquals(1L, BusinessUnitInfo.serialVersionUID);
        assertEquals(1L, AuthorizationContext.serialVersionUID);
        
        // Create objects and verify they can be serialized/deserialized
        BusinessUnitInfo businessUnit = new BusinessUnitInfo(1L, "Test Unit");
        AuthorizationContext context = new AuthorizationContext();
        
        // This should not throw any exceptions
        ByteArrayOutputStream baos1 = new ByteArrayOutputStream();
        ObjectOutputStream oos1 = new ObjectOutputStream(baos1);
        oos1.writeObject(businessUnit);
        oos1.close();
        
        ByteArrayOutputStream baos2 = new ByteArrayOutputStream();
        ObjectOutputStream oos2 = new ObjectOutputStream(baos2);
        oos2.writeObject(context);
        oos2.close();
        
        // Verify we can read them back
        ByteArrayInputStream bais1 = new ByteArrayInputStream(baos1.toByteArray());
        ObjectInputStream ois1 = new ObjectInputStream(bais1);
        BusinessUnitInfo deserializedBusinessUnit = (BusinessUnitInfo) ois1.readObject();
        ois1.close();
        
        ByteArrayInputStream bais2 = new ByteArrayInputStream(baos2.toByteArray());
        ObjectInputStream ois2 = new ObjectInputStream(bais2);
        AuthorizationContext deserializedContext = (AuthorizationContext) ois2.readObject();
        ois2.close();
        
        assertEquals(businessUnit, deserializedBusinessUnit);
        assertNotNull(deserializedContext);
    }
}