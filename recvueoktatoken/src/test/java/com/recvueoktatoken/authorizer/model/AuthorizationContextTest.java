package com.recvueoktatoken.authorizer.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for AuthorizationContext class
 */
class AuthorizationContextTest {

    private AuthorizationContext authorizationContext;
    private List<String> testRoles;
    private List<String> testPermissions;
    private List<BusinessUnitInfo> testBusinessUnits;

    @BeforeEach
    void setUp() {
        testRoles = Arrays.asList("ADMIN", "USER_MANAGER");
        testPermissions = Arrays.asList("USER_CREATE", "USER_READ", "USER_UPDATE", "REPORT_VIEW");
        testBusinessUnits = Arrays.asList(
                new BusinessUnitInfo(1L, "North America"),
                new BusinessUnitInfo(2L, "Europe")
        );
        authorizationContext = new AuthorizationContext(testRoles, testPermissions, testBusinessUnits, false);
    }

    @Test
    void testDefaultConstructor() {
        AuthorizationContext context = new AuthorizationContext();
        
        assertNotNull(context.getRoleNames());
        assertNotNull(context.getPermissionNames());
        assertNotNull(context.getBusinessUnits());
        assertTrue(context.getRoleNames().isEmpty());
        assertTrue(context.getPermissionNames().isEmpty());
        assertTrue(context.getBusinessUnits().isEmpty());
        assertFalse(context.isReadOnlyAccess());
        assertTrue(context.getCacheTimestamp() > 0);
    }

    @Test
    void testParameterizedConstructor() {
        assertNotNull(authorizationContext.getRoleNames());
        assertNotNull(authorizationContext.getPermissionNames());
        assertNotNull(authorizationContext.getBusinessUnits());
        
        assertEquals(2, authorizationContext.getRoleNames().size());
        assertEquals(4, authorizationContext.getPermissionNames().size());
        assertEquals(2, authorizationContext.getBusinessUnits().size());
        assertFalse(authorizationContext.isReadOnlyAccess());
        assertTrue(authorizationContext.getCacheTimestamp() > 0);
    }

    @Test
    void testParameterizedConstructorWithNullValues() {
        AuthorizationContext context = new AuthorizationContext(null, null, null, true);
        
        assertNotNull(context.getRoleNames());
        assertNotNull(context.getPermissionNames());
        assertNotNull(context.getBusinessUnits());
        assertTrue(context.getRoleNames().isEmpty());
        assertTrue(context.getPermissionNames().isEmpty());
        assertTrue(context.getBusinessUnits().isEmpty());
        assertTrue(context.isReadOnlyAccess());
    }

    @Test
    void testSetRoleNames() {
        List<String> newRoles = Arrays.asList("VIEWER", "EDITOR");
        authorizationContext.setRoleNames(newRoles);
        
        assertEquals(2, authorizationContext.getRoleNames().size());
        assertTrue(authorizationContext.getRoleNames().contains("VIEWER"));
        assertTrue(authorizationContext.getRoleNames().contains("EDITOR"));
    }

    @Test
    void testSetRoleNamesWithNull() {
        authorizationContext.setRoleNames(null);
        
        assertNotNull(authorizationContext.getRoleNames());
        assertTrue(authorizationContext.getRoleNames().isEmpty());
    }

    @Test
    void testSetPermissionNames() {
        List<String> newPermissions = Arrays.asList("READ_ONLY", "WRITE_ACCESS");
        authorizationContext.setPermissionNames(newPermissions);
        
        assertEquals(2, authorizationContext.getPermissionNames().size());
        assertTrue(authorizationContext.getPermissionNames().contains("READ_ONLY"));
        assertTrue(authorizationContext.getPermissionNames().contains("WRITE_ACCESS"));
    }

    @Test
    void testSetPermissionNamesWithNull() {
        authorizationContext.setPermissionNames(null);
        
        assertNotNull(authorizationContext.getPermissionNames());
        assertTrue(authorizationContext.getPermissionNames().isEmpty());
    }

    @Test
    void testSetBusinessUnits() {
        List<BusinessUnitInfo> newBusinessUnits = Arrays.asList(
                new BusinessUnitInfo(3L, "Asia Pacific")
        );
        authorizationContext.setBusinessUnits(newBusinessUnits);
        
        assertEquals(1, authorizationContext.getBusinessUnits().size());
        assertEquals("Asia Pacific", authorizationContext.getBusinessUnits().get(0).getBusinessUnitName());
    }

    @Test
    void testSetBusinessUnitsWithNull() {
        authorizationContext.setBusinessUnits(null);
        
        assertNotNull(authorizationContext.getBusinessUnits());
        assertTrue(authorizationContext.getBusinessUnits().isEmpty());
    }

    @Test
    void testHasRole() {
        assertTrue(authorizationContext.hasRole("ADMIN"));
        assertTrue(authorizationContext.hasRole("USER_MANAGER"));
        assertFalse(authorizationContext.hasRole("VIEWER"));
        assertFalse(authorizationContext.hasRole(null));
    }

    @Test
    void testHasPermission() {
        assertTrue(authorizationContext.hasPermission("USER_CREATE"));
        assertTrue(authorizationContext.hasPermission("REPORT_VIEW"));
        assertFalse(authorizationContext.hasPermission("DELETE_ALL"));
        assertFalse(authorizationContext.hasPermission(null));
    }

    @Test
    void testHasBusinessUnitAccess() {
        assertTrue(authorizationContext.hasBusinessUnitAccess(1L));
        assertTrue(authorizationContext.hasBusinessUnitAccess(2L));
        assertFalse(authorizationContext.hasBusinessUnitAccess(3L));
        assertFalse(authorizationContext.hasBusinessUnitAccess(null));
    }

    @Test
    void testIsEmpty() {
        assertFalse(authorizationContext.isEmpty());
        
        AuthorizationContext emptyContext = new AuthorizationContext();
        assertTrue(emptyContext.isEmpty());
    }

    @Test
    void testReadOnlyAccess() {
        assertFalse(authorizationContext.isReadOnlyAccess());
        
        authorizationContext.setReadOnlyAccess(true);
        assertTrue(authorizationContext.isReadOnlyAccess());
    }

    @Test
    void testCacheTimestamp() {
        long initialTimestamp = authorizationContext.getCacheTimestamp();
        assertTrue(initialTimestamp > 0);
        
        long newTimestamp = System.currentTimeMillis() + 1000;
        authorizationContext.setCacheTimestamp(newTimestamp);
        assertEquals(newTimestamp, authorizationContext.getCacheTimestamp());
    }

    @Test
    void testToString() {
        String result = authorizationContext.toString();
        
        assertNotNull(result);
        assertTrue(result.contains("AuthorizationContext"));
        assertTrue(result.contains("roleNames"));
        assertTrue(result.contains("permissionNames"));
        assertTrue(result.contains("businessUnits"));
        assertTrue(result.contains("readOnlyAccess"));
        assertTrue(result.contains("cacheTimestamp"));
    }

    @Test
    void testImmutabilityOfLists() {
        // Test that modifying the original lists doesn't affect the context
        testRoles.add("NEW_ROLE");
        testPermissions.add("NEW_PERMISSION");
        testBusinessUnits.add(new BusinessUnitInfo(3L, "Test Unit"));
        
        assertEquals(2, authorizationContext.getRoleNames().size());
        assertEquals(4, authorizationContext.getPermissionNames().size());
        assertEquals(2, authorizationContext.getBusinessUnits().size());
        
        // Test that modifying returned lists doesn't affect the context
        authorizationContext.getRoleNames().add("ANOTHER_ROLE");
        authorizationContext.getPermissionNames().add("ANOTHER_PERMISSION");
        authorizationContext.getBusinessUnits().add(new BusinessUnitInfo(4L, "Another Unit"));
        
        // The context should still have the original values
        assertEquals(2, authorizationContext.getRoleNames().size());
        assertEquals(4, authorizationContext.getPermissionNames().size());
        assertEquals(2, authorizationContext.getBusinessUnits().size());
    }
}