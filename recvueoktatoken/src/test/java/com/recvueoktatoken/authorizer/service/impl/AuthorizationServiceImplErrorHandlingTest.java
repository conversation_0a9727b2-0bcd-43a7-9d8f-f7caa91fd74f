package com.recvueoktatoken.authorizer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import com.recvueoktatoken.authorizer.model.AuthorizationContext;
import com.recvueoktatoken.authorizer.model.BusinessUnitInfo;
import com.recvueoktatoken.authorizer.model.UserToken;
import com.recvueoktatoken.authorizer.monitoring.AuthorizationMetrics;
import com.recvueoktatoken.coreuser.dao.CoreUserDao;

/**
 * Unit tests for comprehensive error handling in AuthorizationServiceImpl
 */
@ExtendWith(MockitoExtension.class)
class AuthorizationServiceImplErrorHandlingTest {

    @Mock
    private CoreUserDao coreUserDao;

    @Mock
    private RedisTemplate<String, UserToken> redisTokenTemplate;

    @Mock
    private ValueOperations<String, UserToken> valueOperations;

    @Mock
    private AuthorizationMetrics authorizationMetrics;

    @InjectMocks
    private AuthorizationServiceImpl authorizationService;

    private String testUsername = "testuser";
    private List<String> testRoles;
    private List<String> testPermissions;
    private List<BusinessUnitInfo> testBusinessUnits;

    @BeforeEach
    void setUp() {
        testRoles = Arrays.asList("ADMIN", "USER_MANAGER");
        testPermissions = Arrays.asList("USER_CREATE", "USER_READ", "USER_UPDATE", "REPORT_VIEW");
        testBusinessUnits = Arrays.asList(
                new BusinessUnitInfo(1L, "North America"),
                new BusinessUnitInfo(2L, "Europe")
        );

        when(redisTokenTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void testGetUserAuthorizationContext_DatabaseErrorWithCacheFallback() throws Exception {
        // Arrange
        UserToken cachedToken = UserToken.fromAuthorizationContext(
                new AuthorizationContext(testRoles, testPermissions, testBusinessUnits, false),
                testUsername, "John", "Doe", "<EMAIL>", "jwt-token",
                (System.currentTimeMillis() / 1000) + 3600, "server");

        // First call returns null (cache miss), second call returns cached data (fallback)
        when(valueOperations.get(anyString()))
                .thenReturn(null)
                .thenReturn(cachedToken);

        // Database throws connectivity error
        when(coreUserDao.getUserRoleNames(testUsername))
                .thenThrow(new RuntimeException("Database connection failed"));

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getRoleNames().size());
        assertEquals(4, result.getPermissionNames().size());
        assertEquals(2, result.getBusinessUnits().size());

        // Verify metrics were recorded
        verify(authorizationMetrics).recordCacheMiss();
        verify(authorizationMetrics).recordDatabaseError();
        verify(authorizationMetrics).recordDatabaseFallback();
        verify(authorizationMetrics, times(2)).recordResponseTime(anyLong());
    }

    @Test
    void testGetUserAuthorizationContext_DatabaseErrorWithoutCacheFallback() throws Exception {
        // Arrange
        when(valueOperations.get(anyString())).thenReturn(null);
        when(coreUserDao.getUserRoleNames(testUsername))
                .thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        Exception exception = assertThrows(Exception.class, () -> {
            authorizationService.getUserAuthorizationContext(testUsername);
        });

        assertTrue(exception.getMessage().contains("Failed to retrieve authorization context"));

        // Verify metrics were recorded
        verify(authorizationMetrics).recordCacheMiss();
        verify(authorizationMetrics).recordDatabaseError();
        verify(authorizationMetrics, never()).recordDatabaseFallback();
    }

    @Test
    void testGetUserAuthorizationContext_RedisConnectionFailure() throws Exception {
        // Arrange
        when(valueOperations.get(anyString())).thenThrow(new RedisConnectionFailureException("Redis is down"));
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getRoleNames().size());

        // Verify cache error was recorded but database query succeeded
        verify(authorizationMetrics).recordCacheError();
        verify(authorizationMetrics).recordDatabaseQuery();
        verify(authorizationMetrics).recordResponseTime(anyLong());
    }

    @Test
    void testGetUserAuthorizationContext_EmptyAuthorizationData() throws Exception {
        // Arrange
        when(valueOperations.get(anyString())).thenReturn(null);
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(Arrays.asList());
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(Arrays.asList());
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(Arrays.asList());
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // Verify empty context was recorded
        verify(authorizationMetrics).recordEmptyAuthorizationContext();
        verify(authorizationMetrics).recordDatabaseQuery();
        verify(authorizationMetrics).recordResponseTime(anyLong());
    }

    @Test
    void testGetUserAuthorizationContext_ReadOnlyUser() throws Exception {
        // Arrange
        when(valueOperations.get(anyString())).thenReturn(null);
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(true);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertTrue(result.isReadOnlyAccess());

        // Verify read-only user was recorded
        verify(authorizationMetrics).recordReadOnlyUser();
        verify(authorizationMetrics).recordDatabaseQuery();
        verify(authorizationMetrics).recordResponseTime(anyLong());
    }

    @Test
    void testGetUserAuthorizationContext_CacheHit() throws Exception {
        // Arrange
        UserToken cachedToken = UserToken.fromAuthorizationContext(
                new AuthorizationContext(testRoles, testPermissions, testBusinessUnits, false),
                testUsername, "John", "Doe", "<EMAIL>", "jwt-token",
                (System.currentTimeMillis() / 1000) + 3600, "server");

        when(valueOperations.get(anyString())).thenReturn(cachedToken);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getRoleNames().size());

        // Verify cache hit was recorded and database was not called
        verify(authorizationMetrics).recordCacheHit();
        verify(authorizationMetrics).recordResponseTime(anyLong());
        verify(authorizationMetrics, never()).recordDatabaseQuery();
        verify(coreUserDao, never()).getUserRoleNames(any());
    }

    @Test
    void testGetUserAuthorizationContext_CacheHitWithReadOnlyUser() throws Exception {
        // Arrange
        UserToken cachedToken = UserToken.fromAuthorizationContext(
                new AuthorizationContext(testRoles, testPermissions, testBusinessUnits, true),
                testUsername, "John", "Doe", "<EMAIL>", "jwt-token",
                (System.currentTimeMillis() / 1000) + 3600, "server");

        when(valueOperations.get(anyString())).thenReturn(cachedToken);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertTrue(result.isReadOnlyAccess());

        // Verify read-only user was recorded from cache
        verify(authorizationMetrics).recordCacheHit();
        verify(authorizationMetrics).recordReadOnlyUser();
        verify(authorizationMetrics).recordResponseTime(anyLong());
    }

    @Test
    void testGetUserAuthorizationContext_CacheHitWithEmptyData() throws Exception {
        // Arrange
        UserToken cachedToken = UserToken.fromAuthorizationContext(
                new AuthorizationContext(),
                testUsername, "John", "Doe", "<EMAIL>", "jwt-token",
                (System.currentTimeMillis() / 1000) + 3600, "server");

        when(valueOperations.get(anyString())).thenReturn(cachedToken);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // Verify empty context was recorded from cache
        verify(authorizationMetrics).recordCacheHit();
        verify(authorizationMetrics).recordEmptyAuthorizationContext();
        verify(authorizationMetrics).recordResponseTime(anyLong());
    }

    @Test
    void testGetUserAuthorizationContext_SlowOperation() throws Exception {
        // Arrange
        when(valueOperations.get(anyString())).thenReturn(null);
        
        // Simulate slow database operation
        when(coreUserDao.getUserRoleNames(testUsername)).thenAnswer(invocation -> {
            Thread.sleep(100); // Simulate delay
            return testRoles;
        });
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);

        // Verify response time was recorded (should be > 100ms due to sleep)
        verify(authorizationMetrics).recordResponseTime(longThat(time -> time >= 100));
    }

    @Test
    void testInvalidateUserAuthorizationCache_RedisError() {
        // Arrange
        when(redisTokenTemplate.delete(anyString())).thenThrow(new RedisConnectionFailureException("Redis is down"));

        // Act - Should not throw exception
        assertDoesNotThrow(() -> {
            authorizationService.invalidateUserAuthorizationCache(testUsername);
        });

        // Verify error was handled gracefully
        verify(redisTokenTemplate).delete(anyString());
    }

    @Test
    void testGetCachedAuthorizationContext_RedisError() {
        // Arrange
        when(valueOperations.get(anyString())).thenThrow(new RedisConnectionFailureException("Redis is down"));

        // Act
        AuthorizationContext result = authorizationService.getCachedAuthorizationContext(testUsername);

        // Assert
        assertNull(result);

        // Verify cache error was recorded
        verify(authorizationMetrics).recordCacheError();
    }

    @Test
    void testGetCachedAuthorizationContext_ExpiredToken() {
        // Arrange
        UserToken expiredToken = UserToken.fromAuthorizationContext(
                new AuthorizationContext(testRoles, testPermissions, testBusinessUnits, false),
                testUsername, "John", "Doe", "<EMAIL>", "jwt-token",
                (System.currentTimeMillis() / 1000) - 3600, "server"); // Expired 1 hour ago

        when(valueOperations.get(anyString())).thenReturn(expiredToken);
        when(redisTokenTemplate.delete(anyString())).thenReturn(true);

        // Act
        AuthorizationContext result = authorizationService.getCachedAuthorizationContext(testUsername);

        // Assert
        assertNull(result);

        // Verify expired token was deleted
        verify(redisTokenTemplate).delete(anyString());
    }

    @Test
    void testDatabaseConnectivityIssueDetection() throws Exception {
        // Test various database connectivity issues
        String[] connectivityErrors = {
                "connection timeout",
                "network unreachable",
                "connection refused",
                "Connection failed"
        };

        for (String errorMessage : connectivityErrors) {
            // Arrange
            when(valueOperations.get(anyString())).thenReturn(null);
            when(coreUserDao.getUserRoleNames(testUsername))
                    .thenThrow(new RuntimeException(errorMessage));

            // Act & Assert
            Exception exception = assertThrows(Exception.class, () -> {
                authorizationService.getUserAuthorizationContext(testUsername);
            });

            assertTrue(exception.getMessage().contains("Failed to retrieve authorization context"));

            // Reset mocks for next iteration
            reset(authorizationMetrics);
        }
    }

    @Test
    void testMultipleErrorScenarios() throws Exception {
        // Test handling of multiple consecutive errors
        
        // First call: Cache error
        when(valueOperations.get(anyString())).thenThrow(new RedisConnectionFailureException("Redis down"));
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        AuthorizationContext result1 = authorizationService.getUserAuthorizationContext(testUsername);
        assertNotNull(result1);

        // Second call: Database error with cache fallback
        reset(valueOperations, coreUserDao);
        when(redisTokenTemplate.opsForValue()).thenReturn(valueOperations);
        
        UserToken cachedToken = UserToken.fromAuthorizationContext(
                new AuthorizationContext(testRoles, testPermissions, testBusinessUnits, false),
                testUsername, "John", "Doe", "<EMAIL>", "jwt-token",
                (System.currentTimeMillis() / 1000) + 3600, "server");

        when(valueOperations.get(anyString()))
                .thenReturn(null)
                .thenReturn(cachedToken);
        when(coreUserDao.getUserRoleNames(testUsername))
                .thenThrow(new RuntimeException("Database connection failed"));

        AuthorizationContext result2 = authorizationService.getUserAuthorizationContext(testUsername);
        assertNotNull(result2);

        // Verify both scenarios were handled properly
        verify(authorizationMetrics, times(2)).recordCacheError();
        verify(authorizationMetrics).recordDatabaseError();
        verify(authorizationMetrics).recordDatabaseFallback();
    }

    @Test
    void testErrorHandlingWithNullValues() throws Exception {
        // Test handling of null values from DAO
        when(valueOperations.get(anyString())).thenReturn(null);
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(null);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(null);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(null);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRoleNames());
        assertNotNull(result.getPermissionNames());
        assertNotNull(result.getBusinessUnits());
        assertTrue(result.getRoleNames().isEmpty());
        assertTrue(result.getPermissionNames().isEmpty());
        assertTrue(result.getBusinessUnits().isEmpty());

        // Verify empty context was recorded
        verify(authorizationMetrics).recordEmptyAuthorizationContext();
    }
}