package com.recvueoktatoken.authorizer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.recvueoktatoken.authorizer.model.AuthorizationContext;
import com.recvueoktatoken.authorizer.model.BusinessUnitInfo;
import com.recvueoktatoken.coreuser.dao.CoreUserDao;

/**
 * Unit tests for AuthorizationServiceImpl
 */
@ExtendWith(MockitoExtension.class)
class AuthorizationServiceImplTest {

    @Mock
    private CoreUserDao coreUserDao;

    @InjectMocks
    private AuthorizationServiceImpl authorizationService;

    private String testUsername = "testuser";
    private List<String> testRoles;
    private List<String> testPermissions;
    private List<BusinessUnitInfo> testBusinessUnits;

    @BeforeEach
    void setUp() {
        testRoles = Arrays.asList("ADMIN", "USER_MANAGER");
        testPermissions = Arrays.asList("USER_CREATE", "USER_READ", "USER_UPDATE", "REPORT_VIEW");
        testBusinessUnits = Arrays.asList(
                new BusinessUnitInfo(1L, "North America"),
                new BusinessUnitInfo(2L, "Europe")
        );
    }

    @Test
    void testGetUserAuthorizationContext_Success() throws Exception {
        // Arrange
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getRoleNames().size());
        assertEquals(4, result.getPermissionNames().size());
        assertEquals(2, result.getBusinessUnits().size());
        assertFalse(result.isReadOnlyAccess());
        
        assertTrue(result.hasRole("ADMIN"));
        assertTrue(result.hasRole("USER_MANAGER"));
        assertTrue(result.hasPermission("USER_CREATE"));
        assertTrue(result.hasBusinessUnitAccess(1L));
        assertTrue(result.hasBusinessUnitAccess(2L));
        
        verify(coreUserDao).getUserRoleNames(testUsername);
        verify(coreUserDao).getUserPermissionNames(testUsername);
        verify(coreUserDao).getUserBusinessUnits(testUsername);
        verify(coreUserDao).isReadOnlyUser(testUsername);
    }

    @Test
    void testGetUserAuthorizationContext_ReadOnlyUser() throws Exception {
        // Arrange
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(true);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertTrue(result.isReadOnlyAccess());
    }

    @Test
    void testGetUserAuthorizationContext_EmptyData() throws Exception {
        // Arrange
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(Arrays.asList());
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(Arrays.asList());
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(Arrays.asList());
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        assertTrue(result.getRoleNames().isEmpty());
        assertTrue(result.getPermissionNames().isEmpty());
        assertTrue(result.getBusinessUnits().isEmpty());
        assertFalse(result.isReadOnlyAccess());
    }

    @Test
    void testGetUserAuthorizationContext_NullUsername() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            authorizationService.getUserAuthorizationContext(null);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            authorizationService.getUserAuthorizationContext("");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            authorizationService.getUserAuthorizationContext("   ");
        });
    }

    @Test
    void testGetUserAuthorizationContext_DatabaseError() throws Exception {
        // Arrange
        when(coreUserDao.getUserRoleNames(testUsername)).thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        Exception exception = assertThrows(Exception.class, () -> {
            authorizationService.getUserAuthorizationContext(testUsername);
        });
        
        assertTrue(exception.getMessage().contains("Failed to retrieve authorization context"));
    }

    @Test
    void testGetUserAuthorizationContext_NullDataFromDao() throws Exception {
        // Arrange
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(null);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(null);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(null);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRoleNames());
        assertNotNull(result.getPermissionNames());
        assertNotNull(result.getBusinessUnits());
        assertTrue(result.getRoleNames().isEmpty());
        assertTrue(result.getPermissionNames().isEmpty());
        assertTrue(result.getBusinessUnits().isEmpty());
    }

    @Test
    void testIsUserAuthorizedForResource_Success() throws Exception {
        // Arrange
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        boolean result = authorizationService.isUserAuthorizedForResource(testUsername, "USER", "CREATE");

        // Assert
        assertTrue(result);
        verify(coreUserDao).getUserRoleNames(testUsername);
        verify(coreUserDao).getUserPermissionNames(testUsername);
        verify(coreUserDao).getUserBusinessUnits(testUsername);
        verify(coreUserDao).isReadOnlyUser(testUsername);
    }

    @Test
    void testIsUserAuthorizedForResource_Denied() throws Exception {
        // Arrange
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        boolean result = authorizationService.isUserAuthorizedForResource(testUsername, "ADMIN", "DELETE");

        // Assert
        assertFalse(result); // ADMIN_DELETE is not in testPermissions
    }

    @Test
    void testIsUserAuthorizedForResource_CaseInsensitive() throws Exception {
        // Arrange
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        boolean result = authorizationService.isUserAuthorizedForResource(testUsername, "user", "create");

        // Assert
        assertTrue(result); // Should work with lowercase
    }

    @Test
    void testIsUserAuthorizedForResource_InvalidParameters() {
        // Act & Assert
        assertFalse(authorizationService.isUserAuthorizedForResource(null, "USER", "CREATE"));
        assertFalse(authorizationService.isUserAuthorizedForResource("", "USER", "CREATE"));
        assertFalse(authorizationService.isUserAuthorizedForResource("   ", "USER", "CREATE"));
        
        assertFalse(authorizationService.isUserAuthorizedForResource(testUsername, null, "CREATE"));
        assertFalse(authorizationService.isUserAuthorizedForResource(testUsername, "", "CREATE"));
        assertFalse(authorizationService.isUserAuthorizedForResource(testUsername, "   ", "CREATE"));
        
        assertFalse(authorizationService.isUserAuthorizedForResource(testUsername, "USER", null));
        assertFalse(authorizationService.isUserAuthorizedForResource(testUsername, "USER", ""));
        assertFalse(authorizationService.isUserAuthorizedForResource(testUsername, "USER", "   "));
    }

    @Test
    void testIsUserAuthorizedForResource_DatabaseError() throws Exception {
        // Arrange
        when(coreUserDao.getUserRoleNames(testUsername)).thenThrow(new RuntimeException("Database error"));

        // Act
        boolean result = authorizationService.isUserAuthorizedForResource(testUsername, "USER", "CREATE");

        // Assert
        assertFalse(result); // Should deny access on error
    }

    @Test
    void testGetCachedAuthorizationContext_NotImplemented() {
        // Act
        AuthorizationContext result = authorizationService.getCachedAuthorizationContext(testUsername);

        // Assert
        assertNull(result); // Should return null as cache is not implemented yet
    }

    @Test
    void testGetCachedAuthorizationContext_InvalidUsername() {
        // Act & Assert
        assertNull(authorizationService.getCachedAuthorizationContext(null));
        assertNull(authorizationService.getCachedAuthorizationContext(""));
        assertNull(authorizationService.getCachedAuthorizationContext("   "));
    }

    @Test
    void testRefreshUserAuthorizationContext_Success() throws Exception {
        // Arrange
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        AuthorizationContext result = authorizationService.refreshUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getRoleNames().size());
        assertEquals(4, result.getPermissionNames().size());
        assertEquals(2, result.getBusinessUnits().size());
        assertFalse(result.isReadOnlyAccess());
        
        verify(coreUserDao).getUserRoleNames(testUsername);
        verify(coreUserDao).getUserPermissionNames(testUsername);
        verify(coreUserDao).getUserBusinessUnits(testUsername);
        verify(coreUserDao).isReadOnlyUser(testUsername);
    }

    @Test
    void testRefreshUserAuthorizationContext_NullUsername() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            authorizationService.refreshUserAuthorizationContext(null);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            authorizationService.refreshUserAuthorizationContext("");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            authorizationService.refreshUserAuthorizationContext("   ");
        });
    }

    @Test
    void testRefreshUserAuthorizationContext_DatabaseError() throws Exception {
        // Arrange
        when(coreUserDao.getUserRoleNames(testUsername)).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        Exception exception = assertThrows(Exception.class, () -> {
            authorizationService.refreshUserAuthorizationContext(testUsername);
        });
        
        assertTrue(exception.getMessage().contains("Failed to refresh authorization context"));
    }

    @Test
    void testInvalidateUserAuthorizationCache_Success() {
        // Act - Should not throw exception
        assertDoesNotThrow(() -> {
            authorizationService.invalidateUserAuthorizationCache(testUsername);
        });
    }

    @Test
    void testInvalidateUserAuthorizationCache_InvalidUsername() {
        // Act - Should not throw exception even with invalid usernames
        assertDoesNotThrow(() -> {
            authorizationService.invalidateUserAuthorizationCache(null);
            authorizationService.invalidateUserAuthorizationCache("");
            authorizationService.invalidateUserAuthorizationCache("   ");
        });
    }

    @Test
    void testMultipleCallsConsistency() throws Exception {
        // Arrange
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        AuthorizationContext result1 = authorizationService.getUserAuthorizationContext(testUsername);
        AuthorizationContext result2 = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(result1.getRoleNames().size(), result2.getRoleNames().size());
        assertEquals(result1.getPermissionNames().size(), result2.getPermissionNames().size());
        assertEquals(result1.getBusinessUnits().size(), result2.getBusinessUnits().size());
        assertEquals(result1.isReadOnlyAccess(), result2.isReadOnlyAccess());
        
        // Verify DAO methods are called for each request (no caching yet)
        verify(coreUserDao, times(2)).getUserRoleNames(testUsername);
        verify(coreUserDao, times(2)).getUserPermissionNames(testUsername);
        verify(coreUserDao, times(2)).getUserBusinessUnits(testUsername);
        verify(coreUserDao, times(2)).isReadOnlyUser(testUsername);
    }

    @Test
    void testAuthorizationContextIntegrity() throws Exception {
        // Arrange
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert - Verify defensive copying (modifying returned lists shouldn't affect internal state)
        List<String> returnedRoles = result.getRoleNames();
        List<String> returnedPermissions = result.getPermissionNames();
        List<BusinessUnitInfo> returnedBusinessUnits = result.getBusinessUnits();
        
        int originalRolesSize = returnedRoles.size();
        int originalPermissionsSize = returnedPermissions.size();
        int originalBusinessUnitsSize = returnedBusinessUnits.size();
        
        // Try to modify returned collections
        returnedRoles.add("NEW_ROLE");
        returnedPermissions.add("NEW_PERMISSION");
        returnedBusinessUnits.add(new BusinessUnitInfo(999L, "New BU"));
        
        // Get context again and verify it wasn't affected
        AuthorizationContext result2 = authorizationService.getUserAuthorizationContext(testUsername);
        assertEquals(originalRolesSize, result2.getRoleNames().size());
        assertEquals(originalPermissionsSize, result2.getPermissionNames().size());
        assertEquals(originalBusinessUnitsSize, result2.getBusinessUnits().size());
    }

    @Test
    void testPermissionFormatting() throws Exception {
        // Arrange
        List<String> permissions = Arrays.asList("USER_CREATE", "REPORT_VIEW", "ADMIN_MANAGE");
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(Arrays.asList());
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(permissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(Arrays.asList());
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act & Assert
        assertTrue(authorizationService.isUserAuthorizedForResource(testUsername, "USER", "CREATE"));
        assertTrue(authorizationService.isUserAuthorizedForResource(testUsername, "REPORT", "VIEW"));
        assertTrue(authorizationService.isUserAuthorizedForResource(testUsername, "ADMIN", "MANAGE"));
        
        // Test case insensitive
        assertTrue(authorizationService.isUserAuthorizedForResource(testUsername, "user", "create"));
        assertTrue(authorizationService.isUserAuthorizedForResource(testUsername, "Report", "View"));
        
        // Test non-existent permission
        assertFalse(authorizationService.isUserAuthorizedForResource(testUsername, "USER", "DELETE"));
    }
}