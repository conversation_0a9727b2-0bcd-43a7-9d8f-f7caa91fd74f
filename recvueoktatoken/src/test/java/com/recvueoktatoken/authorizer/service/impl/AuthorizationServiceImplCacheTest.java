package com.recvueoktatoken.authorizer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import com.recvueoktatoken.authorizer.model.AuthorizationContext;
import com.recvueoktatoken.authorizer.model.BusinessUnitInfo;
import com.recvueoktatoken.authorizer.model.UserToken;
import com.recvueoktatoken.common.multitenancy.TenantContext;
import com.recvueoktatoken.coreuser.dao.CoreUserDao;

/**
 * Unit tests for Redis caching functionality in AuthorizationServiceImpl
 */
@ExtendWith(MockitoExtension.class)
class AuthorizationServiceImplCacheTest {

    @Mock
    private CoreUserDao coreUserDao;

    @Mock
    private RedisTemplate<String, UserToken> redisTokenTemplate;

    @Mock
    private ValueOperations<String, UserToken> valueOperations;

    @InjectMocks
    private AuthorizationServiceImpl authorizationService;

    private String testUsername = "testuser";
    private String testTenant = "testtenant";
    private List<String> testRoles;
    private List<String> testPermissions;
    private List<BusinessUnitInfo> testBusinessUnits;
    private AuthorizationContext testContext;

    @BeforeEach
    void setUp() {
        testRoles = Arrays.asList("ADMIN", "USER_MANAGER");
        testPermissions = Arrays.asList("USER_CREATE", "USER_READ", "USER_UPDATE", "REPORT_VIEW");
        testBusinessUnits = Arrays.asList(
                new BusinessUnitInfo(1L, "North America"),
                new BusinessUnitInfo(2L, "Europe")
        );
        testContext = new AuthorizationContext(testRoles, testPermissions, testBusinessUnits, false);

        when(redisTokenTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void testGetUserAuthorizationContext_CacheHit() throws Exception {
        // Arrange
        UserToken cachedToken = UserToken.fromAuthorizationContext(testContext, testUsername, 
                "John", "Doe", "<EMAIL>", "jwt-token", 
                (System.currentTimeMillis() / 1000) + 3600, "server");
        
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            when(valueOperations.get("auth_context:" + testTenant + ":" + testUsername)).thenReturn(cachedToken);

            // Act
            AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

            // Assert
            assertNotNull(result);
            assertEquals(2, result.getRoleNames().size());
            assertEquals(4, result.getPermissionNames().size());
            assertEquals(2, result.getBusinessUnits().size());
            assertFalse(result.isReadOnlyAccess());

            // Verify cache was checked but database was not called
            verify(valueOperations).get("auth_context:" + testTenant + ":" + testUsername);
            verify(coreUserDao, never()).getUserRoleNames(any());
            verify(coreUserDao, never()).getUserPermissionNames(any());
            verify(coreUserDao, never()).getUserBusinessUnits(any());
            verify(coreUserDao, never()).isReadOnlyUser(any());
        }
    }

    @Test
    void testGetUserAuthorizationContext_CacheMiss() throws Exception {
        // Arrange
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            when(valueOperations.get("auth_context:" + testTenant + ":" + testUsername)).thenReturn(null);
            
            when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
            when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
            when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
            when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

            // Act
            AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

            // Assert
            assertNotNull(result);
            assertEquals(2, result.getRoleNames().size());
            assertEquals(4, result.getPermissionNames().size());
            assertEquals(2, result.getBusinessUnits().size());
            assertFalse(result.isReadOnlyAccess());

            // Verify cache was checked, database was called, and result was cached
            verify(valueOperations).get("auth_context:" + testTenant + ":" + testUsername);
            verify(coreUserDao).getUserRoleNames(testUsername);
            verify(coreUserDao).getUserPermissionNames(testUsername);
            verify(coreUserDao).getUserBusinessUnits(testUsername);
            verify(coreUserDao).isReadOnlyUser(testUsername);
            verify(valueOperations).set(eq("auth_context:" + testTenant + ":" + testUsername), 
                                       any(UserToken.class), eq(58L), eq(TimeUnit.MINUTES));
        }
    }

    @Test
    void testGetUserAuthorizationContext_ExpiredCache() throws Exception {
        // Arrange
        UserToken expiredToken = UserToken.fromAuthorizationContext(testContext, testUsername, 
                "John", "Doe", "<EMAIL>", "jwt-token", 
                (System.currentTimeMillis() / 1000) - 3600, "server"); // Expired 1 hour ago
        
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            when(valueOperations.get("auth_context:" + testTenant + ":" + testUsername)).thenReturn(expiredToken);
            when(redisTokenTemplate.delete("auth_context:" + testTenant + ":" + testUsername)).thenReturn(true);
            
            when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
            when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
            when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
            when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

            // Act
            AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

            // Assert
            assertNotNull(result);
            assertEquals(2, result.getRoleNames().size());

            // Verify expired cache was deleted and database was called
            verify(redisTokenTemplate).delete("auth_context:" + testTenant + ":" + testUsername);
            verify(coreUserDao).getUserRoleNames(testUsername);
        }
    }

    @Test
    void testGetUserAuthorizationContext_DatabaseErrorWithCacheFallback() throws Exception {
        // Arrange
        UserToken cachedToken = UserToken.fromAuthorizationContext(testContext, testUsername, 
                "John", "Doe", "<EMAIL>", "jwt-token", 
                (System.currentTimeMillis() / 1000) + 3600, "server");
        
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            
            // First call returns null (cache miss), second call returns cached data (fallback)
            when(valueOperations.get("auth_context:" + testTenant + ":" + testUsername))
                .thenReturn(null)
                .thenReturn(cachedToken);
            
            when(coreUserDao.getUserRoleNames(testUsername))
                .thenThrow(new RuntimeException("Database connection failed"));

            // Act
            AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

            // Assert
            assertNotNull(result);
            assertEquals(2, result.getRoleNames().size());

            // Verify cache was checked twice (initial check and fallback)
            verify(valueOperations, times(2)).get("auth_context:" + testTenant + ":" + testUsername);
        }
    }

    @Test
    void testGetUserAuthorizationContext_RedisConnectionFailure() throws Exception {
        // Arrange
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            when(valueOperations.get(any())).thenThrow(new RedisConnectionFailureException("Redis is down"));
            
            when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
            when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
            when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
            when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

            // Act
            AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

            // Assert
            assertNotNull(result);
            assertEquals(2, result.getRoleNames().size());

            // Verify database was still called despite Redis failure
            verify(coreUserDao).getUserRoleNames(testUsername);
        }
    }

    @Test
    void testInvalidateUserAuthorizationCache_Success() {
        // Arrange
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            when(redisTokenTemplate.delete("auth_context:" + testTenant + ":" + testUsername)).thenReturn(true);

            // Act
            authorizationService.invalidateUserAuthorizationCache(testUsername);

            // Assert
            verify(redisTokenTemplate).delete("auth_context:" + testTenant + ":" + testUsername);
        }
    }

    @Test
    void testInvalidateUserAuthorizationCache_NotFound() {
        // Arrange
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            when(redisTokenTemplate.delete("auth_context:" + testTenant + ":" + testUsername)).thenReturn(false);

            // Act
            authorizationService.invalidateUserAuthorizationCache(testUsername);

            // Assert
            verify(redisTokenTemplate).delete("auth_context:" + testTenant + ":" + testUsername);
        }
    }

    @Test
    void testInvalidateUserAuthorizationCache_RedisFailure() {
        // Arrange
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            when(redisTokenTemplate.delete(any())).thenThrow(new RedisConnectionFailureException("Redis is down"));

            // Act - Should not throw exception
            assertDoesNotThrow(() -> {
                authorizationService.invalidateUserAuthorizationCache(testUsername);
            });
        }
    }

    @Test
    void testInvalidateUserAuthorizationCache_InvalidUsername() {
        // Act - Should not throw exception
        assertDoesNotThrow(() -> {
            authorizationService.invalidateUserAuthorizationCache(null);
            authorizationService.invalidateUserAuthorizationCache("");
            authorizationService.invalidateUserAuthorizationCache("   ");
        });

        // Assert - Redis should not be called
        verify(redisTokenTemplate, never()).delete(any());
    }

    @Test
    void testGetCachedAuthorizationContext_Success() {
        // Arrange
        UserToken cachedToken = UserToken.fromAuthorizationContext(testContext, testUsername, 
                "John", "Doe", "<EMAIL>", "jwt-token", 
                (System.currentTimeMillis() / 1000) + 3600, "server");
        
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            when(valueOperations.get("auth_context:" + testTenant + ":" + testUsername)).thenReturn(cachedToken);

            // Act
            AuthorizationContext result = authorizationService.getCachedAuthorizationContext(testUsername);

            // Assert
            assertNotNull(result);
            assertEquals(2, result.getRoleNames().size());
            assertEquals(4, result.getPermissionNames().size());
            assertEquals(2, result.getBusinessUnits().size());
            assertFalse(result.isReadOnlyAccess());
        }
    }

    @Test
    void testGetCachedAuthorizationContext_NotFound() {
        // Arrange
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            when(valueOperations.get("auth_context:" + testTenant + ":" + testUsername)).thenReturn(null);

            // Act
            AuthorizationContext result = authorizationService.getCachedAuthorizationContext(testUsername);

            // Assert
            assertNull(result);
        }
    }

    @Test
    void testGetCachedAuthorizationContext_Expired() {
        // Arrange
        UserToken expiredToken = UserToken.fromAuthorizationContext(testContext, testUsername, 
                "John", "Doe", "<EMAIL>", "jwt-token", 
                (System.currentTimeMillis() / 1000) - 3600, "server"); // Expired 1 hour ago
        
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            when(valueOperations.get("auth_context:" + testTenant + ":" + testUsername)).thenReturn(expiredToken);
            when(redisTokenTemplate.delete("auth_context:" + testTenant + ":" + testUsername)).thenReturn(true);

            // Act
            AuthorizationContext result = authorizationService.getCachedAuthorizationContext(testUsername);

            // Assert
            assertNull(result);
            verify(redisTokenTemplate).delete("auth_context:" + testTenant + ":" + testUsername);
        }
    }

    @Test
    void testGetCachedAuthorizationContext_RedisFailure() {
        // Arrange
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            when(valueOperations.get(any())).thenThrow(new RedisConnectionFailureException("Redis is down"));

            // Act
            AuthorizationContext result = authorizationService.getCachedAuthorizationContext(testUsername);

            // Assert
            assertNull(result);
        }
    }

    @Test
    void testGetCachedAuthorizationContext_InvalidUsername() {
        // Act & Assert
        assertNull(authorizationService.getCachedAuthorizationContext(null));
        assertNull(authorizationService.getCachedAuthorizationContext(""));
        assertNull(authorizationService.getCachedAuthorizationContext("   "));

        // Verify Redis was not called
        verify(valueOperations, never()).get(any());
    }

    @Test
    void testCacheKeyGeneration_WithTenant() {
        // This test verifies the cache key format through the actual cache operations
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            when(valueOperations.get("auth_context:" + testTenant + ":" + testUsername)).thenReturn(null);

            // Act
            authorizationService.getCachedAuthorizationContext(testUsername);

            // Assert
            verify(valueOperations).get("auth_context:" + testTenant + ":" + testUsername);
        }
    }

    @Test
    void testCacheKeyGeneration_WithoutTenant() {
        // This test verifies the cache key format when tenant is not available
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(null);
            when(valueOperations.get("auth_context:default:" + testUsername)).thenReturn(null);

            // Act
            authorizationService.getCachedAuthorizationContext(testUsername);

            // Assert
            verify(valueOperations).get("auth_context:default:" + testUsername);
        }
    }

    @Test
    void testRefreshUserAuthorizationContext_CacheInvalidation() throws Exception {
        // Arrange
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            when(redisTokenTemplate.delete("auth_context:" + testTenant + ":" + testUsername)).thenReturn(true);
            
            when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
            when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
            when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
            when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

            // Act
            AuthorizationContext result = authorizationService.refreshUserAuthorizationContext(testUsername);

            // Assert
            assertNotNull(result);
            assertEquals(2, result.getRoleNames().size());

            // Verify cache was invalidated and new data was cached
            verify(redisTokenTemplate).delete("auth_context:" + testTenant + ":" + testUsername);
            verify(valueOperations).set(eq("auth_context:" + testTenant + ":" + testUsername), 
                                       any(UserToken.class), eq(58L), eq(TimeUnit.MINUTES));
        }
    }

    @Test
    void testCacheExpiration() throws Exception {
        // Arrange
        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn(testTenant);
            when(valueOperations.get("auth_context:" + testTenant + ":" + testUsername)).thenReturn(null);
            
            when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
            when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
            when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
            when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

            // Act
            authorizationService.getUserAuthorizationContext(testUsername);

            // Assert - Verify cache is set with 58 minute expiration
            verify(valueOperations).set(eq("auth_context:" + testTenant + ":" + testUsername), 
                                       any(UserToken.class), eq(58L), eq(TimeUnit.MINUTES));
        }
    }
}