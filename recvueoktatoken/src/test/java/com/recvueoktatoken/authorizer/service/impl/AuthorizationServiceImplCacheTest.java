package com.recvueoktatoken.authorizer.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import com.recvueoktatoken.authorizer.model.AuthorizationContext;
import com.recvueoktatoken.authorizer.model.BusinessUnitInfo;
import com.recvueoktatoken.authorizer.model.UserToken;
import com.recvueoktatoken.authorizer.monitoring.AuthorizationMetrics;
import com.recvueoktatoken.coreuser.dao.CoreUserDao;

/**
 * Unit tests for Redis caching functionality in AuthorizationServiceImpl
 * JUnit 4 compatible version
 */
@RunWith(MockitoJUnitRunner.class)
public class AuthorizationServiceImplCacheTest {

    @Mock
    private CoreUserDao coreUserDao;

    @Mock
    private RedisTemplate<String, UserToken> redisTokenTemplate;

    @Mock
    private ValueOperations<String, UserToken> valueOperations;

    @Mock
    private AuthorizationMetrics authorizationMetrics;

    @InjectMocks
    private AuthorizationServiceImpl authorizationService;

    private String testUsername = "testuser";
    private String testTenant = "default";
    private List<String> testRoles;
    private List<String> testPermissions;
    private List<BusinessUnitInfo> testBusinessUnits;
    private AuthorizationContext testContext;

    @Before
    public void setUp() {
        testRoles = Arrays.asList("ADMIN", "USER_MANAGER");
        testPermissions = Arrays.asList("USER_CREATE", "USER_READ", "USER_UPDATE", "REPORT_VIEW");
        testBusinessUnits = Arrays.asList(
                new BusinessUnitInfo(1L, "North America"),
                new BusinessUnitInfo(2L, "Europe")
        );
        testContext = new AuthorizationContext(testRoles, testPermissions, testBusinessUnits, false);

        when(redisTokenTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    public void testGetUserAuthorizationContext_CacheHit() throws Exception {
        // Arrange
        UserToken cachedToken = UserToken.fromAuthorizationContext(testContext, testUsername, 
                "John", "Doe", "<EMAIL>", "jwt-token", 
                (System.currentTimeMillis() / 1000) + 3600, "server");
        
        String cacheKey = "auth_context:" + testTenant + ":" + testUsername;
        when(valueOperations.get(cacheKey)).thenReturn(cachedToken);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getRoleNames().size());
        assertEquals(4, result.getPermissionNames().size());
        assertEquals(2, result.getBusinessUnits().size());
        assertFalse(result.isReadOnlyAccess());

        // Verify cache was checked but database was not called
        verify(valueOperations).get(cacheKey);
        verify(coreUserDao, never()).getUserRoleNames(any());
        verify(coreUserDao, never()).getUserPermissionNames(any());
        verify(coreUserDao, never()).getUserBusinessUnits(any());
        verify(coreUserDao, never()).isReadOnlyUser(any());
    }

    @Test
    public void testGetUserAuthorizationContext_CacheMiss() throws Exception {
        // Arrange
        String cacheKey = "auth_context:" + testTenant + ":" + testUsername;
        when(valueOperations.get(cacheKey)).thenReturn(null);
        
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getRoleNames().size());
        assertEquals(4, result.getPermissionNames().size());
        assertEquals(2, result.getBusinessUnits().size());
        assertFalse(result.isReadOnlyAccess());

        // Verify cache was checked, database was called, and result was cached
        verify(valueOperations).get(cacheKey);
        verify(coreUserDao).getUserRoleNames(testUsername);
        verify(coreUserDao).getUserPermissionNames(testUsername);
        verify(coreUserDao).getUserBusinessUnits(testUsername);
        verify(coreUserDao).isReadOnlyUser(testUsername);
        verify(valueOperations).set(eq(cacheKey), any(UserToken.class), eq(58L), eq(TimeUnit.MINUTES));
    }

    @Test
    public void testGetUserAuthorizationContext_ExpiredCache() throws Exception {
        // Arrange
        UserToken expiredToken = UserToken.fromAuthorizationContext(testContext, testUsername, 
                "John", "Doe", "<EMAIL>", "jwt-token", 
                (System.currentTimeMillis() / 1000) - 3600, "server"); // Expired 1 hour ago
        
        String cacheKey = "auth_context:" + testTenant + ":" + testUsername;
        when(valueOperations.get(cacheKey)).thenReturn(expiredToken);
        when(redisTokenTemplate.delete(cacheKey)).thenReturn(true);
        
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getRoleNames().size());

        // Verify expired cache was deleted and database was called
        verify(redisTokenTemplate).delete(cacheKey);
        verify(coreUserDao).getUserRoleNames(testUsername);
    }

    @Test
    public void testGetUserAuthorizationContext_RedisConnectionFailure() throws Exception {
        // Arrange
        String cacheKey = "auth_context:" + testTenant + ":" + testUsername;
        when(valueOperations.get(any())).thenThrow(new RedisConnectionFailureException("Redis is down"));
        
        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getRoleNames().size());

        // Verify database was still called despite Redis failure
        verify(coreUserDao).getUserRoleNames(testUsername);
    }

    @Test
    public void testInvalidateUserAuthorizationCache_Success() {
        // Arrange
        String cacheKey = "auth_context:" + testTenant + ":" + testUsername;
        when(redisTokenTemplate.delete(cacheKey)).thenReturn(true);

        // Act
        authorizationService.invalidateUserAuthorizationCache(testUsername);

        // Assert
        verify(redisTokenTemplate).delete(cacheKey);
    }

    @Test
    public void testInvalidateUserAuthorizationCache_RedisFailure() {
        // Arrange
        String cacheKey = "auth_context:" + testTenant + ":" + testUsername;
        when(redisTokenTemplate.delete(anyString())).thenThrow(new RedisConnectionFailureException("Redis is down"));

        // Act - Should not throw exception
        try {
            authorizationService.invalidateUserAuthorizationCache(testUsername);
            // Test passes if no exception is thrown
        } catch (Exception e) {
            fail("Should not throw exception when Redis fails");
        }
    }

    @Test
    public void testInvalidateUserAuthorizationCache_InvalidUsername() {
        // Act - Should not throw exception
        try {
            authorizationService.invalidateUserAuthorizationCache(null);
            authorizationService.invalidateUserAuthorizationCache("");
            authorizationService.invalidateUserAuthorizationCache("   ");
            // Test passes if no exception is thrown
        } catch (Exception e) {
            fail("Should not throw exception for invalid usernames");
        }

        // Assert - Redis should not be called
        verify(redisTokenTemplate, never()).delete(anyString());
    }

    @Test
    public void testGetCachedAuthorizationContext_Success() {
        // Arrange
        UserToken cachedToken = UserToken.fromAuthorizationContext(testContext, testUsername, 
                "John", "Doe", "<EMAIL>", "jwt-token", 
                (System.currentTimeMillis() / 1000) + 3600, "server");
        
        String cacheKey = "auth_context:" + testTenant + ":" + testUsername;
        when(valueOperations.get(cacheKey)).thenReturn(cachedToken);

        // Act
        AuthorizationContext result = authorizationService.getCachedAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getRoleNames().size());
        assertEquals(4, result.getPermissionNames().size());
        assertEquals(2, result.getBusinessUnits().size());
        assertFalse(result.isReadOnlyAccess());
    }

    @Test
    public void testGetCachedAuthorizationContext_NotFound() {
        // Arrange
        String cacheKey = "auth_context:" + testTenant + ":" + testUsername;
        when(valueOperations.get(cacheKey)).thenReturn(null);

        // Act
        AuthorizationContext result = authorizationService.getCachedAuthorizationContext(testUsername);

        // Assert
        assertNull(result);
    }

    @Test
    public void testGetCachedAuthorizationContext_InvalidUsername() {
        // Act & Assert
        assertNull(authorizationService.getCachedAuthorizationContext(null));
        assertNull(authorizationService.getCachedAuthorizationContext(""));
        assertNull(authorizationService.getCachedAuthorizationContext("   "));

        // Verify Redis was not called
        verify(valueOperations, never()).get(any());
    }

    @Test
    public void testRefreshUserAuthorizationContext_CacheInvalidation() throws Exception {
        // Arrange
        String cacheKey = "auth_context:" + testTenant + ":" + testUsername;
        when(redisTokenTemplate.delete(cacheKey)).thenReturn(true);

        when(coreUserDao.getUserRoleNames(testUsername)).thenReturn(testRoles);
        when(coreUserDao.getUserPermissionNames(testUsername)).thenReturn(testPermissions);
        when(coreUserDao.getUserBusinessUnits(testUsername)).thenReturn(testBusinessUnits);
        when(coreUserDao.isReadOnlyUser(testUsername)).thenReturn(false);

        // Act
        AuthorizationContext result = authorizationService.refreshUserAuthorizationContext(testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getRoleNames().size());

        // Verify cache was invalidated and database was called
        // Note: refreshUserAuthorizationContext calls retrieveAuthorizationContextFromDatabase directly,
        // so it doesn't cache the result - this is by design for refresh operations
        verify(redisTokenTemplate).delete(cacheKey);
        verify(coreUserDao).getUserRoleNames(testUsername);
        verify(coreUserDao).getUserPermissionNames(testUsername);
        verify(coreUserDao).getUserBusinessUnits(testUsername);
        verify(coreUserDao).isReadOnlyUser(testUsername);
    }
}
