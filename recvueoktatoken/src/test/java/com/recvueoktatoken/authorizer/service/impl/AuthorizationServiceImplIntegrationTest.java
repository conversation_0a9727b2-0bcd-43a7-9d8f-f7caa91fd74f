package com.recvueoktatoken.authorizer.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.recvueoktatoken.authorizer.model.AuthorizationContext;
import com.recvueoktatoken.authorizer.service.AuthorizationService;

/**
 * Integration tests for AuthorizationServiceImpl.
 * These tests require a database connection and test data.
 * 
 * To run these tests, set system property: -Drun.integration.tests=true
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@EnabledIfSystemProperty(named = "run.integration.tests", matches = "true")
class AuthorizationServiceImplIntegrationTest {

    @Autowired
    private AuthorizationService authorizationService;

    private static final String TEST_ADMIN_USER = "admin.test";
    private static final String TEST_READONLY_USER = "readonly.test";
    private static final String TEST_MULTI_ROLE_USER = "multirole.test";
    private static final String NON_EXISTENT_USER = "nonexistent.user";

    @Test
    void testGetUserAuthorizationContext_AdminUser() throws Exception {
        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(TEST_ADMIN_USER);

        // Assert
        assertNotNull(result);
        
        if (!result.isEmpty()) { // Only test if test data exists
            assertTrue(result.getRoleNames().size() > 0);
            assertTrue(result.getPermissionNames().size() >= result.getRoleNames().size());
            
            // Log for debugging
            System.out.println("Admin user authorization context:");
            System.out.println("  Roles: " + result.getRoleNames());
            System.out.println("  Permissions: " + result.getPermissionNames());
            System.out.println("  Business Units: " + result.getBusinessUnits());
            System.out.println("  Read-only: " + result.isReadOnlyAccess());
            
            // Verify no duplicates
            assertEquals(result.getRoleNames().size(), result.getRoleNames().stream().distinct().count());
            assertEquals(result.getPermissionNames().size(), result.getPermissionNames().stream().distinct().count());
            
            // Verify business units have valid data
            result.getBusinessUnits().forEach(bu -> {
                assertNotNull(bu.getBusinessUnitId());
                assertNotNull(bu.getBusinessUnitName());
                assertTrue(bu.getBusinessUnitId() > 0);
                assertFalse(bu.getBusinessUnitName().trim().isEmpty());
            });
        }
    }

    @Test
    void testGetUserAuthorizationContext_NonExistentUser() throws Exception {
        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(NON_EXISTENT_USER);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        assertTrue(result.getRoleNames().isEmpty());
        assertTrue(result.getPermissionNames().isEmpty());
        assertTrue(result.getBusinessUnits().isEmpty());
        assertFalse(result.isReadOnlyAccess()); // Default should be false
    }

    @Test
    void testGetUserAuthorizationContext_ReadOnlyUser() throws Exception {
        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(TEST_READONLY_USER);

        // Assert
        assertNotNull(result);
        
        // Log for debugging
        System.out.println("Read-only user authorization context:");
        System.out.println("  Roles: " + result.getRoleNames());
        System.out.println("  Permissions: " + result.getPermissionNames());
        System.out.println("  Business Units: " + result.getBusinessUnits());
        System.out.println("  Read-only: " + result.isReadOnlyAccess());
        
        // The read-only flag test depends on test data setup
        // For now, just verify the method works without throwing exceptions
    }

    @Test
    void testIsUserAuthorizedForResource_Success() throws Exception {
        // First get the user's permissions to test with actual data
        AuthorizationContext context = authorizationService.getUserAuthorizationContext(TEST_ADMIN_USER);
        
        if (!context.getPermissionNames().isEmpty()) {
            // Test with the first permission found
            String firstPermission = context.getPermissionNames().get(0);
            
            // Parse permission to extract resource and action
            if (firstPermission.contains("_")) {
                String[] parts = firstPermission.split("_", 2);
                String resource = parts[0];
                String action = parts[1];
                
                // Act
                boolean result = authorizationService.isUserAuthorizedForResource(TEST_ADMIN_USER, resource, action);
                
                // Assert
                assertTrue(result);
                
                System.out.println("Authorization test - User: " + TEST_ADMIN_USER + 
                                 ", Resource: " + resource + ", Action: " + action + " - GRANTED");
            }
        }
    }

    @Test
    void testIsUserAuthorizedForResource_Denied() throws Exception {
        // Act - Test with a permission that likely doesn't exist
        boolean result = authorizationService.isUserAuthorizedForResource(TEST_ADMIN_USER, "NONEXISTENT", "PERMISSION");

        // Assert
        assertFalse(result);
        
        System.out.println("Authorization test - User: " + TEST_ADMIN_USER + 
                         ", Resource: NONEXISTENT, Action: PERMISSION - DENIED");
    }

    @Test
    void testIsUserAuthorizedForResource_NonExistentUser() {
        // Act
        boolean result = authorizationService.isUserAuthorizedForResource(NON_EXISTENT_USER, "USER", "CREATE");

        // Assert
        assertFalse(result);
    }

    @Test
    void testRefreshUserAuthorizationContext() throws Exception {
        // Act
        AuthorizationContext result1 = authorizationService.getUserAuthorizationContext(TEST_ADMIN_USER);
        AuthorizationContext result2 = authorizationService.refreshUserAuthorizationContext(TEST_ADMIN_USER);

        // Assert
        assertNotNull(result1);
        assertNotNull(result2);
        
        // Results should be consistent
        assertEquals(result1.getRoleNames().size(), result2.getRoleNames().size());
        assertEquals(result1.getPermissionNames().size(), result2.getPermissionNames().size());
        assertEquals(result1.getBusinessUnits().size(), result2.getBusinessUnits().size());
        assertEquals(result1.isReadOnlyAccess(), result2.isReadOnlyAccess());
        
        // Verify content is the same
        assertTrue(result1.getRoleNames().containsAll(result2.getRoleNames()));
        assertTrue(result2.getRoleNames().containsAll(result1.getRoleNames()));
        
        System.out.println("Refresh test - Original context timestamp: " + result1.getCacheTimestamp());
        System.out.println("Refresh test - Refreshed context timestamp: " + result2.getCacheTimestamp());
    }

    @Test
    void testMultiRoleUserAggregation() throws Exception {
        // Act
        AuthorizationContext result = authorizationService.getUserAuthorizationContext(TEST_MULTI_ROLE_USER);

        // Assert
        assertNotNull(result);
        
        if (!result.isEmpty()) { // Only test if test data exists
            System.out.println("Multi-role user authorization context:");
            System.out.println("  Roles: " + result.getRoleNames());
            System.out.println("  Permissions: " + result.getPermissionNames());
            System.out.println("  Business Units: " + result.getBusinessUnits());
            
            // Verify aggregation logic
            // Permissions should typically be >= roles since roles contain multiple permissions
            assertTrue(result.getPermissionNames().size() >= result.getRoleNames().size() || 
                      result.getPermissionNames().isEmpty());
            
            // Verify no duplicates
            assertEquals(result.getRoleNames().size(), result.getRoleNames().stream().distinct().count());
            assertEquals(result.getPermissionNames().size(), result.getPermissionNames().stream().distinct().count());
            
            // Test authorization with aggregated permissions
            if (!result.getPermissionNames().isEmpty()) {
                String testPermission = result.getPermissionNames().get(0);
                if (testPermission.contains("_")) {
                    String[] parts = testPermission.split("_", 2);
                    boolean authorized = authorizationService.isUserAuthorizedForResource(
                        TEST_MULTI_ROLE_USER, parts[0], parts[1]);
                    assertTrue(authorized);
                }
            }
        }
    }

    @Test
    void testPerformanceWithMultipleCalls() throws Exception {
        long startTime = System.currentTimeMillis();
        
        // Act - Make multiple calls to test performance
        for (int i = 0; i < 10; i++) {
            AuthorizationContext context = authorizationService.getUserAuthorizationContext(TEST_ADMIN_USER);
            assertNotNull(context);
            
            // Test authorization checks
            authorizationService.isUserAuthorizedForResource(TEST_ADMIN_USER, "USER", "CREATE");
            authorizationService.isUserAuthorizedForResource(TEST_ADMIN_USER, "REPORT", "VIEW");
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // Assert
        assertTrue(duration < 10000, "Multiple authorization calls took too long: " + duration + "ms");
        
        System.out.println("Performance test - 10 authorization context retrievals + 20 authorization checks completed in: " + duration + "ms");
        System.out.println("Average per operation: " + (duration / 30.0) + "ms");
    }

    @Test
    void testCacheInvalidation() throws Exception {
        // Act
        AuthorizationContext before = authorizationService.getUserAuthorizationContext(TEST_ADMIN_USER);
        
        // Invalidate cache (should not throw exception even though cache is not implemented yet)
        assertDoesNotThrow(() -> {
            authorizationService.invalidateUserAuthorizationCache(TEST_ADMIN_USER);
        });
        
        AuthorizationContext after = authorizationService.getUserAuthorizationContext(TEST_ADMIN_USER);

        // Assert
        assertNotNull(before);
        assertNotNull(after);
        
        // Since cache is not implemented yet, results should be consistent
        assertEquals(before.getRoleNames().size(), after.getRoleNames().size());
        assertEquals(before.getPermissionNames().size(), after.getPermissionNames().size());
        assertEquals(before.getBusinessUnits().size(), after.getBusinessUnits().size());
        assertEquals(before.isReadOnlyAccess(), after.isReadOnlyAccess());
    }

    @Test
    void testGetCachedAuthorizationContext() {
        // Act
        AuthorizationContext cached = authorizationService.getCachedAuthorizationContext(TEST_ADMIN_USER);

        // Assert
        assertNull(cached); // Should be null since cache is not implemented yet
    }

    @Test
    void testErrorHandling() {
        // Test with invalid parameters
        assertThrows(IllegalArgumentException.class, () -> {
            authorizationService.getUserAuthorizationContext(null);
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            authorizationService.getUserAuthorizationContext("");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            authorizationService.refreshUserAuthorizationContext(null);
        });
        
        // Test authorization with invalid parameters
        assertFalse(authorizationService.isUserAuthorizedForResource(null, "USER", "CREATE"));
        assertFalse(authorizationService.isUserAuthorizedForResource(TEST_ADMIN_USER, null, "CREATE"));
        assertFalse(authorizationService.isUserAuthorizedForResource(TEST_ADMIN_USER, "USER", null));
    }

    @Test
    void testBusinessUnitAccessValidation() throws Exception {
        // Act
        AuthorizationContext context = authorizationService.getUserAuthorizationContext(TEST_ADMIN_USER);

        // Assert
        assertNotNull(context);
        
        if (!context.getBusinessUnits().isEmpty()) {
            // Test business unit access methods
            Long firstBUId = context.getBusinessUnits().get(0).getBusinessUnitId();
            assertTrue(context.hasBusinessUnitAccess(firstBUId));
            assertFalse(context.hasBusinessUnitAccess(999999L)); // Non-existent BU
            
            System.out.println("Business unit access test - User has access to BU: " + firstBUId);
        }
    }

    @Test
    void testAuthorizationContextTimestamp() throws Exception {
        // Act
        long beforeTime = System.currentTimeMillis();
        AuthorizationContext context = authorizationService.getUserAuthorizationContext(TEST_ADMIN_USER);
        long afterTime = System.currentTimeMillis();

        // Assert
        assertNotNull(context);
        assertTrue(context.getCacheTimestamp() >= beforeTime);
        assertTrue(context.getCacheTimestamp() <= afterTime);
        
        System.out.println("Context timestamp: " + context.getCacheTimestamp());
        System.out.println("Test time range: " + beforeTime + " - " + afterTime);
    }

    @Test
    void testConcurrentAccess() throws Exception {
        // Test that multiple threads can access the service simultaneously
        final String[] results = new String[2];
        final Exception[] exceptions = new Exception[2];
        
        Thread thread1 = new Thread(() -> {
            try {
                AuthorizationContext context = authorizationService.getUserAuthorizationContext(TEST_ADMIN_USER);
                results[0] = "Thread1: " + context.getRoleNames().size() + " roles";
            } catch (Exception e) {
                exceptions[0] = e;
            }
        });
        
        Thread thread2 = new Thread(() -> {
            try {
                boolean authorized = authorizationService.isUserAuthorizedForResource(TEST_ADMIN_USER, "USER", "CREATE");
                results[1] = "Thread2: " + (authorized ? "AUTHORIZED" : "DENIED");
            } catch (Exception e) {
                exceptions[1] = e;
            }
        });
        
        // Act
        thread1.start();
        thread2.start();
        
        thread1.join(5000); // Wait max 5 seconds
        thread2.join(5000);
        
        // Assert
        assertNull(exceptions[0], "Thread 1 should not throw exception");
        assertNull(exceptions[1], "Thread 2 should not throw exception");
        assertNotNull(results[0], "Thread 1 should produce result");
        assertNotNull(results[1], "Thread 2 should produce result");
        
        System.out.println("Concurrent access test results:");
        System.out.println("  " + results[0]);
        System.out.println("  " + results[1]);
    }
}