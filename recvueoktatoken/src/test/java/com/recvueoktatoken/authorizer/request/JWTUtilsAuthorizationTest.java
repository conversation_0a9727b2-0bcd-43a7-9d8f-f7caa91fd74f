package com.recvueoktatoken.authorizer.request;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import com.recvueoktatoken.authorizer.model.BusinessUnitInfo;

/**
 * Unit tests for authorization claims handling in JWTUtils
 */
@ExtendWith(MockitoExtension.class)
class JWTUtilsAuthorizationTest {

    private JWTUtils jwtUtils;
    private Map<String, Object> testClaims;

    @BeforeEach
    void setUp() {
        jwtUtils = new JWTUtils();
        testClaims = new HashMap<>();
        
        // Add standard claims
        ClaimsObject userClaims = new ClaimsObject();
        userClaims.setUsername("testuser");
        userClaims.setEmail("<EMAIL>");
        
        testClaims.put(Constants.userMap, userClaims);
        testClaims.put(Constants.tenantIdentiferMap, "test-tenant");
        
        // Add authorization claims
        testClaims.put(Constants.rolesMap, Arrays.asList("ADMIN", "USER_MANAGER"));
        testClaims.put(Constants.permissionsMap, Arrays.asList("USER_CREATE", "USER_READ", "USER_UPDATE", "REPORT_VIEW"));
        testClaims.put(Constants.businessUnitsMap, Arrays.asList(
                new BusinessUnitInfo(1L, "North America"),
                new BusinessUnitInfo(2L, "Europe")
        ));
        testClaims.put(Constants.readOnlyAccessMap, false);
    }

    @Test
    void testLogAuthorizationClaims_Success() throws Exception {
        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("logAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, testClaims);
        });
    }

    @Test
    void testLogAuthorizationClaims_WithNullClaims() throws Exception {
        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("logAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, (Map<String, Object>) null);
        });
    }

    @Test
    void testLogAuthorizationClaims_WithEmptyClaims() throws Exception {
        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("logAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, new HashMap<String, Object>());
        });
    }

    @Test
    void testValidateAuthorizationClaims_Success() throws Exception {
        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("validateAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, testClaims);
        });
    }

    @Test
    void testValidateAuthorizationClaims_WithInvalidRolesType() throws Exception {
        // Arrange
        testClaims.put(Constants.rolesMap, "INVALID_TYPE"); // Should be List

        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("validateAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception but should log warning
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, testClaims);
        });
    }

    @Test
    void testValidateAuthorizationClaims_WithInvalidPermissionsType() throws Exception {
        // Arrange
        testClaims.put(Constants.permissionsMap, 12345); // Should be List

        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("validateAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception but should log warning
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, testClaims);
        });
    }

    @Test
    void testValidateAuthorizationClaims_WithInvalidBusinessUnitsType() throws Exception {
        // Arrange
        testClaims.put(Constants.businessUnitsMap, "INVALID_TYPE"); // Should be List

        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("validateAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception but should log warning
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, testClaims);
        });
    }

    @Test
    void testValidateAuthorizationClaims_WithInvalidReadOnlyAccessType() throws Exception {
        // Arrange
        testClaims.put(Constants.readOnlyAccessMap, "INVALID_TYPE"); // Should be Boolean

        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("validateAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception but should log warning
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, testClaims);
        });
    }

    @Test
    void testValidateAuthorizationClaims_WithLargeRolesList() throws Exception {
        // Arrange - Create large roles list
        List<String> largeRolesList = new java.util.ArrayList<>();
        for (int i = 0; i < 60; i++) {
            largeRolesList.add("ROLE_" + i);
        }
        testClaims.put(Constants.rolesMap, largeRolesList);

        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("validateAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception but should log warning
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, testClaims);
        });
    }

    @Test
    void testValidateAuthorizationClaims_WithLargePermissionsList() throws Exception {
        // Arrange - Create large permissions list
        List<String> largePermissionsList = new java.util.ArrayList<>();
        for (int i = 0; i < 120; i++) {
            largePermissionsList.add("PERMISSION_" + i);
        }
        testClaims.put(Constants.permissionsMap, largePermissionsList);

        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("validateAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception but should log warning
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, testClaims);
        });
    }

    @Test
    void testValidateAuthorizationClaims_WithLargeBusinessUnitsList() throws Exception {
        // Arrange - Create large business units list
        List<BusinessUnitInfo> largeBusinessUnitsList = new java.util.ArrayList<>();
        for (int i = 0; i < 25; i++) {
            largeBusinessUnitsList.add(new BusinessUnitInfo((long) i, "Business Unit " + i));
        }
        testClaims.put(Constants.businessUnitsMap, largeBusinessUnitsList);

        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("validateAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception but should log warning
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, testClaims);
        });
    }

    @Test
    void testValidateAuthorizationClaims_WithLargeClaimsMap() throws Exception {
        // Arrange - Create large claims map
        Map<String, Object> largeClaims = new HashMap<>(testClaims);
        for (int i = 0; i < 25; i++) {
            largeClaims.put("extra_claim_" + i, "value_" + i);
        }

        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("validateAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception but should log warning
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, testClaims);
        });
    }

    @Test
    void testValidateAuthorizationClaims_WithNullClaims() throws Exception {
        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("validateAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, (Map<String, Object>) null);
        });
    }

    @Test
    void testValidateAuthorizationClaims_WithNullValues() throws Exception {
        // Arrange
        testClaims.put(Constants.rolesMap, null);
        testClaims.put(Constants.permissionsMap, null);
        testClaims.put(Constants.businessUnitsMap, null);
        testClaims.put(Constants.readOnlyAccessMap, null);

        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("validateAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, testClaims);
        });
    }

    @Test
    void testGenerateJwt_WithAuthorizationClaims() throws Exception {
        // This test requires the private key file to exist, so we'll test the method call structure
        
        // Act & Assert - Should not throw exception during validation and logging
        try {
            String jwt = jwtUtils.generateJwt(testClaims);
            // If we get here, the JWT was generated successfully
            assertNotNull(jwt);
            assertFalse(jwt.isEmpty());
            
            // JWT should have 3 parts separated by dots
            String[] parts = jwt.split("\\.");
            assertEquals(3, parts.length, "JWT should have header, payload, and signature");
            
        } catch (Exception e) {
            // Expected if private key file doesn't exist, but should not be validation errors
            assertFalse(e.getMessage().contains("validation"), 
                       "Error should not be related to authorization claims validation");
        }
    }

    @Test
    void testGenerateJwt_WithEmptyAuthorizationClaims() throws Exception {
        // Arrange
        Map<String, Object> emptyClaims = new HashMap<>();
        emptyClaims.put(Constants.userMap, new ClaimsObject());
        emptyClaims.put(Constants.rolesMap, Arrays.asList());
        emptyClaims.put(Constants.permissionsMap, Arrays.asList());
        emptyClaims.put(Constants.businessUnitsMap, Arrays.asList());
        emptyClaims.put(Constants.readOnlyAccessMap, false);

        // Act & Assert - Should not throw exception
        try {
            String jwt = jwtUtils.generateJwt(emptyClaims);
            assertNotNull(jwt);
            
        } catch (Exception e) {
            // Expected if private key file doesn't exist
            assertFalse(e.getMessage().contains("validation"));
        }
    }

    @Test
    void testGenerateJwt_WithoutAuthorizationClaims() throws Exception {
        // Arrange - Only standard claims
        Map<String, Object> standardClaims = new HashMap<>();
        standardClaims.put(Constants.userMap, new ClaimsObject());
        standardClaims.put(Constants.tenantIdentiferMap, "test-tenant");

        // Act & Assert - Should not throw exception
        try {
            String jwt = jwtUtils.generateJwt(standardClaims);
            assertNotNull(jwt);
            
        } catch (Exception e) {
            // Expected if private key file doesn't exist
            assertFalse(e.getMessage().contains("validation"));
        }
    }

    @Test
    void testAuthorizationClaimsIntegrity() throws Exception {
        // Test that authorization claims are preserved during JWT generation process
        
        // Arrange
        Map<String, Object> originalClaims = new HashMap<>(testClaims);
        
        // Act - Call validation method
        java.lang.reflect.Method validateMethod = JWTUtils.class.getDeclaredMethod("validateAuthorizationClaims", Map.class);
        validateMethod.setAccessible(true);
        validateMethod.invoke(jwtUtils, testClaims);
        
        // Assert - Claims should not be modified by validation
        assertEquals(originalClaims.size(), testClaims.size());
        assertEquals(originalClaims.get(Constants.rolesMap), testClaims.get(Constants.rolesMap));
        assertEquals(originalClaims.get(Constants.permissionsMap), testClaims.get(Constants.permissionsMap));
        assertEquals(originalClaims.get(Constants.businessUnitsMap), testClaims.get(Constants.businessUnitsMap));
        assertEquals(originalClaims.get(Constants.readOnlyAccessMap), testClaims.get(Constants.readOnlyAccessMap));
    }

    @Test
    void testErrorHandlingInValidation() throws Exception {
        // Test that validation errors don't break JWT generation
        
        // Arrange - Create claims that might cause validation issues
        Map<String, Object> problematicClaims = new HashMap<>();
        problematicClaims.put(Constants.rolesMap, new Object() {
            @Override
            public String toString() {
                throw new RuntimeException("Test error");
            }
        });

        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("validateAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception even with problematic claims
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, problematicClaims);
        });
    }

    @Test
    void testErrorHandlingInLogging() throws Exception {
        // Test that logging errors don't break JWT generation
        
        // Arrange - Create claims that might cause logging issues
        Map<String, Object> problematicClaims = new HashMap<>();
        problematicClaims.put(Constants.permissionsMap, new Object() {
            @Override
            public String toString() {
                throw new RuntimeException("Test error");
            }
        });

        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("logAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception even with problematic claims
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, problematicClaims);
        });
    }

    @Test
    void testClaimsStructureValidation() throws Exception {
        // Test validation of different claim structures
        
        Map<String, Object> testCases = new HashMap<>();
        
        // Valid structures
        testCases.put(Constants.rolesMap, Arrays.asList("ADMIN", "USER"));
        testCases.put(Constants.permissionsMap, Arrays.asList("READ", "WRITE"));
        testCases.put(Constants.businessUnitsMap, Arrays.asList(new BusinessUnitInfo(1L, "Test")));
        testCases.put(Constants.readOnlyAccessMap, true);
        
        // Use reflection to test private method
        java.lang.reflect.Method method = JWTUtils.class.getDeclaredMethod("validateAuthorizationClaims", Map.class);
        method.setAccessible(true);

        // Act - Should not throw exception with valid structures
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, testCases);
        });
        
        // Test invalid structures
        testCases.put(Constants.rolesMap, "INVALID");
        testCases.put(Constants.permissionsMap, 123);
        testCases.put(Constants.businessUnitsMap, new Object());
        testCases.put(Constants.readOnlyAccessMap, "INVALID");
        
        // Should still not throw exception, just log warnings
        assertDoesNotThrow(() -> {
            method.invoke(jwtUtils, testCases);
        });
    }
}