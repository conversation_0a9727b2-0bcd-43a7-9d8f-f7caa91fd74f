package com.recvueoktatoken.authorizer.request;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import com.recvueoktatoken.authorizer.model.AuthorizationContext;
import com.recvueoktatoken.authorizer.model.BusinessUnitInfo;
import com.recvueoktatoken.authorizer.model.UserToken;
import com.recvueoktatoken.authorizer.service.AuthorizationService;

/**
 * Unit tests for authorization integration in IdmTokenValidations
 */
@ExtendWith(MockitoExtension.class)
class IdmTokenValidationsAuthorizationTest {

    @Mock
    private AuthorizationService authorizationService;

    @Mock
    private RedisTemplate<String, UserToken> redisTokenTemplate;

    @Mock
    private ValueOperations<String, UserToken> valueOperations;

    @InjectMocks
    private IdmTokenValidations idmTokenValidations;

    private String testUsername = "testuser";
    private String testAccessToken = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0dXNlciIsImV4cCI6OTk5OTk5OTk5OX0.test";
    private String testServerName = "testserver";
    private Map<String, String> testHeaders;
    private List<String> testRoles;
    private List<String> testPermissions;
    private List<BusinessUnitInfo> testBusinessUnits;
    private AuthorizationContext testAuthContext;

    @BeforeEach
    void setUp() {
        testHeaders = new HashMap<>();
        testHeaders.put(TokenNames.accessToken, testAccessToken);
        testHeaders.put(TokenNames.hostName, testServerName);

        testRoles = Arrays.asList("ADMIN", "USER_MANAGER");
        testPermissions = Arrays.asList("USER_CREATE", "USER_READ", "USER_UPDATE", "REPORT_VIEW");
        testBusinessUnits = Arrays.asList(
                new BusinessUnitInfo(1L, "North America"),
                new BusinessUnitInfo(2L, "Europe")
        );
        testAuthContext = new AuthorizationContext(testRoles, testPermissions, testBusinessUnits, false);

        when(redisTokenTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void testGetAuthorizationContextForUser_Success() throws Exception {
        // Arrange
        when(authorizationService.getUserAuthorizationContext(testUsername)).thenReturn(testAuthContext);

        // Use reflection to test private method
        java.lang.reflect.Method method = IdmTokenValidations.class.getDeclaredMethod("getAuthorizationContextForUser", String.class);
        method.setAccessible(true);

        // Act
        AuthorizationContext result = (AuthorizationContext) method.invoke(idmTokenValidations, testUsername);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getRoleNames().size());
        assertEquals(4, result.getPermissionNames().size());
        assertEquals(2, result.getBusinessUnits().size());
        assertFalse(result.isReadOnlyAccess());

        verify(authorizationService).getUserAuthorizationContext(testUsername);
    }

    @Test
    void testGetAuthorizationContextForUser_ServiceError() throws Exception {
        // Arrange
        when(authorizationService.getUserAuthorizationContext(testUsername))
                .thenThrow(new RuntimeException("Service error"));

        // Use reflection to test private method
        java.lang.reflect.Method method = IdmTokenValidations.class.getDeclaredMethod("getAuthorizationContextForUser", String.class);
        method.setAccessible(true);

        // Act
        AuthorizationContext result = (AuthorizationContext) method.invoke(idmTokenValidations, testUsername);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty()); // Should return empty context on error
        assertTrue(result.getRoleNames().isEmpty());
        assertTrue(result.getPermissionNames().isEmpty());
        assertTrue(result.getBusinessUnits().isEmpty());
        assertFalse(result.isReadOnlyAccess());
    }

    @Test
    void testGetAuthorizationContextForUser_NullResult() throws Exception {
        // Arrange
        when(authorizationService.getUserAuthorizationContext(testUsername)).thenReturn(null);

        // Use reflection to test private method
        java.lang.reflect.Method method = IdmTokenValidations.class.getDeclaredMethod("getAuthorizationContextForUser", String.class);
        method.setAccessible(true);

        // Act
        AuthorizationContext result = (AuthorizationContext) method.invoke(idmTokenValidations, testUsername);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty()); // Should return empty context when service returns null
    }

    @Test
    void testAddAuthorizationDataToClaims_WithAuthorizationContext() throws Exception {
        // Arrange
        Map<String, Object> claims = new HashMap<>();

        // Use reflection to test private method
        java.lang.reflect.Method method = IdmTokenValidations.class.getDeclaredMethod("addAuthorizationDataToClaims", Map.class, AuthorizationContext.class);
        method.setAccessible(true);

        // Act
        method.invoke(idmTokenValidations, claims, testAuthContext);

        // Assert
        assertTrue(claims.containsKey("roles"));
        assertTrue(claims.containsKey("permissions"));
        assertTrue(claims.containsKey("businessUnits"));
        assertTrue(claims.containsKey("readOnlyAccess"));

        @SuppressWarnings("unchecked")
        List<String> roles = (List<String>) claims.get("roles");
        assertEquals(2, roles.size());
        assertTrue(roles.contains("ADMIN"));
        assertTrue(roles.contains("USER_MANAGER"));

        @SuppressWarnings("unchecked")
        List<String> permissions = (List<String>) claims.get("permissions");
        assertEquals(4, permissions.size());
        assertTrue(permissions.contains("USER_CREATE"));

        @SuppressWarnings("unchecked")
        List<BusinessUnitInfo> businessUnits = (List<BusinessUnitInfo>) claims.get("businessUnits");
        assertEquals(2, businessUnits.size());
        assertEquals("North America", businessUnits.get(0).getBusinessUnitName());

        assertFalse((Boolean) claims.get("readOnlyAccess"));
    }

    @Test
    void testAddAuthorizationDataToClaims_WithEmptyAuthorizationContext() throws Exception {
        // Arrange
        Map<String, Object> claims = new HashMap<>();
        AuthorizationContext emptyContext = new AuthorizationContext();

        // Use reflection to test private method
        java.lang.reflect.Method method = IdmTokenValidations.class.getDeclaredMethod("addAuthorizationDataToClaims", Map.class, AuthorizationContext.class);
        method.setAccessible(true);

        // Act
        method.invoke(idmTokenValidations, claims, emptyContext);

        // Assert
        assertTrue(claims.containsKey("roles"));
        assertTrue(claims.containsKey("permissions"));
        assertTrue(claims.containsKey("businessUnits"));
        assertTrue(claims.containsKey("readOnlyAccess"));

        @SuppressWarnings("unchecked")
        List<String> roles = (List<String>) claims.get("roles");
        assertTrue(roles.isEmpty());

        @SuppressWarnings("unchecked")
        List<String> permissions = (List<String>) claims.get("permissions");
        assertTrue(permissions.isEmpty());

        @SuppressWarnings("unchecked")
        List<BusinessUnitInfo> businessUnits = (List<BusinessUnitInfo>) claims.get("businessUnits");
        assertTrue(businessUnits.isEmpty());

        assertFalse((Boolean) claims.get("readOnlyAccess"));
    }

    @Test
    void testAddAuthorizationDataToClaims_WithReadOnlyUser() throws Exception {
        // Arrange
        Map<String, Object> claims = new HashMap<>();
        AuthorizationContext readOnlyContext = new AuthorizationContext(testRoles, testPermissions, testBusinessUnits, true);

        // Use reflection to test private method
        java.lang.reflect.Method method = IdmTokenValidations.class.getDeclaredMethod("addAuthorizationDataToClaims", Map.class, AuthorizationContext.class);
        method.setAccessible(true);

        // Act
        method.invoke(idmTokenValidations, claims, readOnlyContext);

        // Assert
        assertTrue((Boolean) claims.get("readOnlyAccess"));
    }

    @Test
    void testAddAuthorizationDataToClaims_WithNullParameters() throws Exception {
        // Arrange
        Map<String, Object> claims = new HashMap<>();

        // Use reflection to test private method
        java.lang.reflect.Method method = IdmTokenValidations.class.getDeclaredMethod("addAuthorizationDataToClaims", Map.class, AuthorizationContext.class);
        method.setAccessible(true);

        // Act - Should not throw exception
        assertDoesNotThrow(() -> {
            method.invoke(idmTokenValidations, null, testAuthContext);
            method.invoke(idmTokenValidations, claims, null);
            method.invoke(idmTokenValidations, null, null);
        });
    }

    @Test
    void testAddAuthorizationDataToClaims_WithUserToken() throws Exception {
        // Arrange
        Map<String, Object> claims = new HashMap<>();
        UserToken userToken = UserToken.fromAuthorizationContext(testAuthContext, testUsername, 
                "John", "Doe", "<EMAIL>", "jwt-token", 1234567890L, testServerName);

        // Use reflection to test private method
        java.lang.reflect.Method method = IdmTokenValidations.class.getDeclaredMethod("addAuthorizationDataToClaims", Map.class, UserToken.class);
        method.setAccessible(true);

        // Act
        method.invoke(idmTokenValidations, claims, userToken);

        // Assert
        assertTrue(claims.containsKey("roles"));
        assertTrue(claims.containsKey("permissions"));
        assertTrue(claims.containsKey("businessUnits"));
        assertTrue(claims.containsKey("readOnlyAccess"));

        @SuppressWarnings("unchecked")
        List<String> roles = (List<String>) claims.get("roles");
        assertEquals(2, roles.size());
        assertTrue(roles.contains("ADMIN"));
    }

    @Test
    void testAddAuthorizationDataToClaims_WithNullUserToken() throws Exception {
        // Arrange
        Map<String, Object> claims = new HashMap<>();

        // Use reflection to test private method
        java.lang.reflect.Method method = IdmTokenValidations.class.getDeclaredMethod("addAuthorizationDataToClaims", Map.class, UserToken.class);
        method.setAccessible(true);

        // Act - Should not throw exception
        assertDoesNotThrow(() -> {
            method.invoke(idmTokenValidations, claims, (UserToken) null);
        });
    }

    @Test
    void testAuthorizationDataConsistency() throws Exception {
        // Test that authorization data is consistent between AuthorizationContext and UserToken methods
        
        // Arrange
        Map<String, Object> claimsFromContext = new HashMap<>();
        Map<String, Object> claimsFromUserToken = new HashMap<>();
        
        UserToken userToken = UserToken.fromAuthorizationContext(testAuthContext, testUsername, 
                "John", "Doe", "<EMAIL>", "jwt-token", 1234567890L, testServerName);

        // Use reflection to test private methods
        java.lang.reflect.Method contextMethod = IdmTokenValidations.class.getDeclaredMethod("addAuthorizationDataToClaims", Map.class, AuthorizationContext.class);
        contextMethod.setAccessible(true);
        
        java.lang.reflect.Method tokenMethod = IdmTokenValidations.class.getDeclaredMethod("addAuthorizationDataToClaims", Map.class, UserToken.class);
        tokenMethod.setAccessible(true);

        // Act
        contextMethod.invoke(idmTokenValidations, claimsFromContext, testAuthContext);
        tokenMethod.invoke(idmTokenValidations, claimsFromUserToken, userToken);

        // Assert - Both methods should produce identical claims
        assertEquals(claimsFromContext.get("roles"), claimsFromUserToken.get("roles"));
        assertEquals(claimsFromContext.get("permissions"), claimsFromUserToken.get("permissions"));
        assertEquals(claimsFromContext.get("readOnlyAccess"), claimsFromUserToken.get("readOnlyAccess"));
        
        @SuppressWarnings("unchecked")
        List<BusinessUnitInfo> contextBUs = (List<BusinessUnitInfo>) claimsFromContext.get("businessUnits");
        @SuppressWarnings("unchecked")
        List<BusinessUnitInfo> tokenBUs = (List<BusinessUnitInfo>) claimsFromUserToken.get("businessUnits");
        
        assertEquals(contextBUs.size(), tokenBUs.size());
        for (int i = 0; i < contextBUs.size(); i++) {
            assertEquals(contextBUs.get(i).getBusinessUnitId(), tokenBUs.get(i).getBusinessUnitId());
            assertEquals(contextBUs.get(i).getBusinessUnitName(), tokenBUs.get(i).getBusinessUnitName());
        }
    }

    @Test
    void testAuthorizationClaimsStructure() throws Exception {
        // Test that authorization claims have the expected structure for JWT generation
        
        // Arrange
        Map<String, Object> claims = new HashMap<>();

        // Use reflection to test private method
        java.lang.reflect.Method method = IdmTokenValidations.class.getDeclaredMethod("addAuthorizationDataToClaims", Map.class, AuthorizationContext.class);
        method.setAccessible(true);

        // Act
        method.invoke(idmTokenValidations, claims, testAuthContext);

        // Assert - Verify claim structure
        assertNotNull(claims.get("roles"));
        assertNotNull(claims.get("permissions"));
        assertNotNull(claims.get("businessUnits"));
        assertNotNull(claims.get("readOnlyAccess"));

        // Verify types
        assertTrue(claims.get("roles") instanceof List);
        assertTrue(claims.get("permissions") instanceof List);
        assertTrue(claims.get("businessUnits") instanceof List);
        assertTrue(claims.get("readOnlyAccess") instanceof Boolean);

        // Verify business units structure
        @SuppressWarnings("unchecked")
        List<BusinessUnitInfo> businessUnits = (List<BusinessUnitInfo>) claims.get("businessUnits");
        for (BusinessUnitInfo bu : businessUnits) {
            assertNotNull(bu.getBusinessUnitId());
            assertNotNull(bu.getBusinessUnitName());
            assertTrue(bu.getBusinessUnitId() > 0);
            assertFalse(bu.getBusinessUnitName().trim().isEmpty());
        }
    }

    @Test
    void testErrorHandlingInAuthorizationDataAddition() throws Exception {
        // Test that errors in authorization data addition don't break the flow
        
        // Arrange
        Map<String, Object> claims = new HashMap<>();
        
        // Create a mock AuthorizationContext that throws exception
        AuthorizationContext mockContext = mock(AuthorizationContext.class);
        when(mockContext.getRoleNames()).thenThrow(new RuntimeException("Test error"));

        // Use reflection to test private method
        java.lang.reflect.Method method = IdmTokenValidations.class.getDeclaredMethod("addAuthorizationDataToClaims", Map.class, AuthorizationContext.class);
        method.setAccessible(true);

        // Act - Should not throw exception
        assertDoesNotThrow(() -> {
            method.invoke(idmTokenValidations, claims, mockContext);
        });

        // Assert - Should have empty default values
        assertTrue(claims.containsKey("roles"));
        assertTrue(claims.containsKey("permissions"));
        assertTrue(claims.containsKey("businessUnits"));
        assertTrue(claims.containsKey("readOnlyAccess"));

        @SuppressWarnings("unchecked")
        List<String> roles = (List<String>) claims.get("roles");
        assertTrue(roles.isEmpty());

        assertFalse((Boolean) claims.get("readOnlyAccess"));
    }

    @Test
    void testBackwardCompatibility() throws Exception {
        // Test that existing JWT claims are not affected by authorization enhancement
        
        // Arrange
        Map<String, Object> claims = new HashMap<>();
        claims.put(Constants.userMap, new ClaimsObject());
        claims.put(Constants.tenantIdentiferMap, "test-tenant");
        claims.put(Constants.jwtTokenMap, "test-jwt");

        // Use reflection to test private method
        java.lang.reflect.Method method = IdmTokenValidations.class.getDeclaredMethod("addAuthorizationDataToClaims", Map.class, AuthorizationContext.class);
        method.setAccessible(true);

        // Act
        method.invoke(idmTokenValidations, claims, testAuthContext);

        // Assert - Existing claims should still be present
        assertTrue(claims.containsKey(Constants.userMap));
        assertTrue(claims.containsKey(Constants.tenantIdentiferMap));
        assertTrue(claims.containsKey(Constants.jwtTokenMap));

        // New authorization claims should be added
        assertTrue(claims.containsKey("roles"));
        assertTrue(claims.containsKey("permissions"));
        assertTrue(claims.containsKey("businessUnits"));
        assertTrue(claims.containsKey("readOnlyAccess"));
    }
}