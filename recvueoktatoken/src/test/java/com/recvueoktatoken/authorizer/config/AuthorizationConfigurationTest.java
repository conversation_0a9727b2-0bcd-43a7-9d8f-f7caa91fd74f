package com.recvueoktatoken.authorizer.config;

import com.recvueoktatoken.authorizer.service.AuthorizationService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for authorization configuration and feature flags
 */
@ExtendWith(SpringJUnitExtension.class)
@SpringBootTest(classes = {AuthorizationConfiguration.class, AuthorizationProperties.class, FeatureFlags.class})
@EnableConfigurationProperties({AuthorizationProperties.class, FeatureFlags.class})
public class AuthorizationConfigurationTest {

    @Autowired(required = false)
    private AuthorizationProperties authorizationProperties;

    @Autowired(required = false)
    private FeatureFlags featureFlags;

    @Test
    void testDefaultConfiguration() {
        // Given: Default configuration
        assertNotNull(authorizationProperties);
        assertNotNull(featureFlags);

        // Then: Verify default values
        assertTrue(authorizationProperties.isEnabled());
        assertTrue(authorizationProperties.isCachingEnabled());
        assertEquals(58L, authorizationProperties.getCache().getExpirationMinutes());
        assertEquals(10000L, authorizationProperties.getCache().getMaxSize());
        assertEquals("auth_context", authorizationProperties.getCache().getKeyPrefix());

        // Verify JWT defaults
        assertTrue(authorizationProperties.getJwt().isIncludeRoles());
        assertTrue(authorizationProperties.getJwt().isIncludePermissions());
        assertTrue(authorizationProperties.getJwt().isIncludeBusinessUnits());
        assertTrue(authorizationProperties.getJwt().isIncludeReadOnlyAccess());
        assertEquals(4096, authorizationProperties.getJwt().getMaxTokenSizeBytes());

        // Verify database defaults
        assertEquals(30, authorizationProperties.getDatabase().getConnectionTimeoutSeconds());
        assertEquals(15, authorizationProperties.getDatabase().getQueryTimeoutSeconds());
        assertEquals(3, authorizationProperties.getDatabase().getMaxRetries());

        // Verify monitoring defaults
        assertTrue(authorizationProperties.getMonitoring().isMetricsEnabled());
        assertFalse(authorizationProperties.getMonitoring().isDetailedLoggingEnabled());
        assertEquals(1000L, authorizationProperties.getMonitoring().getSlowQueryThresholdMs());

        // Verify fallback defaults
        assertTrue(authorizationProperties.getFallback().isEnableCacheFallback());
        assertTrue(authorizationProperties.getFallback().isEnableEmptyDataFallback());
        assertEquals(120L, authorizationProperties.getFallback().getMaxCacheAgeMinutes());
    }

    @Test
    void testFeatureFlagsDefaults() {
        // Given: Default feature flags
        assertNotNull(featureFlags);

        // Then: Verify all features are enabled by default
        assertTrue(featureFlags.isEnabled());
        assertTrue(featureFlags.isRolesEnabled());
        assertTrue(featureFlags.isPermissionsEnabled());
        assertTrue(featureFlags.isBusinessUnitsEnabled());
        assertTrue(featureFlags.isReadOnlyAccessEnabled());
        assertTrue(featureFlags.isCachingEnabled());
        assertTrue(featureFlags.isMetricsEnabled());
        assertFalse(featureFlags.isDetailedLoggingEnabled());
        assertTrue(featureFlags.isBatchProcessingEnabled());
        assertTrue(featureFlags.isCircuitBreakerEnabled());
    }

    @Test
    void testFeatureFlagDependencies() {
        // Given: Feature flags instance
        assertNotNull(featureFlags);

        // When: Master flag is enabled
        assertTrue(featureFlags.isEnabled());

        // Then: Individual features should work based on their own flags
        assertTrue(featureFlags.isAnyJwtClaimEnabled());
        assertTrue(featureFlags.isAnyFallbackEnabled());

        // Verify summary includes enabled features
        String summary = featureFlags.getEnabledFeaturesSummary();
        assertNotNull(summary);
        assertTrue(summary.contains("ENABLED"));
        assertTrue(summary.contains("roles"));
        assertTrue(summary.contains("permissions"));
        assertTrue(summary.contains("businessUnits"));
    }

    @TestPropertySource(properties = {
        "authorization.enabled=false"
    })
    @Test
    void testDisabledConfiguration() {
        // This test would need to be run in a separate context
        // to test the disabled configuration scenario
    }

    @TestPropertySource(properties = {
        "authorization.cache.expiration-minutes=30",
        "authorization.cache.max-size=5000",
        "authorization.jwt.max-token-size-bytes=2048",
        "authorization.database.max-retries=5"
    })
    @Test
    void testCustomConfiguration() {
        // This test would need to be run in a separate context
        // to test custom configuration values
    }

    /**
     * Test configuration for development environment
     */
    @SpringBootTest(classes = {AuthorizationConfiguration.class, AuthorizationProperties.class, FeatureFlags.class})
    @TestPropertySource(locations = "classpath:application-dev.properties")
    static class DevelopmentConfigurationTest {

        @Autowired(required = false)
        private AuthorizationProperties authorizationProperties;

        @Autowired(required = false)
        private FeatureFlags featureFlags;

        @Test
        void testDevelopmentConfiguration() {
            assertNotNull(authorizationProperties);
            assertNotNull(featureFlags);

            // Development should have shorter cache expiration
            assertEquals(10L, authorizationProperties.getCache().getExpirationMinutes());
            assertEquals(1000L, authorizationProperties.getCache().getMaxSize());

            // Development should have shorter timeouts
            assertEquals(10, authorizationProperties.getDatabase().getConnectionTimeoutSeconds());
            assertEquals(5, authorizationProperties.getDatabase().getQueryTimeoutSeconds());

            // Development should have detailed logging enabled
            assertTrue(featureFlags.isDetailedLoggingEnabled());

            // Development should have lower JWT limits
            assertEquals(2048, authorizationProperties.getJwt().getMaxTokenSizeBytes());
            assertEquals(25, authorizationProperties.getJwt().getMaxRoles());
        }
    }

    /**
     * Test configuration for production environment
     */
    @SpringBootTest(classes = {AuthorizationConfiguration.class, AuthorizationProperties.class, FeatureFlags.class})
    @TestPropertySource(locations = "classpath:application-prod.properties")
    static class ProductionConfigurationTest {

        @Autowired(required = false)
        private AuthorizationProperties authorizationProperties;

        @Autowired(required = false)
        private FeatureFlags featureFlags;

        @Test
        void testProductionConfiguration() {
            assertNotNull(authorizationProperties);
            assertNotNull(featureFlags);

            // Production should have longer cache expiration
            assertEquals(58L, authorizationProperties.getCache().getExpirationMinutes());
            assertEquals(50000L, authorizationProperties.getCache().getMaxSize());

            // Production should have longer timeouts
            assertEquals(30, authorizationProperties.getDatabase().getConnectionTimeoutSeconds());
            assertEquals(15, authorizationProperties.getDatabase().getQueryTimeoutSeconds());

            // Production should have detailed logging disabled
            assertFalse(featureFlags.isDetailedLoggingEnabled());

            // Production should have higher JWT limits
            assertEquals(4096, authorizationProperties.getJwt().getMaxTokenSizeBytes());
            assertEquals(50, authorizationProperties.getJwt().getMaxRoles());

            // Production should have higher slow query threshold
            assertEquals(2000L, authorizationProperties.getMonitoring().getSlowQueryThresholdMs());
        }
    }

    /**
     * Test configuration validation
     */
    @Test
    void testConfigurationValidation() {
        assertNotNull(authorizationProperties);

        // Verify cache configuration is valid
        assertTrue(authorizationProperties.getCache().getExpirationMinutes() > 0);
        assertTrue(authorizationProperties.getCache().getMaxSize() > 0);
        assertNotNull(authorizationProperties.getCache().getKeyPrefix());
        assertFalse(authorizationProperties.getCache().getKeyPrefix().isEmpty());

        // Verify database configuration is valid
        assertTrue(authorizationProperties.getDatabase().getConnectionTimeoutSeconds() > 0);
        assertTrue(authorizationProperties.getDatabase().getQueryTimeoutSeconds() > 0);
        assertTrue(authorizationProperties.getDatabase().getMaxRetries() >= 0);
        assertTrue(authorizationProperties.getDatabase().getRetryDelayMs() >= 0);
        assertTrue(authorizationProperties.getDatabase().getBatchSize() > 0);

        // Verify JWT configuration is valid
        assertTrue(authorizationProperties.getJwt().getMaxTokenSizeBytes() > 0);
        assertTrue(authorizationProperties.getJwt().getMaxRoles() > 0);
        assertTrue(authorizationProperties.getJwt().getMaxPermissions() > 0);
        assertTrue(authorizationProperties.getJwt().getMaxBusinessUnits() > 0);

        // Verify monitoring configuration is valid
        assertTrue(authorizationProperties.getMonitoring().getSlowQueryThresholdMs() > 0);
        assertTrue(authorizationProperties.getMonitoring().getHealthCheckIntervalSeconds() > 0);

        // Verify fallback configuration is valid
        assertTrue(authorizationProperties.getFallback().getMaxCacheAgeMinutes() > 0);
        assertTrue(authorizationProperties.getFallback().getCircuitBreakerFailureThreshold() > 0);
        assertTrue(authorizationProperties.getFallback().getCircuitBreakerTimeoutSeconds() > 0);
    }

    /**
     * Test feature flag combinations
     */
    @Test
    void testFeatureFlagCombinations() {
        assertNotNull(featureFlags);

        // Test when master flag is enabled
        assertTrue(featureFlags.isEnabled());
        assertTrue(featureFlags.isRolesEnabled());
        assertTrue(featureFlags.isPermissionsEnabled());

        // Test JWT claim combinations
        assertTrue(featureFlags.isAnyJwtClaimEnabled());

        // Test fallback combinations
        assertTrue(featureFlags.isAnyFallbackEnabled());

        // Test feature summary
        String summary = featureFlags.getEnabledFeaturesSummary();
        assertNotNull(summary);
        assertTrue(summary.contains("ENABLED"));
    }

    /**
     * Test configuration property binding
     */
    @Test
    void testPropertyBinding() {
        assertNotNull(authorizationProperties);

        // Test nested property binding
        assertNotNull(authorizationProperties.getCache());
        assertNotNull(authorizationProperties.getDatabase());
        assertNotNull(authorizationProperties.getJwt());
        assertNotNull(authorizationProperties.getMonitoring());
        assertNotNull(authorizationProperties.getFallback());

        // Test that all nested objects have reasonable defaults
        assertTrue(authorizationProperties.getCache().getExpirationMinutes() > 0);
        assertTrue(authorizationProperties.getDatabase().getConnectionTimeoutSeconds() > 0);
        assertTrue(authorizationProperties.getJwt().getMaxTokenSizeBytes() > 0);
        assertTrue(authorizationProperties.getMonitoring().getSlowQueryThresholdMs() > 0);
        assertTrue(authorizationProperties.getFallback().getMaxCacheAgeMinutes() > 0);
    }
}