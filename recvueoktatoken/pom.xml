<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.recvueoktatoken</groupId>
	<artifactId>recvueoktatoken</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>jar</packaging>

	<name>recvueoktatoken</name>
	<description>recvueoktatoken</description>

	<repositories>
		<repository>
			<id>MavenCentral</id>
			<name>Maven repository</name>
			<url>https://repo1.maven.org/maven2</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>project.local</id>
			<name>project</name>
			<url>file://${project.basedir}/repo</url>
		</repository>
	</repositories>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<maven.compiler.source>${java.version}</maven.compiler.source>
		<maven.compiler.target>${java.version}</maven.compiler.target>
		<springfox-version>2.7.0</springfox-version>
		<ojdbc.version>********.0</ojdbc.version>
	</properties>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.0.1.RELEASE</version>
		<relativePath /> <!-- lookup parent from repository -->
	</parent>

	<dependencies>
		<dependency>
			<groupId>com.oracle</groupId>
			<artifactId>ojdbc8</artifactId>
			<version>${ojdbc.version}</version>
		</dependency>
		<!-- <dependency> <groupId>com.oracle</groupId> <artifactId>ojdbc8</artifactId> 
			<version>********.0</version> <scope>system</scope> <systemPath>${project.basedir}/lib/ojdbc8-********.0.jar</systemPath> 
			</dependency> -->

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-config</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-web</artifactId>
		</dependency>

		<!-- Hibernate -->
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
			<type>jar</type>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-entitymanager</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-ehcache</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/net.sf.ehcache/ehcache -->
		<dependency>
			<groupId>net.sf.ehcache</groupId>
			<artifactId>ehcache</artifactId>
			<version>2.10.3</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate.javax.persistence</groupId>
			<artifactId>hibernate-jpa-2.1-api</artifactId>
			<version>1.0.2.Final</version>
			<type>jar</type>
		</dependency>

		<dependency>
			<groupId>com.github.joschi.jackson</groupId>
			<artifactId>jackson-datatype-threetenbp</artifactId>
			<version>2.6.4</version>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.2</version>
			<!--$NO-MVN-MAN-VER$ -->
			<type>jar</type>
		</dependency>

		<dependency>
			<groupId>com.sun.jersey</groupId>
			<artifactId>jersey-client</artifactId>
			<version>1.19.1</version>
			<type>jar</type>
		</dependency>

		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.11.3</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
			<version>0.7.0</version>
			<type>jar</type>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.56</version>
			<type>jar</type>
		</dependency>
		<!-- dependencies for log service -->
		<!-- <dependency> <groupId>org.springframework.cloud</groupId> <artifactId>spring-cloud-starter-sleuth</artifactId> 
			<version>1.3.3.RELEASE</version> </dependency> -->
		<!-- <dependency> <groupId>org.springframework.cloud</groupId> <artifactId>spring-cloud-sleuth-zipkin</artifactId> 
			<version>1.3.3.RELEASE</version> </dependency> -->
		<dependency>
			<groupId>net.logstash.logback</groupId>
			<artifactId>logstash-logback-encoder</artifactId>
			<version>5.1</version>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>1.2.3</version>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-core</artifactId>
			<version>1.2.3</version>
		</dependency>
		<!-- dependencies for log service -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>javax.interceptor</groupId>
			<artifactId>javax.interceptor-api</artifactId>
			<version>1.2.2</version>
		</dependency>
		
		<!--SpringFox dependencies -->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>2.8.0</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.8.0</version>
			<scope>compile</scope>
		</dependency>
		
		<!-- Adding datadog span -->
		<dependency>
  			<groupId>io.opentracing.contrib</groupId>
  			<artifactId>opentracing-spring-web-starter</artifactId>
  			<version>4.1.0</version>
		</dependency>
		<dependency>
    		<groupId>com.datadoghq</groupId>
    		<artifactId>dd-trace-api</artifactId>
    		<version>0.78.3</version>
		</dependency>
		
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
			<exclusions>
			    <exclusion>
				 <groupId>io.lettuce</groupId>
				 <artifactId>lettuce-core</artifactId>
			    </exclusion>
		    </exclusions>	
		</dependency>
				
		<!-- redis dependency -->
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>	
		
		<!-- New changes start -->
		<dependency>
        	<groupId>commons-codec</groupId>
        	<artifactId>commons-codec</artifactId>
      	</dependency>
      	
      	<dependency>
            <groupId>com.googlecode.json-simple</groupId>
            <artifactId>json-simple</artifactId>
            <version>1.1</version>
        </dependency>
        
        <dependency>
            <groupId>com.okta.jwt</groupId>
            <artifactId>okta-jwt-verifier</artifactId>
            <version>0.2.0</version>
        </dependency>
        
        <dependency>
            <groupId>com.mashape.unirest</groupId>
            <artifactId>unirest-java</artifactId>
            <version>1.4.9</version>
        </dependency>
        
        <!-- https://mvnrepository.com/artifact/jakarta.validation/jakarta.validation-api -->
    	<dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <version>3.1.0</version>
    	</dependency>
    	
    	<dependency>
    <groupId>org.modelmapper</groupId>
    <artifactId>modelmapper</artifactId>
    <version>3.1.1</version>
</dependency>



        
         <!-- Redisson Starter = Spring Data Redis starter(excluding other clients) and Redisson client -->
	    <!--<dependency>
	        <groupId>org.redisson</groupId>
	        <artifactId>redisson-spring-boot-starter</artifactId>
	        <version>3.23.1</version>
	    </dependency>-->
	    
	    <!-- Bucket4J starter = Bucket4J + JCache -->
	    <!--<dependency>
	        <groupId>com.giffing.bucket4j.spring.boot.starter</groupId>
	        <artifactId>bucket4j-spring-boot-starter</artifactId>
	        <version>0.8.1</version>
	        <exclusions>
				<exclusion>
					<groupId>org.ehcache</groupId>
					<artifactId>ehcache</artifactId>
				</exclusion>
			</exclusions>
	    </dependency>-->
    <!-- New changes end -->
		
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<executions>
					<execution>
						<id>build-zip</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<finalName>service</finalName>
							<descriptors>
								<descriptor>src/assembly/distribution.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>


</project>
